# Web-Nuxt 登录系统实现文档

## 概述

本文档详细说明web-nuxt项目中的登录系统实现，支持**手机号验证码登录**和**第三方登录**（QQ、微信扫码、微信公众号）。

## 前端实现

### 1. 登录页面组件

主要登录页面位于：`web-nuxt/pages/user/login.vue`

#### 1.1 支持的登录方式

- **手机号验证码登录**：手机号 + 短信验证码登录
- **QQ登录**：腾讯QQ第三方登录
- **微信扫码登录**：微信开放平台扫码登录（PC端）
- **微信公众号登录**：微信公众号授权登录（微信浏览器内）

#### 1.2 表单数据结构

```typescript
interface State {
    loading: {
        mobileLogin: boolean
    }
    // 手机号登录表单
    mobileLogin: {
        mobile: string       // 手机号
        captcha: string      // 短信验证码
        captchaId: string    // 验证码ID（用于点击验证码）
        captchaInfo: string  // 点击验证码信息
    }
    userLoginCaptchaSwitch: boolean     // 登录验证码开关
    codeSendCountdown: number          // 验证码倒计时
    sendCaptchaLoading: boolean        // 发送验证码加载状态
    to: string                         // 登录成功后跳转地址
}
```

### 2. 手机号验证码登录实现

#### 2.1 登录表单

```vue
<template>
    <el-form ref="mobileLoginRef" :rules="mobileLoginRules" :model="state.mobileLogin">
        <!-- 手机号输入 -->
        <el-form-item prop="mobile">
            <el-input
                v-model="state.mobileLogin.mobile"
                placeholder="请输入手机号"
                size="large"
            >
                <template #prefix>
                    <Icon name="fa fa-mobile" size="16" color="var(--el-input-icon-color)" />
                </template>
            </el-input>
        </el-form-item>
        
        <!-- 验证码输入 -->
        <el-form-item prop="captcha">
            <el-row class="w100">
                <el-col :span="16">
                    <el-input
                        v-model="state.mobileLogin.captcha"
                        placeholder="请输入验证码"
                        size="large"
                    >
                        <template #prefix>
                            <Icon name="fa fa-shield" size="16" color="var(--el-input-icon-color)" />
                        </template>
                    </el-input>
                </el-col>
                <el-col :span="8" class="captcha-box">
                    <el-button
                        @click="sendMobileCaptchaPre"
                        :loading="state.sendCaptchaLoading"
                        :disabled="state.codeSendCountdown > 0"
                        type="primary"
                        size="large"
                    >
                        {{ state.codeSendCountdown > 0 ? `${state.codeSendCountdown}秒` : '发送验证码' }}
                    </el-button>
                </el-col>
            </el-row>
        </el-form-item>
        
        <!-- 登录按钮 -->
        <el-form-item>
            <el-button
                @click="onMobileLoginSubmit"
                :loading="state.loading.mobileLogin"
                type="primary"
                size="large"
                class="login-btn"
            >
                手机号登录
            </el-button>
        </el-form-item>
    </el-form>
</template>
```

#### 2.2 登录逻辑实现

```typescript
// 表单验证规则
const mobileLoginRules: Partial<Record<string, FormItemRule[]>> = reactive({
    mobile: [
        buildValidatorData({ name: 'required', title: '手机号' }),
        buildValidatorData({ name: 'mobile', title: '手机号' })
    ],
    captcha: [buildValidatorData({ name: 'required', title: '验证码' })],
})

// 发送验证码
const sendMobileCaptchaPre = () => {
    if (state.codeSendCountdown > 0) return
    mobileLoginRef.value?.validateField('mobile').then((valid) => {
        if (!valid) return
        clickCaptcha(state.mobileLogin.captchaId, (captchaInfo: string) => sendMobileCaptcha(captchaInfo))
    })
}

const sendMobileCaptcha = (captchaInfo: string) => {
    state.sendCaptchaLoading = true
    sendSms(state.mobileLogin.mobile, 'user_mobile_login', {
        captchaInfo,
        captchaId: state.mobileLogin.captchaId,
    })
        .then((res) => {
            if (res.code == 1) startTiming(60)
        })
        .finally(() => {
            state.sendCaptchaLoading = false
        })
}

// 手机号登录
const onMobileLoginSubmit = () => {
    mobileLoginRef.value?.validate((valid) => {
        if (!valid) return
        state.loading.mobileLogin = true
        
        checkIn('post', {
            tab: 'mobile_login',
            mobile: state.mobileLogin.mobile,
            captcha: state.mobileLogin.captcha
        })
            .then((res) => {
                if (res.code != 1) return
                userInfo.dataFill(res.data.userInfo, false)
                if (state.to) return (location.href = state.to)
                navigateTo({ path: res.data.routePath })
            })
            .finally(() => {
                state.loading.mobileLogin = false
            })
    })
}

// 验证码倒计时
const startTiming = (seconds: number) => {
    state.codeSendCountdown = seconds
    timer = setInterval(() => {
        state.codeSendCountdown--
        if (state.codeSendCountdown <= 0) {
            endTiming()
        }
    }, 1000)
}
```

### 3. 第三方登录实现

#### 3.1 第三方登录组件

位于：`web-nuxt/composables/mixins/loginFooter.vue`

```vue
<template>
    <div class="login-footer-buried-point">
        <div v-if="!isEmpty(state.oauthNames)" class="third-party">
            <div class="third-party-title">第三方账户登录:</div>
            <!-- QQ登录 -->
            <el-tooltip v-if="state.oauthNames.includes('qq')" effect="dark" content="腾讯QQ登录" placement="top">
                <img @click="onLogin('qq')" class="third-party-logo" src="~assets/images/qq.png" alt="" />
            </el-tooltip>
            <!-- 微信扫码登录（非移动端） -->
            <el-tooltip v-if="state.oauthNames.includes('wechat_scan') && !isMobile()" effect="dark" content="微信扫码登录" placement="top">
                <img @click="onLogin('wechat_scan')" class="third-party-logo" src="~assets/images/wechat_scan.png" alt="" />
            </el-tooltip>
            <!-- 微信公众号登录（微信浏览器内） -->
            <el-tooltip v-if="state.oauthNames.includes('wechat_mp') && isWeixinBrowser()" effect="dark" content="微信登录" placement="top">
                <img @click="onLogin('wechat_mp')" class="third-party-logo" src="~assets/images/wechat_mp.png" alt="" />
            </el-tooltip>
        </div>
    </div>
</template>
```

#### 3.2 第三方登录工具函数

位于：`web-nuxt/utils/modules/oauth.ts`

```typescript
// 检测是否在微信浏览器中
export const isWeixinBrowser = () => {
    const event = useRequestEvent()
    const userAgent = import.meta.client ? navigator.userAgent : event?.node.req.headers['user-agent']
    if (!userAgent) return false
    const ua = userAgent.toLowerCase()
    return /micromessenger/.test(ua) ? true : false
}

// 第三方登录入口
export const onLogin = (name: string, type = 'login') => {
    const userInfo = useUserInfo()
    const runtimeConfig = useRuntimeConfig()
    const url = runtimeConfig.public.apiBaseUrl + `/api/OAuthLogin/index?server=1&name=${name}&type=${type}&token=${userInfo.getToken()}`

    // 直接跳转到第三方授权页面
    window.location.href = url
}
```

## API接口调用

### 1. 手机号验证码登录相关接口

#### 1.1 发送短信验证码

```typescript
// web-nuxt/api/common.ts
export function sendSms(mobile: string, templateCode: string, params = {}) {
    return Http.fetch({
        url: '/api/sms/send',
        method: 'post',
        body: { mobile, templateCode, ...params }
    })
}
```

#### 1.2 手机号登录

```typescript
// web-nuxt/api/user/index.ts
export function checkIn(method: 'get' | 'post', params: object = {}) {
    return Http.fetch({
        url: '/api/user/checkIn',
        data: params,
        method: method,
    })
}
```

### 2. 第三方登录相关接口

#### 2.1 获取可用第三方登录平台

```typescript
// web-nuxt/api/user/oauth.ts
export function oauthList() {
    return Http.fetch({
        url: '/api/OAuthLogin/oauthList',
        method: 'get',
    })
}
```

#### 2.2 第三方登录授权处理

```typescript
export function loginAgent(code: string, state: string) {
    return Http.fetch({
        url: '/api/OAuthLogin/loginAgent',
        method: 'get',
        params: { code, state },
    }, {
        loading: true,
    })
}
```

## 登录流程说明

### 1. 手机号验证码登录流程

1. 用户输入手机号
2. 点击"发送验证码"按钮
3. 系统发送短信验证码到用户手机
4. 用户输入收到的验证码
5. 点击"手机号登录"按钮
6. 系统验证手机号和验证码
7. 验证成功后自动登录并跳转

### 2. 第三方登录流程

#### QQ登录流程
1. 用户点击QQ登录图标
2. 跳转到QQ授权页面
3. 用户授权后回调到系统
4. 系统处理授权信息并登录

#### 微信登录流程
1. 用户点击微信登录图标
2. 跳转到微信授权页面
3. 用户扫码或授权后回调到系统
4. 系统处理授权信息并登录

## 实现要点

### 1. Vue3 Composition API 使用

- 使用 `useTemplateRef` 获取表单引用
- 使用 `reactive` 管理响应式状态
- 使用 `computed` 处理派生状态

### 2. 表单验证

```typescript
const mobileLoginRules: Partial<Record<string, FormItemRule[]>> = reactive({
    mobile: [
        buildValidatorData({ name: 'required', title: '手机号' }),
        buildValidatorData({ name: 'mobile', title: '手机号' })
    ],
    captcha: [buildValidatorData({ name: 'required', title: '验证码' })],
})
```

### 3. 安全特性

- 点击验证码防机器人
- 短信验证码防刷（60秒倒计时）
- 验证码有效期限制
- CSRF防护

### 4. 响应式设计

- 使用Element Plus组件库
- 支持移动端适配
- 动态调整按钮状态

## 注意事项

### 1. 环境配置

确保在 `nuxt.config.ts` 中正确配置：

```typescript
export default defineNuxtConfig({
    runtimeConfig: {
        public: {
            apiBaseUrl: process.env.API_BASE_URL || 'http://localhost'
        }
    }
})
```

### 2. 第三方登录配置

需要在后端配置相应的第三方应用信息：
- QQ互联：app_id、app_secret、callback_url
- 微信开放平台：app_id、app_secret、callback_url

### 3. 短信服务配置

需要配置短信服务商（阿里云、腾讯云等）的相关参数。

### 4. 错误处理

所有接口调用都需要适当的错误处理，使用统一的错误提示组件。 