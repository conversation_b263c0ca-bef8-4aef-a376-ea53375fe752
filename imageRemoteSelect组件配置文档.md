# imageRemoteSelect组件配置文档

## 组件简介

`imageRemoteSelect`组件是一个集成了图片上传和远程下拉选择功能的复合组件，支持自定义字段提取和存储。

## 基本配置

在系统配置的"Input 扩展属性"中，需要配置以下参数：

### 必需参数

```
remoteUrl=admin/Content/index
pk=content.id
field=title
```

### 搜索条件配置（params参数）

使用`params`参数传递搜索条件，配置示例：

```
params={"search":[{"field":"content_type","val":"EXPERT,SERVICE","operator":"IN","render":"tag"}]}
```

### 自定义字段配置

```
customFields=[{"name":"content_type","label":"内容类型","type":"string"},{"name":"title","label":"标题","type":"string"},{"name":"subtitle","label":"副标题","type":"string"}]
```

## 完整配置示例

```
remoteUrl=admin/Content/index
pk=content.id
field=title
params={"search":[{"field":"content_type","val":"EXPERT,SERVICE","operator":"IN","render":"tag"}]}
customFields=[{"name":"content_type","label":"内容类型","type":"string"},{"name":"title","label":"标题","type":"string"},{"name":"subtitle","label":"副标题","type":"string"}]
```

## 参数说明

### remoteUrl
- **说明**：远程数据接口URL
- **示例**：`admin/Content/index`
- **注意**：不需要前导斜杠

### pk
- **说明**：主键字段，用于关联表时需要带表别名
- **示例**：`content.id`（关联表）或 `id`（主表）

### field
- **说明**：显示字段名
- **示例**：`title`

### params参数格式
- **格式**：`params={"search":[{"field":"字段名","val":"值","operator":"操作符"}]}`
- **支持多个条件**：在数组中添加多个对象
- **属性**：
  - `field`：搜索字段名
  - `val`：搜索值
  - `operator`：操作符（=, <>, IN, LIKE等）
  - `render`：渲染方式（tag等）

### customFields
- **说明**：自定义字段配置，JSON格式
- **结构**：`[{"name":"字段名","label":"显示标签","type":"字段类型"}]`

## 搜索条件示例

### 单个条件
```
params={"search":[{"field":"content_type","val":"EXPERT","operator":"="}]}
```

### 多个条件
```
params={"search":[{"field":"content_type","val":"EXPERT,SERVICE","operator":"IN"},{"field":"status","val":"1","operator":"="}]}
```

### 模糊搜索
```
params={"search":[{"field":"title","val":"关键词","operator":"LIKE"}]}
```

## 注意事项

1. **参数格式**：使用`params={"search":[...]}`的JSON格式
2. **字段别名**：关联表查询时，pk需要带表别名（如`content.id`）
3. **操作符**：支持=、<>、IN、LIKE、>、<、>=、<=等
4. **自定义字段**：customFields必须是有效的JSON格式
5. **一行一个**：每个参数配置占一行，不需要引号

## 常见问题

### Q: 为什么远程选择没有数据？
A: 检查params参数配置是否正确，特别是字段名和操作符

### Q: 关联表查询失败？
A: 确保pk字段包含表别名，如`content.id`

### Q: 自定义字段没有保存？
A: 检查customFields的JSON格式是否正确 