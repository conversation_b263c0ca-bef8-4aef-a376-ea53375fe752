<?php
// +----------------------------------------------------------------------
// | 支付配置
// | #alipay# 和 #wechat# 这类标记是因为 key 可能重复，使用标记实现编程式修改配置
// +----------------------------------------------------------------------

return [
    'alipay' => [
        'default' => [
            'app_id'                  => '2021005172681863', #alipay#
            'app_secret_cert'         => 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC30A+L/4OFkF/CwMrKjw8NCkTo+xnP81zKH4ftzUpZ3JTi+RRiUqwcxDK0VgvQtdb5EQAhW0sn+44HUWOvKzgBsU470uwFlPDi1rcFecJ1RSUg61APMAhS+5ppY+X25skSbyJMvcoyuva0azSesYc7etW+gXjuAZ5kj7hf5/1Qe22SrP0Th41nC6iB98+xAWYezWF9VVgzhSoLILI0HsMw9mjVrkKzk5p4mX+qBlfRhlVj3oA6wRV297T89zz3/am50vev7jrB/36MMbm/+/zw5g75Lejj4bEmH+3fPmoyKdL3jpA/hYFKC8RW/+rc8Y1lW5I74OQx9XXuIX5Nrw2xAgMBAAECggEATH44ShFuIUIgp8JlYgBAyjNw0+fTKRfkkHSos4yQGW9TQlVXZ0dyOpJna2a3igZyTLoHbNo6H4xu4Zq8OiRpLUMlBH5VHEmMKUojZhLymqzIs7dZXMinXik4bA1XoPUPzi0TgA4nPA9UJWbLwhKs7VLD6oCk258u1Dh/COJZJ4lQPv1YRb+SH3eBZkEPH8jNT8vRNq1be5Lt6ufwO+FR9IAmhn/IJVvP6v1vWfKDRGktj8W6h4sKOQxXE/OEqYirCytzHrHlTOjqlTTrB3xfR9V1eQy2V8n9bXci5lFnfytKj1a9/Jl/8RDtUVrXhXWE/HJViwSQEt+aXZwgM3BtQQKBgQDyJqd+4rPfZndXp487y3s+XyxlXEOddeTD/zrgx+LmXwdhMeAqsbOpsRY+jNUwhqocbXcfy/ZSl9PzvSOksYMrP6f7AWoehXaoVu4aVwl15GbNbLqHkzSma3E1D9e/8oa12KfHRwE6wVxZ+VPDlVlCP1pY007fgGaxFpkdpA61aQKBgQDCU0XdFCcBaTcIiCi3ofqdEzl11LHafwOlytAFaEbiS0quGslPWWeq9Flb3x6wJj0yCSL+H+lMRX61p3es/bEgvCtvU/RKDdzJHz0RkzYaicED98s3S8+B4zL03t4EXRqF3AwoySNapN/T7LaEcq3LRB+Pzv7kb+4H72XuPo6lCQKBgQCXGSOe+1r95XRGj9nCl//5NW4AeYf/cxcibdqRdGjTEy0JqxOT0HqpQkT9FufYdc8kGfIhNyYm93JcPwNEuzd7/yaUa5eKIAGgnU3JxdGzbKfxOw1Rl7tctOJZF9+qeG0yf1SB9n5/6TP1OKzZ+BFQUzPPBVvL/tUVX2J/qmU7MQKBgGV5tL7o2iGLfviOmayjAkrqEhRs+F8f+pimGxDnzjQ3mwzl4f/0sQT9yngGQHE91rD9Qm+v3FU+6RcVF5KXe9qbSJc/IQ9Ww9em324BELy0+L0Ite7vQLIUIbTreMaQk9wY96SIvFSGSnfRYdIyTH0b2ttu/X3u0T8FjZXmxvh5AoGALeHbtzxbWFetVTq9coKjwfRuUCZKyFScvYwkZ6XV+Abj7L5hRVcrIAW/npRWEH3YkyY46OSSgPvyG2U6m3Ifcw/BkqcH5+vmG3Ci71WzBYlUgINKjrfBjOyC6cKjq13j3OXtFXDTOaHHApAvVn13LdHJAv9Gp8J0CtGr1jYX6Ik=', #alipay#
            'app_public_cert_path'    => 'alipayCert/appCertPublicKey_2021005172681863.crt', #alipay#
            'alipay_public_cert_path' => 'alipayCert/alipayCertPublicKey_RSA2.crt', #alipay#
            'alipay_root_cert_path'   => 'alipayCert/alipayRootCert.crt', #alipay#
            'return_url'              => '', #alipay#
            'notify_url'              => '', #alipay#
            'app_auth_token'          => '', #alipay#
            'service_provider_id'     => '', #alipay#
            'mode'                    => 0, #alipay#
        ]
    ],
    'wechat' => [
        'default' => [
            'mch_id'                  => '**********', #wechat#
            'mch_secret_key_v2'       => '21232f297a57a5a743894a0e4a801fc3', #wechat#
            'mch_secret_key'          => '21232f297a57a5a743894a0e4a801fc3', #wechat#
            'mch_secret_cert'         => 'creat/apiclient_key.pem', #wechat#
            'mch_public_cert_path'    => 'creat/apiclient_cert.pem', #wechat#
            'notify_url'              => 'https://yanzhi.dlxingyun.com/index.php/api/PayNotify/wechat', #wechat#
            'mp_app_id'               => 'wx57f98d3a177df4a0', #wechat#
            'mini_app_id'             => '', #wechat#
            'app_id'                  => '', #wechat#
            'combine_app_id'          => 'wx57f98d3a177df4a0', #wechat#
            'combine_mch_id'          => '', #wechat#
            'sub_mp_app_id'           => '', #wechat#
            'sub_app_id'              => '', #wechat#
            'sub_mini_app_id'         => '', #wechat#
            'sub_mch_id'              => '', #wechat#
            'wechat_public_cert_path' => '[{"key":"PUB_KEY_ID_01**********2025072300291915000801","value":"creat//pub_key.pem"}]', #wechat#
            'mode'                    => 0, #wechat#
        ]
    ],
    'logger' => [
        'enable'   => false, #other#
        'file'     => 'runtime/pay/pay.log', #other#
        'level'    => 'debug', #other#
        'type'     => 'single', #other#
        'max_file' => 30, #other#
    ],
    'http'   => [
        'timeout'         => 10, #other#
        'connect_timeout' => 10, #other#
        // 更多配置项请参考 [Guzzle](https://guzzle-cn.readthedocs.io/zh_CN/latest/request-options.html)
    ],
];