<?php
// +----------------------------------------------------------------------
// | 短信配置
// +----------------------------------------------------------------------

return [
    // 发送短信HTTP请求的超时时间（秒）
    'timeout'  => '5',
    // 默认发送配置
    'default'  => [
        // 网关调用策略，默认：顺序调用
        'strategy' => 'Overtrue\EasySms\Strategies\OrderStrategy',
        // 默认可用的发送网关
        'gateways' => ['qcloud'],
    ],
    // 可用的网关配置
    'gateways' => [
        // 配置项后面的注释用于动态修改配置时定位
        'aliyun'  => [
            'access_key_id'     => '',#aliyun#
            'access_key_secret' => '',#aliyun#
            'sign_name'         => '',#aliyun#
        ],
        'qcloud'  => [
            'sdk_app_id' => '**********',#qcloud#
            'secret_id'  => 'AKIDkMmzznRt8B3vcEqLfokT1MTPDoCNL8lB',#qcloud#
            'secret_key' => 'HOJ3aw65tUu9B0c3IK9oJbmnfJm9NRHw',#qcloud#
            'sign_name'  => '广州研知有术',#qcloud#
        ],
        'qiniu'   => [
            'secret_key' => '',#qiniu#
            'access_key' => '',#qiniu#
        ],
        'yunpian' => [
            'api_key'   => '',#yunpian#
            'signature' => '【默认签名】',#yunpian#
        ],
    ],
];