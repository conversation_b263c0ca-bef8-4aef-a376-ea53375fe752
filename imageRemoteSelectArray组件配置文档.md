# imageRemoteSelectArray 组件配置文档

## 组件概述

`imageRemoteSelectArray` 是一个复合型表单组件，支持多行"图片 + 远程下拉选择"的数组形式输入。每行包含一个图片上传区域和一个远程下拉选择框，并且可以自动从远程数据中提取自定义字段。



## 组件特点

- **多行支持**：可以动态添加/删除多行数据
- **图片上传**：每行支持图片上传和选择
- **远程下拉**：每行支持远程数据选择
- **自动提取**：自动从远程数据中提取自定义字段
- **灵活配置**：支持自定义字段映射

## 配置参数

### 基础配置

在系统设置的"Input 扩展属性"中，需要配置以下 JSON 格式的参数：

```json
{
  "baInputExtend": {
    "remoteUrl": "admin/Content/index",
    "pk": "content.id",
    "field": "title",
    "params": "{\"search\":[{\"field\":\"content_type\",\"val\":\"EXPERT,SERVICE\",\"operator\":\"in\"}]}",
    "customFields": "[{\"name\":\"content_type\",\"label\":\"内容类型\",\"type\":\"string\",\"placeholder\":\"请输入内容类型\"},{\"name\":\"title\",\"label\":\"标题\",\"type\":\"string\",\"placeholder\":\"请输入标题\"},{\"name\":\"subtitle\",\"label\":\"副标题\",\"type\":\"string\",\"placeholder\":\"请输入副标题\"}]"
  }
}
```

### 参数说明

#### 必需参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `remoteUrl` | string | 远程数据接口地址 | `"admin/Content/index"` |
| `pk` | string | 主键字段名（支持表别名） | `"content.id"` |
| `field` | string | 显示字段名 | `"title"` |

#### 可选参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|

| `params` | string | 远程查询参数（JSON字符串） | `"{\"search\":[{\"field\":\"content_type\",\"val\":\"EXPERT,SERVICE\",\"operator\":\"in\"}]}"` |
| `customFields` | string | 自定义字段配置（JSON字符串） | 见下方详细说明 |

### customFields 配置详解

`customFields` 是一个 JSON 字符串，定义了需要从远程数据中自动提取的字段：

```json
[
  {
    "name": "content_type",
    "label": "内容类型",
    "type": "string",
    "placeholder": "请输入内容类型"
  },
  {
    "name": "title",
    "label": "标题", 
    "type": "string",
    "placeholder": "请输入标题"
  },
  {
    "name": "subtitle",
    "label": "副标题",
    "type": "string", 
    "placeholder": "请输入副标题"
  }
]
```

#### customFields 字段说明

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `name` | string | 字段名（必须与远程数据中的字段名一致） | `"content_type"` |
| `label` | string | 字段显示名称 | `"内容类型"` |
| `type` | string | 字段类型（目前支持 string） | `"string"` |
| `placeholder` | string | 占位符文本 | `"请输入内容类型"` |

## 配置步骤

### 1. 在系统设置中添加新配置项

1. **进入系统设置页面**
   - 登录后台管理系统
   - 点击左侧菜单"系统配置"
   - 选择"添加配置项"标签页

2. **点击"添加配置"按钮**
   - 在弹出的"添加配置项"对话框中填写以下信息：

3. **填写配置信息**：

   **基础信息：**
   - **变量分组**：选择合适的分组（如"基础配置"）
   - **变量名**：输入配置标识（如"image_remote_select_array"）
   - **变量标题**：输入显示名称（如"aaaa11"、"测试数组图片2"）
   - **变量类型**：选择"imageRemoteSelectArray"
   - **提示信息**：可选，输入使用说明
   - **验证规则**：可选，根据需要选择
   - **权重**：设置排序权重（如"0"）

   **扩展属性：**
   - **FormItem 扩展属性**：留空或根据需要添加
   - **Input 扩展属性**：填入完整的 JSON 配置（见下方示例）

4. **点击"添加"按钮保存配置**

### 2. 完整配置示例

#### 示例1：专家智库选择

**界面填写步骤：**

1. **变量分组**：选择"基础配置"
2. **变量名**：`expert_image_select_array`
3. **变量标题**：`专家智库图片选择数组`
4. **变量类型**：选择 `imageRemoteSelectArray`
5. **提示信息**：`选择专家智库的图片和相关信息`
6. **验证规则**：留空
7. **FormItem 扩展属性**：留空
8. **Input 扩展属性**：复制粘贴以下内容：

```
remoteUrl=admin/Content/index
pk=content.id
field=title
params={"search":[{"field":"content_type","val":"EXPERT","operator":"in"}]}
customFields=[{"name":"content_type","label":"内容类型","type":"string"},{"name":"title","label":"标题","type":"string"},{"name":"subtitle","label":"副标题","type":"string"}]
```

9. **权重**：`0`

```json
{
  "baInputExtend": {
    "remoteUrl": "admin/Content/index",
    "pk": "content.id", 
    "field": "title",
    "params": "{\"search\":[{\"field\":\"content_type\",\"val\":\"EXPERT\",\"operator\":\"in\"}]}",
    "customFields": "[{\"name\":\"content_type\",\"label\":\"内容类型\",\"type\":\"string\"},{\"name\":\"title\",\"label\":\"标题\",\"type\":\"string\"},{\"name\":\"subtitle\",\"label\":\"副标题\",\"type\":\"string\"}]"
  }
}
```

#### 示例2：学术科研服务选择

**界面填写步骤：**

1. **变量分组**：选择"基础配置"
2. **变量名**：`service_image_select_array`
3. **变量标题**：`学术科研服务图片选择数组`
4. **变量类型**：选择 `imageRemoteSelectArray`
5. **提示信息**：`选择学术科研服务的图片和相关信息`
6. **验证规则**：留空
7. **FormItem 扩展属性**：留空
8. **Input 扩展属性**：复制粘贴以下内容：

```
remoteUrl=admin/Content/index
pk=content.id
field=title
params={"search":[{"field":"content_type","val":"SERVICE","operator":"in"}]}
customFields=[{"name":"content_type","label":"内容类型","type":"string"},{"name":"title","label":"标题","type":"string"},{"name":"subtitle","label":"副标题","type":"string"}]
```

9. **权重**：`0`

#### 示例3：混合类型选择

**界面填写步骤：**

1. **变量分组**：选择"基础配置"
2. **变量名**：`mixed_image_select_array`
3. **变量标题**：`混合类型图片选择数组`
4. **变量类型**：选择 `imageRemoteSelectArray`
5. **提示信息**：`选择专家智库或学术科研服务的图片和相关信息`
6. **验证规则**：留空
7. **FormItem 扩展属性**：留空
8. **Input 扩展属性**：复制粘贴以下内容：

```
remoteUrl=admin/Content/index
pk=content.id
field=title
params={"search":[{"field":"content_type","val":"EXPERT,SERVICE","operator":"in"}]}
customFields=[{"name":"content_type","label":"内容类型","type":"string"},{"name":"title","label":"标题","type":"string"},{"name":"subtitle","label":"副标题","type":"string"}]
```

9. **权重**：`0`

## 数据存储格式

组件会将数据以 JSON 字符串格式存储，每行包含以下字段：

```json
[
  {
    "image": "/storage/default/20250731/example.png",
    "remoteValue": "123",
    "content_type": "EXPERT",
    "title": "专家姓名",
    "subtitle": "专家副标题"
  },
  {
    "image": "/storage/default/20250731/example2.png", 
    "remoteValue": "456",
    "content_type": "SERVICE",
    "title": "服务名称",
    "subtitle": "服务副标题"
  }
]
```

## 注意事项

1. **HTML 编码**：配置中的 JSON 字符串会自动进行 HTML 编码存储，组件会自动解码
2. **字段映射**：`customFields` 中的 `name` 必须与远程数据接口返回的字段名完全一致
3. **主键设置**：使用表别名时（如 `content.id`），确保后端查询支持该格式
4. **参数格式**：`params` 中的搜索条件需要符合后端接口的要求
5. **数据提取**：自定义字段的值会在用户选择远程选项时自动提取，无需手动输入

## 常见问题

### Q: 为什么自定义字段没有自动填充？
A: 检查 `customFields` 中的 `name` 是否与远程数据中的字段名一致，以及远程数据是否包含这些字段。

### Q: 远程下拉没有数据？
A: 检查 `remoteUrl` 是否正确，`params` 中的搜索条件是否有效。

### Q: 主键冲突错误？
A: 确保 `pk` 参数正确设置，如果使用表别名，确保后端支持该格式。

### Q: 图片上传失败？
A: 检查文件上传配置和存储路径是否正确。

## 技术支持

如有问题，请检查：
1. 浏览器控制台是否有错误信息
2. 网络请求是否正常
3. 后端接口是否返回正确的数据格式
4. 配置参数是否正确 