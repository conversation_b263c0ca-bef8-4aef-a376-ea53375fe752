# 🚀 支付插件配置指南

## 📋 概述

本指南详细介绍了如何配置和使用基于 `yansongda/pay` 的支付插件，实现微信支付二维码扫码支付功能。

## 🔧 配置步骤

### 1. 微信支付配置

#### 必需配置项

在后台 **支付配置** 页面中配置以下关键信息：

**基础配置：**
- ✅ `微信商户号` - 已配置：`1372968002`
- ✅ `v3商户秘钥` - 已配置
- ✅ `商户私钥` - 已配置：`creat/apiclient_key.pem`
- ✅ `商户公钥证书` - 已配置：`creat/apiclient_cert.pem`
- ✅ `回调通知地址` - 已配置：`https://yanzhi.dlxingyun.com/index.php/api/PayNotify/wechat`

**关键缺失配置：**
- ❌ `App的AppID` - **需要配置**（扫码支付必需）

**重要说明：**
- 🔧 **沙箱模式** - 调用真实的微信支付接口（沙箱环境），但不涉及真实资金
- ⚠️ **配置检查** - 系统会自动检查必要配置，缺少配置时会明确提示

#### 获取 AppID 的方式

**方式一：微信开放平台（推荐）**
1. 前往 [微信开放平台](https://open.weixin.qq.com/)
2. 注册开发者账号
3. 创建网站应用
4. 获取 `AppID` 和 `AppSecret`

**方式二：公众号平台**
1. 前往 [微信公众平台](https://mp.weixin.qq.com/)
2. 注册服务号
3. 开通微信支付功能
4. 获取 `AppID`

### 2. 沙箱模式配置

当前配置状态：
- **支付模式**: `沙箱模式 (1)`
- **作用**: 测试环境，不涉及真实资金

**模式说明：**
- `0` - 正常模式（生产环境）
- `1` - 沙箱模式（测试环境）
- `2` - 服务商模式

### 3. 证书配置

确保以下证书文件已正确上传：

```
/www/wwwroot/yanzhi.dlxingyun.com/creat/
├── apiclient_cert.pem    # 商户公钥证书
├── apiclient_key.pem     # 商户私钥
└── wechatpay_xxx.pem     # 微信平台证书（可选）
```

## 🎯 使用方法

### 1. 创建订单并生成二维码

**测试接口：**
```http
POST /api/User/createOrderTest
Content-Type: application/json

{
    "service_id": 1,
    "quantity": 1,
    "contact_mobile": "13800138000",
    "contact_email": "<EMAIL>",
    "user_remark": "测试订单"
}
```

**正式接口：**
```http
POST /api/User/createOrderAndPay
Content-Type: application/json

{
    "service_id": 1,
    "quantity": 1,
    "contact_mobile": "13800138000",
    "contact_email": "<EMAIL>",
    "user_remark": "正式订单"
}
```

### 2. 支付流程

#### 当前状态（沙箱模式）
1. **创建订单** → 生成订单号
2. **返回模拟二维码** → `weixin://wxpay/bizpayurl?pr=sandbox{订单号}`
3. **前端显示二维码** → 用户扫码（模拟）
4. **轮询支付状态** → 检查支付结果

#### 正式环境配置后
1. **创建订单** → 生成订单号
2. **调用微信接口** → 获取真实二维码
3. **前端显示二维码** → 用户扫码支付
4. **微信回调** → 自动更新订单状态

### 3. 支付状态查询

```http
POST /api/User/checkPayStatus
Content-Type: application/json

{
    "order_id": 35
}
```

### 4. 检查支付配置

```http
POST /api/User/checkPayConfig
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN
```

**返回示例：**
```json
{
  "code": 1,
  "msg": "支付配置检查完成",
  "data": {
    "payment_mode": "沙箱模式",
    "completion_rate": "83.3%",
    "config_status": {
      "商户号(mch_id)": {
        "configured": true,
        "value": "1372968002"
      },
      "App的AppID(app_id)": {
        "configured": false,
        "value": "未配置 - 扫码支付必需"
      }
    },
    "missing_configs": ["App的AppID(app_id)"],
    "is_ready": false,
    "summary": "❌ 配置不完整，缺少 1 项配置"
  }
}
```

## 🔄 支付状态流转

```
创建订单 → 待支付 → 支付中 → 支付成功
   ↓         ↓        ↓        ↓
pending → unpaid → paying → paid
```

## 🧪 测试方法

### 1. 沙箱模式测试（当前）

```bash
# 创建测试订单
curl -X POST "https://yanzhi.dlxingyun.com/api/User/createOrderTest" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "service_id": 1,
    "quantity": 1,
    "contact_mobile": "13800138000"
  }'
```

**预期结果：**
```json
{
  "code": 1,
  "msg": "订单创建成功（沙箱模式）",
  "data": {
    "order_id": 35,
    "order_no": "ORD202507111811148392",
    "total_amount": 208,
    "qr_code": "weixin://wxpay/bizpayurl?pr=sandboxORD202507111811148392",
    "expire_time": "2025-07-11 19:11:14",
    "note": "当前为沙箱模式，配置AppID后启用真实支付"
  }
}
```

### 2. 正式环境测试

配置 `AppID` 后，接口将返回真实的微信支付二维码。

## 📱 前端集成

### 1. 二维码生成

```javascript
// 使用 qrcode.js 生成二维码
import QRCode from 'qrcode'

async function generateQRCode(codeUrl) {
    const canvas = document.getElementById('qrcode-canvas')
    await QRCode.toCanvas(canvas, codeUrl, {
        width: 256,
        margin: 2,
        color: {
            dark: '#000000',
            light: '#FFFFFF'
        }
    })
}
```

### 2. 支付状态轮询

```javascript
// 轮询支付状态
function pollPaymentStatus(orderId) {
    const timer = setInterval(async () => {
        try {
            const response = await fetch('/api/User/checkPayStatus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ order_id: orderId })
            })
            
            const result = await response.json()
            
            if (result.data.payment_status === 'paid') {
                clearInterval(timer)
                // 支付成功处理
                onPaymentSuccess(result.data)
            }
        } catch (error) {
            console.error('查询支付状态失败:', error)
        }
    }, 3000) // 每3秒查询一次
}
```

## 🔍 日志调试

### 1. 支付日志位置

```
/www/wwwroot/yanzhi.dlxingyun.com/runtime/pay/pay.log
```

### 2. 关键日志信息

```log
=== 沙箱模式支付 ===
订单号: ORD202507111811148392
金额: 208
模式: 沙箱模式

=== 微信支付成功 ===
订单号: ORD202507111811148392
二维码: weixin://wxpay/bizpayurl?pr=NwnnwlVjjjvPdUA_xxxxx
```

## ⚠️ 注意事项

### 1. 证书安全

- 确保证书文件权限正确（600）
- 不要将证书文件提交到版本控制系统
- 定期更新证书

### 2. 回调处理

- 确保回调URL可以正常访问
- 验证回调数据的真实性
- 处理重复回调的幂等性

### 3. 错误处理

- 记录详细的错误日志
- 提供用户友好的错误提示
- 设置合理的超时时间

## 🚀 上线准备

### 1. 配置检查清单

- [ ] 微信商户号已配置
- [ ] AppID 已配置
- [ ] 证书文件已上传
- [ ] 回调地址已配置
- [ ] 支付模式设置为正常模式（0）

### 2. 测试检查清单

- [ ] 订单创建功能正常
- [ ] 二维码生成正常
- [ ] 支付回调处理正常
- [ ] 订单状态更新正常
- [ ] 错误处理机制正常

### 3. 安全检查清单

- [ ] 证书文件权限正确
- [ ] 敏感信息已加密
- [ ] 回调验签正常
- [ ] 日志记录完整

## 📞 技术支持

如遇问题，请查看：
1. 日志文件：`/runtime/pay/pay.log`
2. 错误日志：`/runtime/log/`
3. 微信支付文档：https://pay.weixin.qq.com/wiki/doc/api/
4. yansongda/pay 文档：https://pay.yansongda.cn/

---

**当前状态总结：**
- ✅ 基础配置完成
- ✅ 沙箱模式可用
- ❌ 缺少 AppID 配置
- �� 待配置 AppID 启用真实支付 