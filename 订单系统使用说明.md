# 订单系统使用说明

## 概述

基于您提供的订单页面和BuildAdmin支付模块，设计了完整的订单管理系统，支持学术服务的在线购买、支付和管理。

## 数据库表结构

### 1. 主要表结构

- **ba_order** - 订单主表，存储订单基本信息
- **ba_order_status_log** - 订单状态变更记录表
- **ba_order_item** - 订单项目明细表
- **ba_order_review** - 订单评价表
- **ba_order_refund** - 售后退款申请表

### 2. 关键字段说明

#### 订单状态字段
- `status`: 订单主状态（pending, paid, in_progress, completed, cancelled, refunded）
- `payment_status`: 支付状态（unpaid, paying, paid, failed, refunded）
- `service_status`: 服务状态（not_started, in_progress, completed, cancelled）

#### 支付相关字段（配合BuildAdmin支付模块）
- `out_trade_no`: 商户订单号（传给支付模块）
- `trade_no`: 第三方支付交易号
- `payment_data`: 支付回调数据（JSON格式）

## API接口

### 1. 订单流程接口

#### 创建订单
```
POST /api/Order/create
参数：content_id, quantity, remark
```

#### 支付订单
```
POST /api/Order/pay
参数：id, payment_method
```

#### 取消订单
```
POST /api/Order/cancel
参数：id, reason
```

#### 订单列表
```
GET /api/Order/list
参数：page, limit, status
```

#### 订单详情
```
GET /api/Order/detail
参数：id
```

### 2. 支付回调处理
```
POST/GET /api/Order/payNotify
自动处理支付成功回调，更新订单状态
```

## 与BuildAdmin支付模块集成

### 1. 支付参数配置

系统会自动生成符合BuildAdmin支付模块要求的参数：

```php
$payOrder = [
    'out_trade_no' => $order['out_trade_no'],  // 商户订单号
    'description' => $order['title'],          // 订单描述
    'amount' => [
        'total' => $amount,                    // 金额（分）
        'currency' => 'CNY'
    ],
    'notify_url' => request()->domain() . '/api/Order/payNotify'
];
```

### 2. 支付方式支持

- **微信扫码支付**: `Pay::wechat($config)->scan($payOrder)`
- **支付宝网页支付**: `Pay::alipay($config)->web($payOrder)`

### 3. 支付状态流转

1. **创建订单** → status: `pending`, payment_status: `unpaid`
2. **发起支付** → payment_status: `paying`
3. **支付成功** → status: `paid`, payment_status: `paid`
4. **服务开始** → service_status: `in_progress`
5. **服务完成** → status: `completed`, service_status: `completed`

## 订单页面设计要点

### 1. 订单状态显示

根据您的订单页面截图，系统支持以下状态显示：

- **待付款** - 显示支付按钮，可取消订单
- **待完成** - 显示服务进度，联系客服
- **已完成** - 显示评价按钮，申请售后
- **售后** - 显示退款进度

### 2. 客服信息集成

订单表中预设了客服字段：
- `customer_service`: "肖肖@Matrix/毛俊@青"
- `customer_qq`: "1668325326"
- `customer_wechat`: 客服微信号

### 3. 价格显示

- 原价: `original_price`
- 优惠: `discount_amount`
- 实付: `total_amount`

## 使用流程

### 1. 部署步骤

1. **执行SQL文件**
   ```sql
   source database/migrations/create_order_tables.sql
   ```

2. **部署API控制器**
   ```bash
   cp app/api/controller/Order.php 到对应目录
   ```

3. **配置支付模块**
   - 确保BuildAdmin支付模块已安装
   - 配置微信、支付宝支付参数

### 2. 前端集成

#### APIPost测试
- 导入 `apipost-order-config.json` 配置
- 先调用登录接口获取token
- 使用token测试各个订单接口

#### 前端页面开发
```javascript
// 创建订单
const createOrder = async (contentId, quantity) => {
    const response = await fetch('/api/Order/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'ba-user-token': userToken
        },
        body: JSON.stringify({
            content_id: contentId,
            quantity: quantity
        })
    });
    return response.json();
};

// 发起支付
const payOrder = async (orderId, paymentMethod) => {
    const response = await fetch('/api/Order/pay', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'ba-user-token': userToken
        },
        body: JSON.stringify({
            id: orderId,
            payment_method: paymentMethod
        })
    });
    return response.json();
};
```

## 扩展功能

### 1. 优惠券系统
可在创建订单时计算 `discount_amount`，支持：
- 满减优惠券
- 折扣优惠券
- 新用户优惠

### 2. 积分抵扣
可扩展支付方式，支持积分部分抵扣

### 3. 分期付款
可扩展订单项目，支持服务分期付款

### 4. 自动分配专家
可在支付成功后自动分配合适的专家

## 注意事项

### 1. 安全考虑
- 订单金额计算必须在服务端进行
- 支付回调需要验签确保安全
- 用户只能操作自己的订单

### 2. 性能优化
- 订单列表添加索引优化查询
- 支付回调幂等性处理
- 大量订单时考虑分库分表

### 3. 监控告警
- 支付成功率监控
- 订单异常状态告警
- 客服响应时间统计

这个订单系统完全集成了您现有的内容管理系统和BuildAdmin支付模块，可以无缝支持学术服务的在线销售。 