import createAxios from '/@/utils/axios'

export const cnfigControllerUrl = '/admin/oauth.Config/'

export function index() {
    return createAxios({
        url: cnfigControllerUrl + 'index',
        method: 'get',
    })
}

export function saveConfig(name: string, data: anyObj) {
    return createAxios(
        {
            url: cnfigControllerUrl + 'saveConfig',
            method: 'post',
            params: {
                name: name,
            },
            data: data,
        },
        {
            showSuccessMessage: true,
        }
    )
}
