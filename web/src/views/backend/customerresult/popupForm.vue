<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '' : 'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem
                        :label="t('customerresult.journal_name')"
                        type="string"
                        v-model="baTable.form.items!.journal_name"
                        prop="journal_name"
                        :placeholder="t('Please input field', { field: t('customerresult.journal_name') })"
                    />
                    <FormItem
                        :label="t('customerresult.impact_factor')"
                        type="number"
                        v-model="baTable.form.items!.impact_factor"
                        prop="impact_factor"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('customerresult.impact_factor') })"
                    />
                    <FormItem
                        :label="t('customerresult.author_info')"
                        type="string"
                        v-model="baTable.form.items!.author_info"
                        prop="author_info"
                        :placeholder="t('Please input field', { field: t('customerresult.author_info') })"
                    />
                    <FormItem
                        :label="t('customerresult.paper_link')"
                        type="string"
                        v-model="baTable.form.items!.paper_link"
                        prop="paper_link"
                        :placeholder="t('Please input field', { field: t('customerresult.paper_link') })"
                    />
                    <FormItem
                        :label="t('customerresult.service_project')"
                        type="string"
                        v-model="baTable.form.items!.service_project"
                        prop="service_project"
                        :placeholder="t('Please input field', { field: t('customerresult.service_project') })"
                    />
                    <FormItem :label="t('customerresult.cover_image')" type="image" v-model="baTable.form.items!.cover_image" prop="cover_image" />
                    <FormItem
                        :label="t('customerresult.description')"
                        type="textarea"
                        v-model="baTable.form.items!.description"
                        prop="description"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('customerresult.description') })"
                    />
                    <FormItem
                        :label="t('customerresult.publication_date')"
                        type="date"
                        v-model="baTable.form.items!.publication_date"
                        prop="publication_date"
                        :placeholder="t('Please select field', { field: t('customerresult.publication_date') })"
                    />
                    <FormItem
                        :label="t('customerresult.university')"
                        type="string"
                        v-model="baTable.form.items!.university"
                        prop="university"
                        :placeholder="t('Please input field', { field: t('customerresult.university') })"
                    />
                    <FormItem
                        :label="t('customerresult.author_name')"
                        type="string"
                        v-model="baTable.form.items!.author_name"
                        prop="author_name"
                        :placeholder="t('Please input field', { field: t('customerresult.author_name') })"
                    />
                    <FormItem
                        :label="t('customerresult.result_type')"
                        type="string"
                        v-model="baTable.form.items!.result_type"
                        prop="result_type"
                        :placeholder="t('Please input field', { field: t('customerresult.result_type') })"
                    />
                    <FormItem
                        :label="t('customerresult.is_featured')"
                        type="number"
                        v-model="baTable.form.items!.is_featured"
                        prop="is_featured"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('customerresult.is_featured') })"
                    />
                    <FormItem
                        :label="t('customerresult.sort')"
                        type="number"
                        v-model="baTable.form.items!.sort"
                        prop="sort"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('customerresult.sort') })"
                    />
                    <FormItem
                        :label="t('customerresult.status')"
                        type="radio"
                        v-model="baTable.form.items!.status"
                        prop="status"
                        :input-attr="{ content: { '0': 'status 0', '1': 'status 1' } }"
                        :placeholder="t('Please select field', { field: t('customerresult.status') })"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormItemRule } from 'element-plus'
import { inject, reactive, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { buildValidatorData } from '/@/utils/validate'

const config = useConfig()
const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass

const { t } = useI18n()

const rules: Partial<Record<string, FormItemRule[]>> = reactive({
    impact_factor: [buildValidatorData({ name: 'number', title: t('customerresult.impact_factor') })],
    publication_date: [buildValidatorData({ name: 'date', title: t('customerresult.publication_date') })],
    is_featured: [buildValidatorData({ name: 'number', title: t('customerresult.is_featured') })],
    sort: [buildValidatorData({ name: 'number', title: t('customerresult.sort') })],
    create_time: [buildValidatorData({ name: 'date', title: t('customerresult.create_time') })],
    update_time: [buildValidatorData({ name: 'date', title: t('customerresult.update_time') })],
})
</script>

<style scoped lang="scss"></style>
