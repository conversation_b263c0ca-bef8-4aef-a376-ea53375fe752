<template>
    <div class="default-main">
        <el-row v-loading="state.loading">
            <el-col class="xs-mb-20" :xs="24" :sm="16">
                <el-form
                    v-if="!state.loading"
                    ref="formRef"
                    @keyup.enter="onSubmit(formRef)"
                    :model="state.platform[state.activeTab]"
                    :rules="state.rules"
                    :label-position="'top'"
                >
                    <el-tabs v-model="state.activeTab" type="border-card">
                        <el-tab-pane
                            class="config-tab-pane"
                            v-for="(item, key) in state.platform"
                            :key="key"
                            :name="key"
                            :label="t('oauth.config.' + key)"
                        >
                            <div v-for="(cItem, cKey) in item" :key="cKey">
                                <FormItem
                                    v-if="state.activeTab == key"
                                    type="string"
                                    :label="t('oauth.config.' + cKey)"
                                    v-model="state.platform[key][cKey]"
                                    :prop="cKey.toString()"
                                    :placeholder="
                                        te('oauth.index.' + key + '/placeholder/' + cKey)
                                            ? t('oauth.config.' + key + '/placeholder/' + cKey)
                                            : t('oauth.config.default/placeholder/' + cKey)
                                    "
                                />
                            </div>
                        </el-tab-pane>
                        <el-button v-blur :loading="state.submitLoading" @click="onSubmit(formRef)" type="primary">
                            {{ t('Save') }}
                        </el-button>
                    </el-tabs>
                </el-form>
            </el-col>
        </el-row>
    </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { ref, reactive, onMounted } from 'vue'
import { FormInstance } from 'element-plus'
import { index, saveConfig } from '/@/api/backend/oauth/index'
import FormItem from '/@/components/formItem/index.vue'
import { buildValidatorData } from '/@/utils/validate'

const { t, te } = useI18n()
const formRef = ref<FormInstance>()
const state: {
    loading: boolean
    form: anyObj
    rules: anyObj
    platform: anyObj
    activeTab: string
    submitLoading: boolean
} = reactive({
    loading: false,
    form: {},
    rules: {
        app_id: [buildValidatorData({ name: 'required', title: t('oauth.config.app_id') })],
        app_secret: [buildValidatorData({ name: 'required', title: t('oauth.config.app_secret') })],
        callback_url: [buildValidatorData({ name: 'required', title: t('oauth.config.callback_url') })],
    },
    platform: {},
    activeTab: 'qq',
    submitLoading: false,
})

const onSubmit = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate((res) => {
        if (res) {
            state.submitLoading = true
            saveConfig(state.activeTab, state.platform[state.activeTab]).finally(() => {
                state.submitLoading = false
            })
        }
    })
}

onMounted(() => {
    state.loading = true
    index()
        .then((res) => {
            state.platform = res.data.config
        })
        .finally(() => {
            state.loading = false
        })
})
</script>

<style scoped lang="scss">
.send-test-mail {
    padding-bottom: 20px;
}
.el-tabs--border-card {
    border: none;
    box-shadow: var(--el-box-shadow-light);
    border-radius: var(--el-border-radius-base);
}
.el-tabs--border-card :deep(.el-tabs__header) {
    background-color: var(--ba-bg-color);
    border-bottom: none;
    border-top-left-radius: var(--el-border-radius-base);
    border-top-right-radius: var(--el-border-radius-base);
}
.el-tabs--border-card :deep(.el-tabs__item.is-active) {
    border: 1px solid transparent;
}
.el-tabs--border-card :deep(.el-tabs__nav-wrap) {
    border-top-left-radius: var(--el-border-radius-base);
    border-top-right-radius: var(--el-border-radius-base);
}
.el-card :deep(.el-card__header) {
    height: 40px;
    padding: 0;
    line-height: 40px;
    border: none;
    padding-left: 20px;
    background-color: var(--ba-bg-color);
}
.config-tab-pane {
    padding: 5px;
}
@media screen and (max-width: 768px) {
    .xs-mb-20 {
        margin-bottom: 20px;
    }
}
</style>
