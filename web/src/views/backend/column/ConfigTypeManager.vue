<template>
    <div class="config-type-manager">
        <div class="manager-header">
            <h3>{{ title }}</h3>
            <el-button type="primary" @click="handleAdd">
                <Icon name="fa fa-plus" />
                添加配置项
            </el-button>
        </div>

        <div class="config-list">
            <el-table :data="localData" stripe style="width: 100%">
                <el-table-column prop="key" label="配置键" width="200">
                    <template #default="{ row, $index }">
                        <el-input
                            v-if="row.editing"
                            v-model="row.key"
                            placeholder="请输入配置键"
                            @blur="handleSave($index)"
                            @keyup.enter="handleSave($index)"
                        />
                        <span v-else>{{ row.key }}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="value" label="配置值">
                    <template #default="{ row, $index }">
                        <el-input
                            v-if="row.editing"
                            v-model="row.value"
                            placeholder="请输入配置值"
                            @blur="handleSave($index)"
                            @keyup.enter="handleSave($index)"
                        />
                        <span v-else>{{ row.value }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="操作" width="150" align="center">
                    <template #default="{ row, $index }">
                        <div class="action-buttons">
                            <el-button v-if="row.editing" type="primary" size="small" @click="handleSave($index)">
                                <Icon name="fa fa-check" />
                                保存
                            </el-button>
                            <el-button v-else type="primary" size="small" @click="handleEdit($index)">
                                <Icon name="fa fa-edit" />
                                编辑
                            </el-button>
                            <el-button 
                                v-if="row.editing" 
                                type="default" 
                                size="small" 
                                @click="handleCancelEdit($index)"
                            >
                                <Icon name="fa fa-times" />
                                取消
                            </el-button>
                            <el-button 
                                v-else 
                                type="danger" 
                                size="small" 
                                @click="handleDelete($index)"
                            >
                                <Icon name="fa fa-trash" />
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <div v-if="localData.length === 0" class="empty-state">
                <Icon name="fa fa-inbox" />
                <p>暂无配置项，点击上方按钮添加</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Icon from '/@/components/icon/index.vue'

interface ConfigItem {
    key: string
    value: string
    editing?: boolean
    originalKey?: string
    originalValue?: string
}

interface Props {
    title: string
    data: Array<{ key: string; value: string }>
    configKey: string
}

const props = defineProps<Props>()
const emit = defineEmits(['update'])

// 本地数据副本
const localData = ref<ConfigItem[]>([])

// 监听数据变化
watch(
    () => props.data,
    (newData) => {
        localData.value = newData.map(item => ({
            ...item,
            editing: false
        }))
    },
    { immediate: true, deep: true }
)

// 触发更新
const emitUpdate = () => {
    const cleanData = localData.value.map(item => ({
        key: item.key,
        value: item.value
    }))
    emit('update', props.configKey, cleanData)
}

// 添加配置项
const handleAdd = () => {
    const newItem: ConfigItem = {
        key: '',
        value: '',
        editing: true
    }
    localData.value.push(newItem)
}

// 编辑配置项
const handleEdit = (index: number) => {
    const item = localData.value[index]
    // 保存原始值
    item.originalKey = item.key
    item.originalValue = item.value
    item.editing = true
}

// 保存配置项
const handleSave = (index: number) => {
    const item = localData.value[index]
    
    // 验证输入
    if (!item.key.trim()) {
        ElMessage.error('配置键不能为空')
        return
    }
    
    if (!item.value.trim()) {
        ElMessage.error('配置值不能为空')
        return
    }
    
    // 检查重复键
    const duplicateIndex = localData.value.findIndex((other, i) => 
        i !== index && other.key === item.key
    )
    
    if (duplicateIndex !== -1) {
        ElMessage.error('配置键已存在')
        return
    }
    
    // 保存成功
    item.editing = false
    delete item.originalKey
    delete item.originalValue
    
    emitUpdate()
    ElMessage.success('保存成功')
}

// 取消编辑
const handleCancelEdit = (index: number) => {
    const item = localData.value[index]
    
    if (item.originalKey !== undefined) {
        // 恢复原始值
        item.key = item.originalKey
        item.value = item.originalValue || ''
        item.editing = false
        delete item.originalKey
        delete item.originalValue
    } else {
        // 新添加的项，直接删除
        localData.value.splice(index, 1)
    }
}

// 删除配置项
const handleDelete = async (index: number) => {
    const item = localData.value[index]
    
    try {
        await ElMessageBox.confirm(
            `确定要删除配置项 "${item.key}: ${item.value}" 吗？`,
            '删除确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
        
        localData.value.splice(index, 1)
        emitUpdate()
        ElMessage.success('删除成功')
    } catch {
        // 用户取消删除
    }
}
</script>

<style scoped lang="scss">
.config-type-manager {
    .manager-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
            margin: 0;
            color: #333;
        }
    }
    
    .config-list {
        .action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #999;
            
            .icon {
                font-size: 48px;
                margin-bottom: 16px;
                display: block;
            }
            
            p {
                margin: 0;
                font-size: 14px;
            }
        }
    }
}
</style> 