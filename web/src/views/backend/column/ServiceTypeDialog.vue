<template>
    <el-dialog v-model="dialogVisible" title="服务类型管理" width="1000px" :close-on-click-modal="false">
        <div class="service-type-manage">
            <el-tabs v-model="activeTab" type="card">
                <!-- 期刊类型管理 -->
                <el-tab-pane label="期刊类型" name="journal_type">
                    <ConfigTypeManager
                        title="期刊类型配置"
                        :data="configData.journal_type"
                        config-key="journal_type"
                        @update="handleConfigUpdate"
                    />
                </el-tab-pane>

                <!-- 数据库类型管理 -->
                <el-tab-pane label="数据库类型" name="journal_datatype">
                    <ConfigTypeManager
                        title="数据库类型配置"
                        :data="configData.journal_datatype"
                        config-key="journal_datatype"
                        @update="handleConfigUpdate"
                    />
                </el-tab-pane>

                <!-- 中科院分区管理 -->
                <el-tab-pane label="中科院分区" name="journal_zky">
                    <ConfigTypeManager
                        title="中科院分区配置"
                        :data="configData.journal_zky"
                        config-key="journal_zky"
                        @update="handleConfigUpdate"
                    />
                </el-tab-pane>

                <!-- JCR分区管理 -->
                <el-tab-pane label="JCR分区" name="journal_jcr">
                    <ConfigTypeManager
                        title="JCR分区配置"
                        :data="configData.journal_jcr"
                        config-key="journal_jcr"
                        @update="handleConfigUpdate"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
                <el-button type="primary" :loading="loading" @click="handleSave">保存所有配置</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import createAxios from '/@/utils/axios'
import ConfigTypeManager from './ConfigTypeManager.vue'

interface Props {
    modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'refresh'])

const dialogVisible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const activeTab = ref('journal_type')

// 配置数据
const configData = ref({
    journal_type: [] as Array<{ key: string; value: string }>,
    journal_datatype: [] as Array<{ key: string; value: string }>,
    journal_zky: [] as Array<{ key: string; value: string }>,
    journal_jcr: [] as Array<{ key: string; value: string }>,
})

// 原始配置数据（用于比较变化）
const originalConfigData = ref({
    journal_type: [] as Array<{ key: string; value: string }>,
    journal_datatype: [] as Array<{ key: string; value: string }>,
    journal_zky: [] as Array<{ key: string; value: string }>,
    journal_jcr: [] as Array<{ key: string; value: string }>,
})

// 加载配置数据
const loadConfigData = async () => {
    try {
        loading.value = true
        const res = await createAxios({
            url: '/admin/Content/getJournalConfig',
            method: 'get',
        })

        if (res.code === 1 && res.data) {
            configData.value = { ...res.data }
            originalConfigData.value = JSON.parse(JSON.stringify(res.data))
        }
    } catch (error) {
        console.error('加载配置失败:', error)
        ElMessage.error('加载配置失败')
    } finally {
        loading.value = false
    }
}

// 处理配置更新
const handleConfigUpdate = (configKey: string, data: Array<{ key: string; value: string }>) => {
    configData.value[configKey as keyof typeof configData.value] = data
}

// 保存配置
const handleSave = async () => {
    try {
        loading.value = true
        
        // 检查是否有变化
        const hasChanges = JSON.stringify(configData.value) !== JSON.stringify(originalConfigData.value)
        if (!hasChanges) {
            ElMessage.info('没有检测到配置变化')
            return
        }

        const res = await createAxios({
            url: '/admin/Content/updateJournalConfig',
            method: 'post',
            data: configData.value,
        })

        if (res.code === 1) {
            ElMessage.success('配置保存成功')
            originalConfigData.value = JSON.parse(JSON.stringify(configData.value))
            emit('refresh')
        } else {
            ElMessage.error(res.msg || '保存失败')
        }
    } catch (error) {
        console.error('保存配置失败:', error)
        ElMessage.error('保存配置失败')
    } finally {
        loading.value = false
    }
}

// 关闭弹窗
const handleClose = () => {
    dialogVisible.value = false
}

// 监听弹窗开关
watch(dialogVisible, (newVal) => {
    if (newVal) {
        loadConfigData()
    }
})
</script>

<style scoped lang="scss">
.service-type-manage {
    .el-tabs {
        :deep(.el-tabs__content) {
            padding: 20px 0;
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style> 