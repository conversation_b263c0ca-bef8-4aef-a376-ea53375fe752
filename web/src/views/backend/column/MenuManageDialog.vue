<template>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="dialogWidth" :before-close="handleClose" class="menu-manage-dialog">
        <div class="menu-manage-container">
            <!-- 加载状态 -->
            <div v-if="!langPacksLoaded" class="loading-container">
                <div class="loading-icon">
                    <i class="el-icon-loading"></i>
                </div>
                <p>正在加载语言包...</p>
            </div>

            <!-- 学科分类管理 -->
            <div v-else-if="managementType === 'subject'" class="management-content subject-management">
                <SubjectCategoryIndex :key="componentKey" />
            </div>

            <!-- 服务类型管理 -->
            <div v-else-if="managementType === 'service'" class="management-content service-management">
                <div class="service-panels">
                    <!-- 左侧：服务场景 -->
                    <div class="service-panel left-panel">
                        <div class="panel-header">
                            <h3>服务场景</h3>
                        </div>
                        <div class="panel-content">
                            <ChangjingIndex :key="`changjing-${componentKey}`" />
                        </div>
                    </div>

                    <!-- 右侧：服务类型 -->
                    <div class="service-panel right-panel">
                        <div class="panel-header">
                            <h3>服务类型</h3>
                        </div>
                        <div class="panel-content">
                            <FutypeIndex :key="`futype-${componentKey}`" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 头条分类管理 -->
            <div v-else-if="managementType === 'headline'" class="management-content headline-management">
                <ToutiaotypeIndex :key="componentKey" />
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useConfig } from '/@/stores/config'
import { mergeMessage } from '/@/lang/index'
import SubjectCategoryIndex from '/@/views/backend/subjectcategory/index.vue'
import FutypeIndex from '/@/views/backend/futype/index.vue'
import ChangjingIndex from '/@/views/backend/changjing/index.vue'
import ToutiaotypeIndex from '/@/views/backend/toutiaotype/index.vue'

interface Props {
    modelValue: boolean
    managementType: 'subject' | 'service' | 'headline'
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const dialogVisible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
})

const dialogTitle = computed(() => {
    switch (props.managementType) {
        case 'subject':
            return '学科分类管理'
        case 'service':
            return '服务类型管理'
        case 'headline':
            return '头条分类管理'
        default:
            return '管理'
    }
})

// 根据管理类型动态调整弹窗宽度
const dialogWidth = computed(() => {
    switch (props.managementType) {
        case 'service':
            return '1350px' // 服务管理需要更宽的空间
        case 'subject':
            return '1200px' // 学科分类管理进一步加宽
        case 'headline':
            return '1000px' // 头条分类管理加宽
        default:
            return '800px' // 其他类型使用标准宽度
    }
})

const componentRenderKey = ref(0)
const langPacksLoaded = ref(false)

const componentKey = computed(() => {
    return `${props.managementType}-${componentRenderKey.value}`
})

// 手动加载语言包的函数
const loadRequiredLangPacks = async () => {
    const config = useConfig()
    const locale = config.lang.defaultLang
    const langPacks = []

    if (props.managementType === 'subject') {
        langPacks.push('subjectcategory')
    } else if (props.managementType === 'service') {
        langPacks.push('changjing', 'futype')
    } else if (props.managementType === 'headline') {
        langPacks.push('toutiaotype')
    }

    // 动态加载所需的语言包
    for (const packName of langPacks) {
        const langPath = `./backend/${locale}/${packName}.ts`
        if (langPath in window.loadLangHandle) {
            try {
                const res = await window.loadLangHandle[langPath]()
                if (res.default) {
                    mergeMessage(res.default, packName)
                }
            } catch (error) {
                console.error(`Failed to load language pack: ${packName}`, error)
            }
        }
    }
}

// 监听对话框开启，加载语言包并重新渲染组件
watch(dialogVisible, async (newVal) => {
    if (newVal) {
        // 先重置语言包加载状态
        langPacksLoaded.value = false
        // 先加载语言包
        await loadRequiredLangPacks()
        // 确保语言包加载完成后再允许组件渲染
        langPacksLoaded.value = true
        // 更新组件key强制重新渲染
        componentRenderKey.value++
    } else {
        // 对话框关闭时重置状态
        langPacksLoaded.value = false
    }
})

const handleClose = () => {
    dialogVisible.value = false
}
</script>

<style scoped lang="scss">
.menu-manage-dialog {
    :deep(.el-dialog) {
        max-height: 90vh;
        height: 80vh;
        display: flex;
        flex-direction: column;
        
        .el-dialog__body {
            flex: 1;
            overflow: hidden;
            padding: 20px;
        }
    }
}

.menu-manage-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 500px;

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #666;

        .loading-icon {
            font-size: 30px;
            animation: rotating 2s linear infinite;
        }

        p {
            margin-top: 15px;
            font-size: 14px;
        }
    }

    @keyframes rotating {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

    .management-content {
        flex: 1;
        overflow: hidden;
        min-height: 0;
        display: flex;
        flex-direction: column;

        // 服务类型管理的特殊样式
        &.service-management {
            .service-panels {
                display: flex;
                height: 100%;
                gap: 20px;
                max-width: 100%;
                margin: 0 auto;

                .service-panel {
                    display: flex;
                    flex-direction: column;
                    border: 1px solid #e4e7ed;
                    border-radius: 8px;
                    overflow: hidden;
                    min-width: 500px;

                    &.left-panel {
                        flex: 1;
                        max-width: 580px;
                    }

                    &.right-panel {
                        flex: 1.2;
                        max-width: 680px;
                    }

                    .panel-header {
                        flex-shrink: 0;
                        padding: 10px 15px;
                        background: #f5f7fa;
                        border-bottom: 1px solid #e4e7ed;

                        h3 {
                            margin: 0;
                            font-size: 14px;
                            font-weight: 600;
                            color: #333;
                        }
                    }

                    .panel-content {
                        flex: 1;
                        overflow: hidden;
                        display: flex;
                        flex-direction: column;
                        padding: 12px;
                        min-height: 0;

                        // 基本样式
                        :deep(.ba-table-box) {
                            background: #fff;
                            border: none;
                            box-shadow: none;
                            overflow: visible;
                            height: 100%;
                            display: flex;
                            flex-direction: column;
                        }

                        // 按钮组样式 - 确保完全显示
                        :deep(.table-header) {
                            margin-bottom: 8px;
                            padding: 6px 0;
                            overflow: visible;
                            flex-shrink: 0;

                            .el-button {
                                padding: 4px 8px;
                                font-size: 12px;
                                margin-right: 5px;
                                margin-bottom: 4px;
                            }
                        }

                        // 表格容器样式
                        :deep(.ba-table-container) {
                            flex: 1;
                            overflow: auto;
                            min-height: 0;
                        }

                        // 表格样式优化 - 紧凑单元格
                        :deep(.el-table) {
                            font-size: 12px;
                            height: 100%;

                            .el-table__header {
                                th {
                                    padding: 4px 6px;
                                    font-size: 12px;
                                    background: #fafafa;
                                    border-bottom: 1px solid #e4e7ed;
                                }
                            }

                            .el-table__body {
                                td {
                                    padding: 3px 6px;
                                    font-size: 12px;
                                    line-height: 1.2;
                                }
                            }

                            // 操作列按钮更紧凑
                            .el-button {
                                padding: 2px 4px;
                                font-size: 11px;
                                margin: 0 1px;
                                min-height: 20px;
                            }

                            // 紧凑的图标和开关
                            .el-icon {
                                font-size: 12px;
                            }
                        }

                        // 分页器样式 - 固定在底部
                        :deep(.el-pagination) {
                            font-size: 12px;
                            padding: 8px 0;
                            margin-top: 8px;
                            flex-shrink: 0;
                            border-top: 1px solid #f0f0f0;

                            .el-pagination__total,
                            .el-pagination__sizes,
                            .el-pagination__jump {
                                font-size: 12px;
                            }

                            .el-pagination__pager {
                                .number {
                                    min-width: 24px;
                                    height: 24px;
                                    line-height: 24px;
                                    font-size: 12px;
                                }
                            }
                        }

                        // 右边面板特殊优化 - 让开关和图标更紧凑
                        :deep(.el-switch) {
                            --el-switch-width: 30px;
                            --el-switch-height: 16px;
                        }

                        // 确保内容完全显示
                        :deep(.el-scrollbar__view) {
                            height: 100%;
                        }
                    }
                }

                .left-panel {
                    .panel-header {
                        background: #f0f9ff;
                        border-bottom-color: #0ea5e9;

                        h3 {
                            color: #0ea5e9;
                        }
                    }
                }

                .right-panel {
                    .panel-header {
                        background: #f0fdf4;
                        border-bottom-color: #22c55e;

                        h3 {
                            color: #22c55e;
                        }
                    }
                }
            }
        }

        // 学科分类管理的特殊样式
        &.subject-management {
            // 确保表格内容完整显示
            :deep(.ba-table-box) {
                height: 100%;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            // 按钮组样式 - 确保完全显示
            :deep(.table-header) {
                margin-bottom: 8px;
                padding: 6px 0;
                overflow: visible;
                flex-shrink: 0;

                .el-button {
                    padding: 4px 8px;
                    font-size: 12px;
                    margin-right: 5px;
                    margin-bottom: 4px;
                }
            }

            // 表格容器样式
            :deep(.ba-table-container) {
                flex: 1;
                overflow: auto;
                min-height: 0;
            }

            // 表格样式优化 - 紧凑单元格
            :deep(.el-table) {
                font-size: 12px;
                height: 100%;
                width: 100%;

                .el-table__header {
                    th {
                        padding: 4px 6px;
                        font-size: 12px;
                        background: #fafafa;
                        border-bottom: 1px solid #e4e7ed;
                        white-space: nowrap;
                    }
                }

                .el-table__body {
                    td {
                        padding: 3px 6px;
                        font-size: 12px;
                        line-height: 1.2;
                        white-space: nowrap;
                    }
                }

                // 操作列按钮更紧凑
                .el-button {
                    padding: 2px 4px;
                    font-size: 11px;
                    margin: 0 1px;
                    min-height: 20px;
                }

                // 紧凑的图标和开关
                .el-icon {
                    font-size: 12px;
                }

                // 确保表格能水平滚动
                .el-table__body-wrapper {
                    overflow-x: auto;
                }
            }

            // 分页器样式 - 固定在底部
            :deep(.el-pagination) {
                font-size: 12px;
                padding: 8px 0;
                margin-top: 8px;
                flex-shrink: 0;
                border-top: 1px solid #f0f0f0;

                .el-pagination__total,
                .el-pagination__sizes,
                .el-pagination__jump {
                    font-size: 12px;
                }

                .el-pagination__pager {
                    .number {
                        min-width: 24px;
                        height: 24px;
                        line-height: 24px;
                        font-size: 12px;
                    }
                }
            }

            // 右边面板特殊优化 - 让开关和图标更紧凑
            :deep(.el-switch) {
                --el-switch-width: 30px;
                --el-switch-height: 16px;
            }

            // 确保内容完全显示
            :deep(.el-scrollbar__view) {
                height: 100%;
            }
        }

        // 头条分类管理的特殊样式
        &.headline-management {
            // 确保表格内容完整显示
            :deep(.ba-table-box) {
                height: 100%;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            // 按钮组样式 - 确保完全显示
            :deep(.table-header) {
                margin-bottom: 8px;
                padding: 6px 0;
                overflow: visible;
                flex-shrink: 0;

                .el-button {
                    padding: 4px 8px;
                    font-size: 12px;
                    margin-right: 5px;
                    margin-bottom: 4px;
                }
            }

            // 表格容器样式
            :deep(.ba-table-container) {
                flex: 1;
                overflow: auto;
                min-height: 0;
            }

            // 表格样式优化 - 紧凑单元格
            :deep(.el-table) {
                font-size: 12px;
                height: 100%;
                width: 100%;

                .el-table__header {
                    th {
                        padding: 4px 6px;
                        font-size: 12px;
                        background: #fafafa;
                        border-bottom: 1px solid #e4e7ed;
                        white-space: nowrap;
                    }
                }

                .el-table__body {
                    td {
                        padding: 3px 6px;
                        font-size: 12px;
                        line-height: 1.2;
                        white-space: nowrap;
                    }
                }

                // 操作列按钮更紧凑
                .el-button {
                    padding: 2px 4px;
                    font-size: 11px;
                    margin: 0 1px;
                    min-height: 20px;
                }

                // 紧凑的图标和开关
                .el-icon {
                    font-size: 12px;
                }

                // 确保表格能水平滚动
                .el-table__body-wrapper {
                    overflow-x: auto;
                }
            }

            // 分页器样式 - 固定在底部
            :deep(.el-pagination) {
                font-size: 12px;
                padding: 8px 0;
                margin-top: 8px;
                flex-shrink: 0;
                border-top: 1px solid #f0f0f0;

                .el-pagination__total,
                .el-pagination__sizes,
                .el-pagination__jump {
                    font-size: 12px;
                }

                .el-pagination__pager {
                    .number {
                        min-width: 24px;
                        height: 24px;
                        line-height: 24px;
                        font-size: 12px;
                    }
                }
            }

            // 右边面板特殊优化 - 让开关和图标更紧凑
            :deep(.el-switch) {
                --el-switch-width: 30px;
                --el-switch-height: 16px;
            }

            // 确保内容完全显示
            :deep(.el-scrollbar__view) {
                height: 100%;
            }
        }
    }
}
</style>
