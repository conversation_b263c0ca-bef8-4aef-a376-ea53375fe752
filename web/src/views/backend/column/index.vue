<template>
    <div class="default-main ba-table-box">
        <el-alert class="ba-table-alert" v-if="baTable.table.remark" :title="baTable.table.remark" type="info" show-icon />

        <!-- 表格顶部菜单 -->
        <!-- 自定义按钮请使用插槽，甚至公共搜索也可以使用具名插槽渲染，参见文档 -->
        <TableHeader
            :buttons="['refresh', 'add', 'edit', 'delete', 'comSearch', 'quickSearch', 'columnDisplay']"
            :quick-search-placeholder="t('Quick search placeholder', { fields: t('column.quick Search Fields') })"
        >
            <!-- 可以在此处以插槽的方式设置一些自定义按钮 -->

            <!-- 默认插槽 -->
            <template #default>
                <el-button v-blur class="table-header-operate" type="primary" @click="handleSubjectCategoryManage">
                    <Icon name="fa fa-sitemap" />
                    <span class="table-header-operate-text">学科分类管理</span>
                </el-button>
                <el-button v-blur class="table-header-operate" type="success" @click="handleServiceTypeManage">
                    <Icon name="fa fa-cogs" />
                    <span class="table-header-operate-text">服务类型管理</span>
                </el-button>
                <el-button v-blur class="table-header-operate" type="warning" @click="handleHeadlineTypeManage">
                    <Icon name="fa fa-tags" />
                    <span class="table-header-operate-text">头条分类管理</span>
                </el-button>
            </template>
        </TableHeader>

        <!-- 表格 -->
        <!-- 表格列有多种自定义渲染方式，比如自定义组件、具名插槽等，参见文档 -->
        <!-- 要使用 el-table 组件原有的属性，直接加在 Table 标签上即可 -->
        <Table ref="tableRef"></Table>

        <!-- 表单 -->
        <PopupForm />

        <!-- 快捷添加内容弹窗 -->
        <el-dialog v-model="quickContentVisible" title="快捷添加内容" width="800px" :close-on-click-modal="false" :destroy-on-close="true">
            <QuickContentForm
                v-if="quickContentVisible"
                :column-data="selectedColumn"
                @success="handleContentSuccess"
                @cancel="quickContentVisible = false"
            />
        </el-dialog>

        <!-- 内容列表弹窗 -->
        <ContentListDialog v-model="contentListVisible" :column-data="selectedColumn" @refresh="handleRefreshColumn" />

        <!-- 学科分类管理弹窗 -->
        <MenuManageDialog v-model="subjectCategoryVisible" management-type="subject" />

        <!-- 服务类型管理弹窗 -->
        <MenuManageDialog v-model="serviceTypeVisible" management-type="service" />

        <!-- 头条分类管理弹窗 -->
        <MenuManageDialog v-model="headlineTypeVisible" management-type="headline" />
    </div>
</template>

<script setup lang="ts">
import { onMounted, provide, useTemplateRef, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'

import PopupForm from './popupForm.vue'
import QuickContentForm from './QuickContentForm.vue'
import ContentListDialog from './ContentListDialog.vue'
import MenuManageDialog from './MenuManageDialog.vue'
import { baTableApi } from '/@/api/common'
import { defaultOptButtons } from '/@/components/table'
import TableHeader from '/@/components/table/header/index.vue'
import Table from '/@/components/table/index.vue'
import Icon from '/@/components/icon/index.vue'
import baTableClass from '/@/utils/baTable'

defineOptions({
    name: 'column',
})

const { t } = useI18n()
const tableRef = useTemplateRef('tableRef')

// 快捷添加内容相关状态
const quickContentVisible = ref(false)
const contentListVisible = ref(false)
const subjectCategoryVisible = ref(false)
const serviceTypeVisible = ref(false)
const headlineTypeVisible = ref(false)
const selectedColumn = ref<any>(null)

// 学科分类管理
const handleSubjectCategoryManage = () => {
    subjectCategoryVisible.value = true
}

// 服务类型管理
const handleServiceTypeManage = () => {
    serviceTypeVisible.value = true
}

// 头条分类管理
const handleHeadlineTypeManage = () => {
    headlineTypeVisible.value = true
}

// 快捷添加内容功能
const quickAddContent = (row: TableRow) => {
    selectedColumn.value = row
    quickContentVisible.value = true
}

// 内容添加成功回调
const handleContentSuccess = () => {
    quickContentVisible.value = false
    ElMessage.success('内容添加成功')
    // 刷新栏目列表以更新内容数量
    baTable.getData()
}

// 刷新栏目列表
const handleRefreshColumn = () => {
    baTable.getData()
}

// 查看栏目下的内容
const viewColumnContent = (row: TableRow) => {
    selectedColumn.value = row
    contentListVisible.value = true
}

const optButtons: OptButton[] = [
    ...defaultOptButtons(['weigh-sort', 'edit', 'delete']),
    {
        render: 'tipButton',
        name: 'viewContent',
        title: '查看内容',
        text: '内容',
        type: 'primary',
        icon: 'fa fa-list',
        class: 'table-row-view-content',
        disabledTip: false,
        click: (row: TableRow) => {
            viewColumnContent(row)
        },
    },
    {
        render: 'tipButton',
        name: 'quickAddContent',
        title: '快捷添加内容',
        text: '',
        type: 'success',
        icon: 'fa fa-plus-circle',
        class: 'table-row-quick-add',
        disabledTip: false,
        click: (row: TableRow) => {
            quickAddContent(row)
        },
    },
]

/**
 * baTable 内包含了表格的所有数据且数据具备响应性，然后通过 provide 注入给了后代组件
 */
const baTable = new baTableClass(
    new baTableApi('/admin/Column/'),
    {
        pk: 'id',
        column: [
            { type: 'selection', align: 'center', operator: false },
            { label: t('column.id'), prop: 'id', align: 'center', operator: 'RANGE', sortable: false },
            { label: t('column.name'), prop: 'name', align: 'center', operatorPlaceholder: t('Fuzzy query'), operator: 'LIKE', sortable: false },
            {
                label: t('column.code'),
                prop: 'code',
                align: 'center',
                render: 'tag',
                operator: 'eq',
                sortable: false,
                replaceValue: {
                    service: t('column.code service'),
                    expert: t('column.code expert'),
                    journal: t('column.code journal'),
                    headline: t('column.code headline'),
                },
            },
            { label: t('column.sort'), prop: 'sort', align: 'center', operator: 'RANGE', sortable: 'custom' },
            {
                label: t('column.status'),
                prop: 'status',
                align: 'center',
                render: 'tag',
                operator: 'eq',
                sortable: false,
                replaceValue: { '0': t('column.status 0'), '1': t('column.status 1') },
            },
            { label: t('column.remark'), prop: 'remark', align: 'center', operatorPlaceholder: t('Fuzzy query'), operator: 'LIKE', sortable: false },
            {
                label: '内容数量',
                prop: 'content_count',
                align: 'center',
                width: 100,
                operator: false,
                render: 'tag',
            },
            {
                label: t('column.create_time'),
                prop: 'create_time',
                align: 'center',
                render: 'datetime',
                operator: 'RANGE',
                sortable: 'custom',
                width: 160,
                timeFormat: 'yyyy-mm-dd hh:MM:ss',
            },
            {
                label: t('column.update_time'),
                prop: 'update_time',
                align: 'center',
                render: 'datetime',
                operator: 'RANGE',
                sortable: 'custom',
                width: 160,
                timeFormat: 'yyyy-mm-dd hh:MM:ss',
            },
            { label: t('Operate'), align: 'center', width: 250, render: 'buttons', buttons: optButtons, operator: false },
        ],
        dblClickNotEditColumn: [undefined],
        defaultOrder: { prop: 'sort', order: 'desc' },
    },
    {
        defaultItems: { code: 'service', status: '0' },
    }
)

provide('baTable', baTable)

onMounted(() => {
    baTable.table.ref = tableRef.value
    baTable.mount()
    baTable.getData()?.then(() => {
        baTable.initSort()
        baTable.dragSort()
    })
})
</script>

<style scoped lang="scss">
.table-header-operate {
    margin-left: 12px;
}
.table-header-operate-text {
    margin-left: 6px;
}

:deep(.table-row-view-content) {
    margin-left: 5px;
}

:deep(.table-row-view-content .el-button) {
    padding: 5px 8px;
    font-size: 12px;
}

:deep(.table-row-quick-add) {
    margin-left: 5px;
}

:deep(.table-row-quick-add .el-button) {
    padding: 5px 8px;
    font-size: 12px;
}
</style>
