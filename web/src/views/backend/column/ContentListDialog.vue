<template>
    <el-dialog v-model="visible" :title="`${columnData?.name || ''}的内容管理`" width="90%" height="80vh" :close-on-click-modal="false" :destroy-on-close="true" @close="handleClose">
        <div class="content-list-dialog">
            <!-- 表格顶部菜单 -->
            <div class="table-header">
                <el-button type="primary" @click="handleAdd">
                    <i class="fa fa-plus"></i>
                    添加内容
                </el-button>
                <el-button @click="handleRefresh">
                    <i class="fa fa-refresh"></i>
                    刷新
                </el-button>
                <div class="search-box">
                    <el-input v-model="searchKeyword" placeholder="搜索标题..." style="width: 200px" @input="handleSearch" clearable>
                        <template #prefix>
                            <i class="fa fa-search"></i>
                        </template>
                    </el-input>
                </div>
            </div>

            <!-- 内容表格 -->
            <el-table v-loading="loading" :data="contentList" style="width: 100%" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="title" label="标题" min-width="150" show-overflow-tooltip />
                <el-table-column prop="column.name" label="栏目" width="100" show-overflow-tooltip>
                    <template #default="{ row }">
                        <el-tag type="info">{{ row.column?.name || '-' }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="content_type" label="类型" width="100">
                    <template #default="{ row }">
                        <el-tag :type="getContentTypeTagType(row.content_type)">
                            {{ getContentTypeText(row.content_type) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="homepage_recommend" label="首页推荐" width="100">
                    <template #default="{ row }">
                        <el-tag :type="row.homepage_recommend === 1 ? 'success' : 'info'">
                            {{ row.homepage_recommend === 1 ? '是' : '否' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="推荐操作" width="120" align="center">
                    <template #default="{ row }">
                        <el-button :type="row.homepage_recommend === 1 ? 'warning' : 'success'" size="small" @click="handleToggleRecommend(row)">
                            {{ row.homepage_recommend === 1 ? '取消推荐' : '推荐首页' }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="80">
                    <template #default="{ row }">
                        <el-tag :type="row.status === '1' ? 'success' : 'danger'">
                            {{ row.status === '1' ? '启用' : '禁用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="click_count" label="点击量" width="80" />
                <el-table-column prop="create_time" label="创建时间" width="160" />
                <el-table-column label="操作" :width="isExpertColumn ? 180 : 140" fixed="right">
                    <template #default="{ row }">
                        <!-- 拖拽排序按钮 - 仅专家智库显示 -->
                        <el-tooltip v-if="isExpertColumn" content="拖拽排序" placement="top">
                            <el-button
                                size="small"
                                type="info"
                                class="table-row-weigh-sort move-button"
                                style="cursor: move;"
                            >
                                <i class="fa fa-arrows"></i>
                            </el-button>
                        </el-tooltip>
                        <el-button type="primary" size="small" @click="handleEdit(row)"> 编辑 </el-button>
                        <el-button type="danger" size="small" @click="handleDelete(row)"> 删除 </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>

        <!-- 内容编辑弹窗 -->
        <el-dialog
            v-model="editDialogVisible"
            :title="editForm.id ? '编辑内容' : '添加内容'"
            width="800px"
            :close-on-click-modal="false"
            :destroy-on-close="true"
            append-to-body
        >
            <QuickContentForm
                v-if="editDialogVisible"
                :column-data="columnData"
                :edit-data="editForm.id ? editForm : null"
                @success="handleEditSuccess"
                @cancel="editDialogVisible = false"
            />
        </el-dialog>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import createAxios from '/@/utils/axios'
import QuickContentForm from './QuickContentForm.vue'
import Sortable from 'sortablejs'

interface Props {
    modelValue: boolean
    columnData: any
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'refresh'])

// 弹窗显示状态
const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
})

// 判断是否是专家智库栏目
const isExpertColumn = computed(() => {
    return props.columnData?.code === 'expert'
})

// 数据状态
const loading = ref(false)
const contentList = ref<any[]>([])
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const selectedItems = ref<any[]>([])

// 编辑弹窗状态
const editDialogVisible = ref(false)
const editForm = ref<any>({})

// 加载内容列表
const loadContentList = async () => {
    if (!props.columnData?.id) return

    loading.value = true
    try {
        const params: any = {
            page: currentPage.value,
            limit: pageSize.value,
            search: [
                {
                    field: 'column_id',
                    operator: '=',
                    val: props.columnData.id,
                },
            ],
        }

        // 添加搜索条件
        if (searchKeyword.value) {
            params.search.push({
                field: 'title',
                operator: 'LIKE',
                val: searchKeyword.value,
            })
        }

        const res = await createAxios({
            url: '/admin/Content/index',
            method: 'get',
            params,
        })

        if (res.code === 1) {
            contentList.value = res.data.list || []
            total.value = res.data.total || 0
            // 数据加载完成后初始化拖拽排序
            nextTick(() => {
                initDragSort()
            })
        }
    } catch (error) {
        console.error('加载内容列表失败:', error)
        ElMessage.error('加载内容列表失败')
    } finally {
        loading.value = false
    }
}

// 搜索处理
const handleSearch = () => {
    currentPage.value = 1
    loadContentList()
}

// 刷新
const handleRefresh = () => {
    loadContentList()
}

// 分页处理
const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    loadContentList()
}

const handleCurrentChange = (page: number) => {
    currentPage.value = page
    loadContentList()
}

// 选择处理
const handleSelectionChange = (selection: any[]) => {
    selectedItems.value = selection
}

// 添加内容
const handleAdd = () => {
    editForm.value = {}
    editDialogVisible.value = true
}

// 编辑内容
const handleEdit = (row: any) => {
    editForm.value = { ...row }
    editDialogVisible.value = true
}

// 删除内容
const handleDelete = async (row: any) => {
    try {
        await ElMessageBox.confirm('确定删除该内容吗？', '确认删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })

        const res = await createAxios({
            url: '/admin/Content/del',
            method: 'delete',
            params: { ids: [row.id] },
        })

        if (res.code === 1) {
            ElMessage.success('删除成功')
            loadContentList()
            emit('refresh')
        } else {
            ElMessage.error(res.msg || '删除失败')
        }
    } catch (error) {
        console.error('删除失败:', error)
    }
}

// 切换推荐状态
const handleToggleRecommend = async (row: any) => {
    try {
        const newStatus = row.homepage_recommend === 1 ? 0 : 1
        const res = await createAxios({
            url: '/admin/Content/edit',
            method: 'post',
            data: {
                id: row.id,
                homepage_recommend: newStatus,
            },
        })

        if (res.code === 1) {
            ElMessage.success(newStatus === 1 ? '推荐成功' : '取消推荐成功')
            loadContentList()
            emit('refresh')
        } else {
            ElMessage.error(res.msg || '操作失败')
        }
    } catch (error) {
        console.error('切换推荐状态失败:', error)
        ElMessage.error('操作失败')
    }
}

// 编辑成功回调
const handleEditSuccess = () => {
    editDialogVisible.value = false
    loadContentList()
    emit('refresh')
}

// 弹窗关闭处理
const handleClose = () => {
    searchKeyword.value = ''
    currentPage.value = 1
    contentList.value = []
    total.value = 0
}

// 获取内容类型标签类型
const getContentTypeTagType = (contentType: string) => {
    const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
        SERVICE: 'success',
        EXPERT: 'warning',
        JOURNAL: 'info',
        HEADLINE: 'danger',
    }
    return typeMap[contentType] || 'info'
}

// 获取内容类型文本
const getContentTypeText = (contentType: string) => {
    const typeMap: Record<string, string> = {
        SERVICE: '学术服务',
        EXPERT: '专家智库',
        JOURNAL: '学术期刊',
        HEADLINE: '学术头条',
    }
    return typeMap[contentType] || contentType
}

// 初始化拖拽排序
const initDragSort = () => {
    if (!isExpertColumn.value) return

    nextTick(() => {
        const tableEl = document.querySelector('.content-list-dialog .el-table__body-wrapper .el-table__body tbody')
        if (tableEl) {
            Sortable.create(tableEl as HTMLElement, {
                animation: 200,
                handle: '.table-row-weigh-sort',
                ghostClass: 'ba-table-row',
                onEnd: async (evt: any) => {
                    // 目标位置不变
                    if (evt.oldIndex === evt.newIndex || typeof evt.newIndex === 'undefined' || typeof evt.oldIndex === 'undefined') return

                    const moveRow = contentList.value[evt.oldIndex]
                    const targetRow = contentList.value[evt.newIndex]

                    try {
                        const res = await createAxios({
                            url: '/admin/Content/sortable',
                            method: 'post',
                            data: {
                                move: moveRow.id,
                                target: targetRow.id,
                                order: 'sort,desc',
                                direction: evt.newIndex > evt.oldIndex ? 'down' : 'up',
                            }
                        })

                        if (res.code === 1) {
                            ElMessage.success('排序成功')
                            loadContentList()
                            emit('refresh')
                        } else {
                            ElMessage.error(res.msg || '排序失败')
                            loadContentList() // 恢复原始顺序
                        }
                    } catch (error) {
                        console.error('排序失败:', error)
                        ElMessage.error('排序失败')
                        loadContentList() // 恢复原始顺序
                    }
                }
            })
        }
    })
}

// 监听弹窗显示状态
watch(visible, (newVal) => {
    if (newVal) {
        loadContentList().then(() => {
            initDragSort()
        })
    }
})
</script>

<style scoped lang="scss">
.content-list-dialog {
    height: calc(80vh - 120px);
    display: flex;
    flex-direction: column;

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding: 0 4px;
        flex-shrink: 0;

        .search-box {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }

    .el-table {
        flex: 1;
        overflow: auto;
    }

    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 16px;
        flex-shrink: 0;
    }
}

// 覆盖Element Plus的对话框样式
:deep(.el-dialog) {
    display: flex;
    flex-direction: column;
    margin: 5vh auto !important;
    height: 80vh;
    max-height: 80vh;
}

:deep(.el-dialog__header) {
    flex-shrink: 0;
}

:deep(.el-dialog__body) {
    flex: 1;
    padding: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

:deep(.el-dialog__footer) {
    flex-shrink: 0;
}

// 拖拽排序样式
.move-button {
    cursor: move !important;
}

.ba-table-row {
    opacity: 0.5;
}

// 拖拽时的样式
:deep(.sortable-ghost) {
    opacity: 0.5;
    background-color: #f5f7fa;
}
</style>