<template>
    <div class="quick-content-form">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" @submit.prevent="">
            <!-- 基础信息 -->
            <el-form-item label="所属栏目" prop="column_id">
                <el-input v-model="columnName" disabled />
            </el-form-item>

            <!-- 通用字段 -->
            <el-form-item label="标题" prop="title">
                <el-input v-model="formData.title" placeholder="请输入标题" />
            </el-form-item>

            <!-- 根据栏目类型显示不同字段 -->
            <div v-if="columnType === 'service'" class="service-fields">
                <el-divider content-position="left">学术服务字段</el-divider>

                <el-form-item label="副标题" prop="subtitle">
                    <el-input v-model="formData.subtitle" placeholder="请输入服务副标题" />
                </el-form-item>

                <el-form-item label="服务描述" prop="service_desc">
                    <FormItem type="editor" v-model="formData.service_desc" placeholder="请输入服务详细描述" />
                </el-form-item>

                <el-form-item label="封面图片" prop="cover_image">
                    <FormItem
                        type="image"
                        v-model="formData.cover_image"
                        blockHelp="支持格式：jpg、jpeg、png、gif，建议尺寸：800x600px，文件大小不超过5MB"
                    />
                </el-form-item>

                <el-form-item label="售价(元)" prop="price">
                    <el-input-number
                        v-model="formData.price"
                        :precision="2"
                        :min="0"
                        :max="999999"
                        placeholder="请输入服务售价"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item label="购买人数" prop="purchase_count">
                    <el-input-number v-model="formData.purchase_count" :min="0" :max="999999" placeholder="请输入购买人数" style="width: 100%" />
                </el-form-item>

                <el-form-item label="服务标签" prop="service_tags">
                    <el-tag v-for="tag in serviceTags" :key="tag" class="service-tag" closable @close="handleRemoveServiceTag(tag)">
                        {{ tag }}
                    </el-tag>
                    <el-input
                        v-if="serviceInputVisible"
                        ref="ServiceInputRef"
                        v-model="serviceInputValue"
                        class="tag-input"
                        size="small"
                        @keyup.enter="handleServiceInputConfirm"
                        @blur="handleServiceInputConfirm"
                    />
                    <el-button v-else class="button-new-tag" size="small" @click="showServiceInput"> + 添加服务标签 </el-button>
                </el-form-item>

                <el-form-item label="服务场景" prop="service_scenarios">
                    <FormItem
                        type="remoteSelect"
                        v-model="formData.service_scenarios"
                        :input-attr="{
                            pk: 'id',
                            field: 'cj_name',
                            remoteUrl: '/admin/Changjing/index',
                            multiple: true,
                            clearable: true,
                            placeholder: '请选择服务场景',
                            style: { width: '100%', minWidth: '200px' },
                        }"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item label="服务类型" prop="service_types_select">
                    <FormItem
                        type="remoteSelect"
                        v-model="formData.service_types_select"
                        :input-attr="{
                            pk: 'id',
                            field: 'ft_name',
                            remoteUrl: '/admin/Futype/index',
                            multiple: true,
                            clearable: true,
                            placeholder: '请选择服务类型',
                            style: { width: '100%', minWidth: '200px' },
                        }"
                        style="width: 100%"
                    />
                </el-form-item>
            </div>

            <div v-else-if="columnType === 'expert'" class="expert-fields">
                <el-divider content-position="left">专家智库字段</el-divider>

                <el-form-item label="专家头像" prop="avatar_url">
                    <FormItem
                        type="image"
                        v-model="formData.avatar_url"
                        blockHelp="支持格式：jpg、jpeg、png、gif，建议尺寸：200x200px，文件大小不超过2MB"
                    />
                </el-form-item>

                <el-form-item label="专家编号" prop="expert_number">
                    <el-input v-model="formData.expert_number" placeholder="请输入专家编号" />
                </el-form-item>

                <el-form-item label="研究方向" prop="research_area">
                    <el-tag v-for="tag in researchTags" :key="tag" class="research-tag" closable @close="handleRemoveTag(tag)">
                        {{ tag }}
                    </el-tag>
                    <el-input
                        v-if="inputVisible"
                        ref="InputRef"
                        v-model="inputValue"
                        class="tag-input"
                        size="small"
                        @keyup.enter="handleInputConfirm"
                        @blur="handleInputConfirm"
                    />
                    <el-button v-else class="button-new-tag" size="small" @click="showInput"> + 添加研究方向 </el-button>
                </el-form-item>

                <el-form-item label="专家简介" prop="summary">
                    <el-input v-model="formData.summary" type="textarea" :rows="4" placeholder="请输入专家简介" />
                </el-form-item>
                <el-form-item label="学术成果" prop="academic_achievements">
                    <el-input v-model="formData.academic_achievements" type="textarea" :rows="4" placeholder="请输入学术成果" />
                </el-form-item>

                <el-form-item label="辅导经验" prop="tutoring_experience">
                    <el-input v-model="formData.tutoring_experience" type="textarea" :rows="4" placeholder="请输入辅导经验" />
                </el-form-item>

                <el-form-item label="擅长技能" prop="expertise_skills">
                    <el-input v-model="formData.expertise_skills" type="textarea" :rows="3" placeholder="请输入擅长技能" />
                </el-form-item>

                <el-form-item label="服务类型" prop="service_types">
                    <el-input v-model="formData.service_types" type="textarea" :rows="3" placeholder="请输入服务类型" />
                </el-form-item>
            </div>

            <div v-else-if="columnType === 'journal'" class="journal-fields">
                <el-divider content-position="left">学术期刊字段</el-divider>

                <el-form-item label="期刊封面" prop="cover_image">
                    <FormItem
                        type="image"
                        v-model="formData.cover_image"
                        blockHelp="支持格式：jpg、jpeg、png、gif，建议尺寸：284x180px，文件大小不超过3MB"
                    />
                </el-form-item>

                <el-form-item label="大类学科" prop="big_type">
                    <FormItem
                        type="remoteSelect"
                        v-model="formData.big_type"
                        :input-attr="{
                            pk: 'id',
                            field: 'name',
                            remoteUrl: '/admin/Subjectcategory/select',
                            params: { parent_id: 0, isTree: false },
                            multiple: false,
                            clearable: true,
                            placeholder: '请选择大类学科',
                            style: { width: '100%', minWidth: '200px' },
                        }"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item label="小类学科" prop="small_type">
                    <FormItem
                        :key="`small_type_${formData.big_type || 'empty'}`"
                        type="remoteSelect"
                        v-model="formData.small_type"
                        :input-attr="smallTypeParams"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item label="期刊类型" prop="journal_type">
                    <el-select v-model="formData.journal_type" placeholder="请选择期刊类型" style="width: 100%">
                        <el-option v-for="option in journalOptions.journal_type" :key="option.key" :label="option.value" :value="option.key" />
                    </el-select>
                </el-form-item>

                <el-form-item label="数据库" prop="journal_datatype">
                    <el-select v-model="formData.journal_datatype" placeholder="请选择数据库" style="width: 100%">
                        <el-option v-for="option in journalOptions.journal_datatype" :key="option.key" :label="option.value" :value="option.key" />
                    </el-select>
                </el-form-item>

                <el-form-item label="中科院" prop="journal_zky">
                    <el-select v-model="formData.journal_zky" placeholder="请选择中科院分区" style="width: 100%">
                        <el-option v-for="option in journalOptions.journal_zky" :key="option.key" :label="option.value" :value="option.key" />
                    </el-select>
                </el-form-item>

                <el-form-item label="JCR" prop="journal_jcr">
                    <el-select v-model="formData.journal_jcr" placeholder="请选择JCR分区" style="width: 100%">
                        <el-option v-for="option in journalOptions.journal_jcr" :key="option.key" :label="option.value" :value="option.key" />
                    </el-select>
                </el-form-item>

                <el-form-item label="影响因子" prop="impact_factor">
                    <el-input-number
                        v-model="formData.impact_factor"
                        :precision="3"
                        :min="0"
                        :max="100"
                        placeholder="请输入影响因子"
                        style="width: 100%"
                    />
                </el-form-item>
                <el-form-item label="期刊简介" prop="summary">
                    <FormItem type="editor" v-model="formData.summary" placeholder="请输入期刊简介" />
                </el-form-item>
                <el-form-item label="征稿主题" prop="journal_data">
                    <FormItem type="editor" v-model="formData.journal_data" placeholder="请输入征稿主题" />
                </el-form-item>

                <el-form-item label="参考周期" prop="paper_requirements">
                    <FormItem type="editor" v-model="formData.paper_requirements" placeholder="请输入参考周期" />
                </el-form-item>

                <el-form-item label="投稿须知" prop="submission_guidelines">
                    <FormItem type="editor" v-model="formData.submission_guidelines" placeholder="请输入详细的投稿须知" />
                </el-form-item>
            </div>

            <div v-else-if="columnType === 'headline'" class="headline-fields">
                <el-divider content-position="left">学术头条字段</el-divider>

                <el-form-item label="作者" prop="author">
                    <el-input v-model="formData.author" placeholder="请输入作者姓名" />
                </el-form-item>

                <el-form-item label="发布日期" prop="publish_date">
                    <el-date-picker
                        v-model="formData.publish_date"
                        type="date"
                        placeholder="请选择发布日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item label="摘要" prop="summary">
                    <el-input v-model="formData.summary" type="textarea" :rows="3" placeholder="请输入新闻摘要" />
                </el-form-item>

                <el-form-item label="富文本内容" prop="content">
                    <FormItem type="editor" v-model="formData.content" placeholder="请输入详细内容" />
                </el-form-item>

                <el-form-item label="封面图片" prop="cover_image">
                    <FormItem
                        type="image"
                        v-model="formData.cover_image"
                        blockHelp="支持格式：jpg、jpeg、png、gif，建议尺寸：800x450px，文件大小不超过5MB"
                    />
                </el-form-item>

                <el-form-item label="点赞数" prop="like_count">
                    <el-input-number v-model="formData.like_count" :min="0" :max="999999" placeholder="请输入点赞数" style="width: 100%" />
                </el-form-item>

                <el-form-item label="收藏数" prop="collect_count">
                    <el-input-number v-model="formData.collect_count" :min="0" :max="999999" placeholder="请输入收藏数" style="width: 100%" />
                </el-form-item>

                <el-form-item label="所属分类" prop="headline_types">
                    <FormItem
                        type="remoteSelects"
                        v-model="formData.headline_types"
                        :input-attr="{
                            remoteUrl: '/admin/Toutiaotype/index',
                            pk: 'id',
                            field: 'name',
                            multiple: true,
                            clearable: true,
                        }"
                        placeholder="请选择头条分类"
                    />
                </el-form-item>

                <el-form-item label="标签" prop="tags">
                    <el-tag v-for="tag in headlineTags" :key="tag" class="headline-tag" closable @close="handleRemoveHeadlineTag(tag)">
                        {{ tag }}
                    </el-tag>
                    <el-input
                        v-if="headlineInputVisible"
                        ref="HeadlineInputRef"
                        v-model="headlineInputValue"
                        class="tag-input"
                        size="small"
                        @keyup.enter="handleHeadlineInputConfirm"
                        @blur="handleHeadlineInputConfirm"
                    />
                    <el-button v-else class="button-new-tag" size="small" @click="showHeadlineInput"> + 添加标签 </el-button>
                </el-form-item>
            </div>

            <!-- 其他类型显示基础字段 -->
            <div v-else class="basic-fields">
                <el-form-item label="副标题" prop="subtitle">
                    <el-input v-model="formData.subtitle" placeholder="请输入副标题" />
                </el-form-item>

                <el-form-item label="摘要" prop="summary">
                    <el-input v-model="formData.summary" type="textarea" :rows="3" placeholder="请输入摘要" />
                </el-form-item>

                <el-form-item label="封面图片" prop="cover_image">
                    <FormItem
                        type="image"
                        v-model="formData.cover_image"
                        blockHelp="支持格式：jpg、jpeg、png、gif，建议尺寸：800x600px，文件大小不超过5MB"
                    />
                </el-form-item>
            </div>

            <!-- 关联字段 -->
            <div class="relation-fields">
                <el-divider content-position="left">关联内容</el-divider>

                <el-form-item label="关联服务" prop="related_services">
                    <FormItem
                        type="remoteSelects"
                        v-model="formData.related_services"
                        :input-attr="{
                            remoteUrl: '/admin/Content/getRelatedServices',
                            pk: 'id',
                            field: 'name',
                            multiple: true,
                            clearable: true,
                        }"
                        placeholder="请选择关联的学术服务"
                    />
                </el-form-item>

                <el-form-item label="关联专家" prop="related_experts">
                    <FormItem
                        type="remoteSelects"
                        v-model="formData.related_experts"
                        :input-attr="{
                            remoteUrl: '/admin/Content/getRelatedExperts',
                            pk: 'id',
                            field: 'name',
                            multiple: true,
                            clearable: true,
                        }"
                        placeholder="请选择关联的专家"
                    />
                </el-form-item>

                <el-form-item label="关联期刊" prop="related_journals">
                    <FormItem
                        type="remoteSelects"
                        v-model="formData.related_journals"
                        :input-attr="{
                            remoteUrl: '/admin/Content/getRelatedJournals',
                            pk: 'id',
                            field: 'name',
                            multiple: true,
                            clearable: true,
                        }"
                        placeholder="请选择关联的学术期刊"
                    />
                </el-form-item>
            </div>

            <!-- 通用字段 -->
            <el-form-item label="首页推荐" prop="homepage_recommend">
                <el-radio-group v-model="formData.homepage_recommend">
                    <el-radio :value="1">是</el-radio>
                    <el-radio :value="0">否</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="状态" prop="status">
                <el-radio-group v-model="formData.status">
                    <el-radio value="1">启用</el-radio>
                    <el-radio value="0">禁用</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>

        <div class="form-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" :loading="loading" @click="handleSubmit">保存</el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import createAxios from '/@/utils/axios'
import FormItem from '/@/components/formItem/index.vue'

interface Props {
    columnData: any
    editData?: any
}

const props = defineProps<Props>()
const emit = defineEmits(['success', 'cancel'])

const formRef = ref<FormInstance>()
const loading = ref(false)

// 研究方向标签相关
const inputVisible = ref(false)
const inputValue = ref('')
const InputRef = ref()
const researchTags = ref<string[]>([])

// 学术头条标签相关
const headlineInputVisible = ref(false)
const headlineInputValue = ref('')
const HeadlineInputRef = ref()
const headlineTags = ref<string[]>([])

// 服务标签相关
const serviceInputVisible = ref(false)
const serviceInputValue = ref('')
const ServiceInputRef = ref()
const serviceTags = ref<string[]>([])

// 期刊配置选项
const journalOptions = ref({
    big_type: [] as Array<{ key: string; value: string }>,
    small_type: [] as Array<{ key: string; value: string }>,
    journal_type: [] as Array<{ key: string; value: string }>,
    journal_datatype: [] as Array<{ key: string; value: string }>,
    journal_zky: [] as Array<{ key: string; value: string }>,
    journal_jcr: [] as Array<{ key: string; value: string }>,
})

// 表单数据
const formData = ref({
    id: undefined as number | undefined,
    title: '',
    author: '',
    publish_date: '',
    subtitle: '',
    summary: '',
    content: '',
    cover_image: '',
    link_url: '',
    sort: 50,
    status: '1',
    click_count: 0,
    like_count: 0,
    collect_count: 0,
    tags: '',
    top_flag: 'opt0',
    homepage_recommend: 0,
    // 学术服务字段
    service_desc: '',
    service_tags: '',
    service_scenarios: [] as string[],
    service_types_select: [] as string[],
    price: undefined as number | undefined,
    purchase_count: 0,
    // 专家智库字段
    avatar_url: '',
    expert_name: '',
    expert_number: '',
    institution: '',
    research_area: '',
    academic_achievements: '',
    tutoring_experience: '',
    expertise_skills: '',
    service_types: '',
    // 学术期刊字段
    big_type: '',
    small_type: '',
    journal_type: '',
    journal_datatype: '',
    journal_zky: '',
    journal_jcr: '',
    impact_factor: undefined as number | undefined,
    journal_data: '',
    paper_requirements: '',
    submission_guidelines: '',
    // 学术头条分类字段
    headline_types: [] as string[],
    // 关联字段
    column_id: 0,
    content_type: '',
    related_services: [] as string[],
    related_experts: [] as string[],
    related_journals: [] as string[],
})

// 计算属性
const columnName = computed(() => props.columnData?.name || '')
const columnType = computed(() => props.columnData?.code || '')

// 动态表单验证规则
const rules = computed(() => {
    const baseRules: any = {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
    }

    // 根据栏目类型添加特定字段的验证规则
    switch (columnType.value) {
        case 'service':
            return {
                ...baseRules,
                service_desc: [{ required: true, message: '请输入服务描述', trigger: 'blur' }],
                cover_image: [{ required: true, message: '请输入封面图片', trigger: 'blur' }],
                price: [{ required: true, message: '请输入服务售价', trigger: 'blur' }],
                purchase_count: [{ required: true, message: '请输入购买人数', trigger: 'blur' }],
            }
        case 'expert':
            return {
                ...baseRules,
                avatar_url: [{ required: true, message: '请输入专家头像', trigger: 'blur' }],
                expert_number: [{ required: true, message: '请输入专家编号', trigger: 'blur' }],
                research_area: [{ required: true, message: '请添加研究方向', trigger: 'blur' }],
                // 去掉专家字段的必填验证
                // academic_achievements: [{ required: true, message: '请输入学术成果', trigger: 'blur' }],
                // tutoring_experience: [{ required: true, message: '请输入辅导经验', trigger: 'blur' }],
                // expertise_skills: [{ required: true, message: '请输入擅长技能', trigger: 'blur' }],
                // service_types: [{ required: true, message: '请输入服务类型', trigger: 'blur' }],
                // summary: [{ required: true, message: '请输入专家简介', trigger: 'blur' }],
            }
        case 'journal':
            return {
                ...baseRules,
                domain: [{ required: true, message: '请输入学科领域', trigger: 'blur' }],
                big_type: [{ required: true, message: '请选择大类学科', trigger: 'change' }],
                small_type: [{ required: true, message: '请选择小类学科', trigger: 'change' }],
                journal_type: [{ required: true, message: '请选择期刊类型', trigger: 'change' }],
                journal_datatype: [{ required: true, message: '请选择数据库', trigger: 'change' }],
                journal_zky: [{ required: true, message: '请选择中科院分区', trigger: 'change' }],
                journal_jcr: [{ required: true, message: '请选择JCR分区', trigger: 'change' }],
                impact_factor: [{ required: true, message: '请输入影响因子', trigger: 'blur' }],
                cover_image: [{ required: true, message: '请输入期刊封面', trigger: 'blur' }],
            }
        case 'headline':
            return {
                ...baseRules,
                author: [{ required: true, message: '请输入作者', trigger: 'blur' }],
                publish_date: [{ required: true, message: '请选择发布日期', trigger: 'change' }],
                summary: [{ required: true, message: '请输入摘要', trigger: 'blur' }],
                content: [{ required: true, message: '请输入富文本内容', trigger: 'blur' }],
                like_count: [{ required: true, message: '请输入点赞数', trigger: 'blur' }],
                collect_count: [{ required: true, message: '请输入收藏数', trigger: 'blur' }],
                tags: [{ required: true, message: '请添加标签', trigger: 'blur' }],
            }
        default:
            return baseRules
    }
})

// 根据栏目类型重置表单字段
const resetFormByColumnType = () => {
    // 清空所有特定字段
    formData.value.author = ''
    formData.value.publish_date = ''
    formData.value.subtitle = ''
    formData.value.summary = ''
    formData.value.content = ''
    formData.value.cover_image = ''
    formData.value.link_url = ''
    formData.value.like_count = 0
    formData.value.collect_count = 0
    formData.value.tags = ''
    formData.value.service_desc = ''
    formData.value.service_tags = ''
    formData.value.service_scenarios = []
    formData.value.service_types_select = []
    formData.value.price = undefined
    formData.value.purchase_count = 0
    formData.value.avatar_url = ''
    formData.value.expert_name = ''
    formData.value.expert_number = ''
    formData.value.institution = ''
    formData.value.research_area = ''
    formData.value.academic_achievements = ''
    formData.value.tutoring_experience = ''
    formData.value.expertise_skills = ''
    formData.value.service_types = ''
    formData.value.big_type = ''
    formData.value.small_type = ''
    formData.value.journal_type = ''
    formData.value.journal_datatype = ''
    formData.value.journal_zky = ''
    formData.value.journal_jcr = ''
    formData.value.impact_factor = undefined
    formData.value.journal_data = ''
    formData.value.paper_requirements = ''
    formData.value.submission_guidelines = ''
    // 清空关联字段
    formData.value.related_services = []
    formData.value.related_experts = []
    formData.value.related_journals = []
}

// 加载期刊配置选项
const loadJournalOptions = async () => {
    try {
        const res = await createAxios({
            url: '/admin/Content/getJournalConfig',
            method: 'get',
        })

        if (res.code === 1 && res.data) {
            journalOptions.value = res.data
        }
    } catch (error) {
        console.error('加载期刊配置选项失败:', error)
    }
}

// 服务标签相关方法
const handleRemoveServiceTag = (tag: string) => {
    serviceTags.value.splice(serviceTags.value.indexOf(tag), 1)
    formData.value.service_tags = serviceTags.value.join(',')
}

const showServiceInput = () => {
    serviceInputVisible.value = true
    nextTick(() => {
        ServiceInputRef.value!.input!.focus()
    })
}

const handleServiceInputConfirm = () => {
    if (serviceInputValue.value) {
        serviceTags.value.push(serviceInputValue.value)
        formData.value.service_tags = serviceTags.value.join(',')
    }
    serviceInputVisible.value = false
    serviceInputValue.value = ''
}

// 研究方向标签相关方法
const handleRemoveTag = (tag: string) => {
    researchTags.value.splice(researchTags.value.indexOf(tag), 1)
    formData.value.research_area = researchTags.value.join(',')
}

const showInput = () => {
    inputVisible.value = true
    nextTick(() => {
        InputRef.value!.input!.focus()
    })
}

const handleInputConfirm = () => {
    if (inputValue.value) {
        researchTags.value.push(inputValue.value)
        formData.value.research_area = researchTags.value.join(',')
    }
    inputVisible.value = false
    inputValue.value = ''
}

// 学术头条标签相关方法
const handleRemoveHeadlineTag = (tag: string) => {
    headlineTags.value.splice(headlineTags.value.indexOf(tag), 1)
    formData.value.tags = headlineTags.value.join(',')
}

const showHeadlineInput = () => {
    headlineInputVisible.value = true
    nextTick(() => {
        HeadlineInputRef.value!.input!.focus()
    })
}

const handleHeadlineInputConfirm = () => {
    if (headlineInputValue.value) {
        headlineTags.value.push(headlineInputValue.value)
        formData.value.tags = headlineTags.value.join(',')
    }
    headlineInputVisible.value = false
    headlineInputValue.value = ''
}

// 监听大类学科变化，重置小类学科
watch(
    () => formData.value.big_type,
    (newValue, oldValue) => {
        // 大类学科变化时清空小类学科
        if (newValue !== oldValue) {
            formData.value.small_type = ''
        }
    }
)

// 计算小类学科的查询参数
const smallTypeParams = computed(() => {
    const baseConfig = {
        pk: 'id',
        field: 'name',
        remoteUrl: '/admin/Subjectcategory/select',
        multiple: false,
        clearable: true,
        style: { width: '100%', minWidth: '200px' },
    }

    if (!formData.value.big_type) {
        return {
            ...baseConfig,
            params: { parent_id: 0, isTree: false },
            placeholder: '请先选择大类学科',
            disabled: true,
        }
    }

    const params = {
        parent_id: formData.value.big_type,
        isTree: false,
    }

    return {
        ...baseConfig,
        params: params,
        placeholder: '请选择小类学科',
        disabled: false,
    }
})

// 监听栏目数据变化
watch(
    () => props.columnData,
    (newData) => {
        if (newData) {
            formData.value.column_id = newData.id
            formData.value.content_type = newData.code?.toUpperCase() || ''

            // 如果不是编辑模式，重置表单字段
            if (!props.editData) {
                resetFormByColumnType()
            }

            // 如果是期刊类型，加载期刊配置选项
            if (newData.code === 'journal') {
                loadJournalOptions()
            }
        }
    },
    { immediate: true }
)

// 监听编辑数据变化
watch(
    () => props.editData,
    async (newData) => {
        if (newData) {
            // 先保存small_type的值，稍后单独设置
            const originalSmallType = newData.small_type

            // 临时移除small_type、position、domain和service_icon，避免同时设置导致的问题
            const dataWithoutSmallType = { ...newData }
            delete dataWithoutSmallType.small_type
            delete dataWithoutSmallType.position
            delete dataWithoutSmallType.domain
            delete dataWithoutSmallType.service_icon

            // 填充编辑数据（不包括small_type、position、domain和service_icon）
            Object.assign(formData.value, dataWithoutSmallType)

            // 解析研究方向标签
            if (newData.research_area) {
                researchTags.value = newData.research_area.split(',').filter((tag: string) => tag.trim())
            }
            // 解析学术头条标签
            if (newData.tags) {
                headlineTags.value = newData.tags.split(',').filter((tag: string) => tag.trim())
            }
            // 解析学术头条分类
            if (newData.headline_types) {
                formData.value.headline_types = newData.headline_types.split(',').filter((id: string) => id.trim())
            }
            // 解析关联字段
            if (newData.related_services) {
                formData.value.related_services = newData.related_services.split(',').filter((id: string) => id.trim())
            }
            if (newData.related_experts) {
                formData.value.related_experts = newData.related_experts.split(',').filter((id: string) => id.trim())
            }
            if (newData.related_journals) {
                formData.value.related_journals = newData.related_journals.split(',').filter((id: string) => id.trim())
            }
            // 解析学术服务标签
            if (newData.service_tags) {
                serviceTags.value = newData.service_tags.split(',').filter((tag: string) => tag.trim())
                formData.value.service_tags = newData.service_tags
            }
            // 解析服务场景和服务类型
            if (newData.service_scenarios) {
                formData.value.service_scenarios = newData.service_scenarios.split(',').filter((id: string) => id.trim())
            }
            if (newData.service_types_select) {
                formData.value.service_types_select = newData.service_types_select.split(',').filter((id: string) => id.trim())
            }

            // 如果有大类学科和小类学科，等待DOM更新后再设置小类学科
            if (newData.big_type && originalSmallType) {
                await nextTick()
                // 再等待一小段时间，确保RemoteSelect组件完全响应
                setTimeout(() => {
                    formData.value.small_type = originalSmallType
                }, 100)
            }
        }
    },
    { immediate: true }
)

// 取消操作
const handleCancel = () => {
    emit('cancel')
}

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return

    try {
        const valid = await formRef.value.validate()
        if (!valid) return

        loading.value = true

        // 根据栏目类型过滤提交数据
        const submitData = getSubmitDataByColumnType()

        const res = await createAxios({
            url: props.editData ? '/admin/Content/edit' : '/admin/Content/add',
            method: 'post',
            data: submitData,
        })

        if (res.code === 1) {
            emit('success')
        } else {
            ElMessage.error(res.msg || (props.editData ? '编辑失败' : '添加失败'))
        }
    } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败')
    } finally {
        loading.value = false
    }
}

// 根据栏目类型获取提交数据
const getSubmitDataByColumnType = () => {
    const baseData: any = {
        title: formData.value.title,
        sort: formData.value.sort,
        status: formData.value.status,
        click_count: formData.value.click_count,
        top_flag: formData.value.top_flag,
        homepage_recommend: formData.value.homepage_recommend,
        column_id: formData.value.column_id,
        content_type: formData.value.content_type,
        related_services: formData.value.related_services.join(','),
        related_experts: formData.value.related_experts.join(','),
        related_journals: formData.value.related_journals.join(','),
    }

    // 如果是编辑模式，添加ID
    if (props.editData) {
        baseData.id = formData.value.id
    }

    let result: any = { ...baseData }

    switch (columnType.value) {
        case 'service':
            result = {
                ...result,
                subtitle: formData.value.subtitle,
                cover_image: formData.value.cover_image,
                service_desc: formData.value.service_desc,
                service_tags: formData.value.service_tags,
                service_scenarios: formData.value.service_scenarios.join(','),
                service_types_select: formData.value.service_types_select.join(','),
                price: formData.value.price,
                purchase_count: formData.value.purchase_count,
            }
            break
        case 'expert':
            result = {
                ...result,
                summary: formData.value.summary,
                avatar_url: formData.value.avatar_url,
                expert_name: formData.value.expert_name,
                expert_number: formData.value.expert_number,
                institution: formData.value.institution,
                research_area: formData.value.research_area,
                academic_achievements: formData.value.academic_achievements,
                tutoring_experience: formData.value.tutoring_experience,
                expertise_skills: formData.value.expertise_skills,
                service_types: formData.value.service_types,
            }
            break
        case 'journal':
            result = {
                ...result,
                summary: formData.value.summary,
                cover_image: formData.value.cover_image,
                big_type: formData.value.big_type,
                small_type: formData.value.small_type,
                journal_type: formData.value.journal_type,
                journal_datatype: formData.value.journal_datatype,
                journal_zky: formData.value.journal_zky,
                journal_jcr: formData.value.journal_jcr,
                impact_factor: formData.value.impact_factor,
                journal_data: formData.value.journal_data,
                paper_requirements: formData.value.paper_requirements,
                submission_guidelines: formData.value.submission_guidelines,
            }
            break
        case 'headline':
            result = {
                ...result,
                author: formData.value.author,
                publish_date: formData.value.publish_date,
                summary: formData.value.summary,
                content: formData.value.content,
                cover_image: formData.value.cover_image,
                like_count: formData.value.like_count,
                collect_count: formData.value.collect_count,
                tags: formData.value.tags,
                headline_types: formData.value.headline_types.join(','),
            }
            break
        default:
            result = {
                ...result,
                subtitle: formData.value.subtitle,
                summary: formData.value.summary,
                cover_image: formData.value.cover_image,
            }
    }

    return result
}
</script>

<style scoped lang="scss">
.quick-content-form {
    .el-divider {
        margin: 20px 0 15px 0;
        font-weight: 600;
        color: var(--el-color-primary);
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #ebeef5;
    }
}

.service-fields,
.expert-fields,
.journal-fields,
.headline-fields,
.basic-fields,
.relation-fields {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
    border-left: 4px solid var(--el-color-primary);
}

.service-fields {
    border-left-color: #4caf50;
}

.expert-fields {
    border-left-color: #ff9800;
}

.journal-fields {
    border-left-color: #2196f3;
}

.headline-fields {
    border-left-color: #f44336;
}

.relation-fields {
    border-left-color: #9c27b0;
}

.research-tag,
.headline-tag,
.service-tag {
    margin-right: 8px;
    margin-bottom: 8px;
}

.tag-input {
    width: 120px;
    margin-right: 8px;
    vertical-align: bottom;
}

.button-new-tag {
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
}
</style>
