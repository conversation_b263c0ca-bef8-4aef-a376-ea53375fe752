<template>
    <div class="default-main ba-table-box">
        <el-alert class="ba-table-alert" v-if="baTable.table.remark" :title="baTable.table.remark" type="info" show-icon />

        <!-- 表格顶部菜单 -->
        <!-- 自定义按钮请使用插槽，甚至公共搜索也可以使用具名插槽渲染，参见文档 -->
        <TableHeader
            :buttons="['refresh', 'edit', 'delete', 'comSearch', 'quickSearch', 'columnDisplay']"
            :quick-search-placeholder="t('Quick search placeholder', { fields: t('content.quick Search Fields') })"
        ></TableHeader>

        <!-- 表格 -->
        <!-- 表格列有多种自定义渲染方式，比如自定义组件、具名插槽等，参见文档 -->
        <!-- 要使用 el-table 组件原有的属性，直接加在 Table 标签上即可 -->
        <Table ref="tableRef"></Table>

        <!-- 表单 -->
        <PopupForm />
    </div>
</template>

<script setup lang="ts">
import { onMounted, provide, useTemplateRef, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import PopupForm from './popupForm.vue'
import { baTableApi } from '/@/api/common'
import { defaultOptButtons } from '/@/components/table'
import TableHeader from '/@/components/table/header/index.vue'
import Table from '/@/components/table/index.vue'
import baTableClass from '/@/utils/baTable'

defineOptions({
    name: 'contentIndex',
})

const { t } = useI18n()
const route = useRoute()
const tableRef = useTemplateRef('tableRef')

// 判断是否是专家智库栏目
const isExpertColumn = computed(() => {
    // 从路由参数或筛选条件中判断
    return route.query.column_code === 'expert' || route.query.content_type === 'EXPERT'
})

// 根据栏目类型动态设置操作按钮
const optButtons = computed(() => {
    if (isExpertColumn.value) {
        return defaultOptButtons(['weigh-sort', 'edit', 'delete'])
    } else {
        return defaultOptButtons(['edit', 'delete'])
    }
})

// 动态生成表格列配置
const getTableColumns = () => {
    const baseColumns = [
        { type: 'selection', align: 'center', operator: false },
        { label: t('content.id'), prop: 'id', align: 'center', width: 70, operator: 'RANGE', sortable: false },
        {
            label: t('content.column__name'),
            prop: 'column.name',
            align: 'center',
            width: 100,
            operatorPlaceholder: t('Fuzzy query'),
            render: 'tag',
            operator: 'LIKE',
        },
        {
            label: t('content.content_type'),
            prop: 'content_type',
            align: 'center',
            width: 100,
            operatorPlaceholder: t('Fuzzy query'),
            operator: 'eq',
            sortable: false,
            render: 'tag',
            replaceValue: {
                SERVICE: '学术服务',
                EXPERT: '专家智库',
                JOURNAL: '学术期刊',
                HEADLINE: '学术头条',
            },
        },
        {
            label: t('content.title'),
            prop: 'title',
            align: 'center',
            minWidth: 200,
            operatorPlaceholder: t('Fuzzy query'),
            operator: 'LIKE',
            sortable: false,
            showOverflowTooltip: true,
        },
        {
            label: t('content.status'),
            prop: 'status',
            align: 'center',
            width: 80,
            render: 'tag',
            operator: 'eq',
            sortable: false,
            replaceValue: { '0': t('content.status 0'), '1': t('content.status 1') },
        },
        {
            label: t('content.homepage_recommend'),
            prop: 'homepage_recommend',
            align: 'center',
            width: 100,
            render: 'tag',
            operator: 'eq',
            sortable: false,
            replaceValue: { '0': t('content.homepage_recommend 0'), '1': t('content.homepage_recommend 1') },
        },
    ]

    // 只有专家智库栏目才显示排序字段
    if (isExpertColumn.value) {
        baseColumns.push({
            label: t('content.sort'),
            prop: 'sort',
            align: 'center',
            width: 80,
            operator: 'RANGE',
            sortable: true,
        })
    }

    // 添加其他通用字段
    baseColumns.push(
        { label: t('content.click_count'), prop: 'click_count', align: 'center', width: 80, operator: 'RANGE', sortable: false },
        {
            label: t('content.create_time'),
            prop: 'create_time',
            align: 'center',
            render: 'datetime',
            operator: 'RANGE',
            sortable: true,
            width: 160,
            timeFormat: 'yyyy-mm-dd hh:MM:ss',
        } as any,
        { label: t('Operate'), align: 'center', width: 100, render: 'buttons', buttons: optButtons.value, operator: false } as any
    )

    return baseColumns as TableColumn[]
}
/**
 * baTable 内包含了表格的所有数据且数据具备响应性，然后通过 provide 注入给了后代组件
 */
const baTable = new baTableClass(
    new baTableApi('/admin/Content/'),
    {
        pk: 'id',
        column: getTableColumns(),
        dblClickNotEditColumn: [undefined],
        defaultOrder: isExpertColumn.value ? { prop: 'sort', order: 'desc' } : { prop: 'create_time', order: 'desc' },
        filter: isExpertColumn.value ? { order: 'sort,desc' } : {},
    },
    {
        defaultItems: { status: '0', top_flag: 'opt0' },
    }
)

provide('baTable', baTable)

onMounted(() => {
    baTable.table.ref = tableRef.value
    baTable.mount()

    // 处理从栏目页面跳转过来的筛选条件
    if (route.query.column_id) {
        const columnId = Array.isArray(route.query.column_id) ? route.query.column_id[0] : route.query.column_id
        const columnName = Array.isArray(route.query.column_name) ? route.query.column_name[0] : route.query.column_name

        if (columnId) {
            // 设置栏目筛选条件
            baTable.table.filter!.search = [
                {
                    field: 'column_id',
                    operator: '=',
                    val: columnId,
                },
            ]
        }

        // 如果有栏目名称，显示筛选提示
        if (columnName) {
            baTable.table.remark = `正在显示栏目"${columnName}"下的内容`
        }
    }

    baTable.getData()?.then(() => {
        // 专家智库内容由服务端排序，不需要客户端重新排序
        if (!isExpertColumn.value) {
            baTable.initSort()
        }
        // 只有专家智库栏目才启用拖拽排序
        if (isExpertColumn.value) {
            baTable.dragSort()
        }
    })

    // 处理快捷添加参数
    if (route.query.quickAdd === '1') {
        nextTick(() => {
            // 预填充表单数据
            const defaultItems: any = {}

            if (route.query.columnId) {
                defaultItems.column_id = route.query.columnId
            }
            if (route.query.contentType) {
                defaultItems.content_type = route.query.contentType
            }

            // 设置默认值并打开添加表单
            if (baTable.form.defaultItems) {
                Object.assign(baTable.form.defaultItems, defaultItems)
            }
            baTable.toggleForm('Add')
        })
    }
})
</script>

<style scoped lang="scss"></style>
