<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
        :destroy-on-close="true"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '' : 'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem
                        :label="t('content.column_id')"
                        type="remoteSelect"
                        v-model="baTable.form.items!.column_id"
                        prop="column_id"
                        :input-attr="{ pk: 'column.id', field: 'name', remoteUrl: '/admin/Column/index' }"
                        :placeholder="t('Please select field', { field: t('content.column_id') })"
                    />

                    <!-- 内容类型选择 -->
                    <FormItem
                        :label="'内容类型'"
                        type="radio"
                        v-model="baTable.form.items!.content_type"
                        prop="content_type"
                        :input-attr="{
                            content: {
                                SERVICE: '学术服务',
                                EXPERT: '专家智库',
                                JOURNAL: '学术期刊',
                                HEADLINE: '学术头条',
                            },
                        }"
                        :placeholder="'请选择内容类型'"
                    />

                    <!-- 基础字段 -->
                    <FormItem
                        :label="t('content.title')"
                        type="string"
                        v-model="baTable.form.items!.title"
                        prop="title"
                        :placeholder="t('Please input field', { field: t('content.title') })"
                    />
                    <FormItem
                        :label="t('content.subtitle')"
                        type="string"
                        v-model="baTable.form.items!.subtitle"
                        prop="subtitle"
                        :placeholder="t('Please input field', { field: t('content.subtitle') })"
                    />
                    <FormItem
                        :label="t('content.summary')"
                        type="textarea"
                        v-model="baTable.form.items!.summary"
                        prop="summary"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('content.summary') })"
                    />
                    <FormItem :label="t('content.cover_image')" type="image" v-model="baTable.form.items!.cover_image" prop="cover_image" />

                    <!-- 学术服务字段 -->
                    <div v-if="currentColumnType === 'service'">
                        <el-divider content-position="left">学术服务字段</el-divider>
                        <FormItem
                            :label="t('content.service_desc')"
                            type="editor"
                            v-model="baTable.form.items!.service_desc"
                            prop="service_desc"
                            :placeholder="t('Please input field', { field: t('content.service_desc') })"
                        />
                        <FormItem
                            :label="'服务标签'"
                            type="string"
                            v-model="baTable.form.items!.service_tags"
                            prop="service_tags"
                            :placeholder="'请输入服务标签，多个用逗号分隔'"
                        />
                        <FormItem
                            :label="'服务场景'"
                            type="remoteSelect"
                            v-model="baTable.form.items!.service_scenarios"
                            prop="service_scenarios"
                            :input-attr="{ pk: 'id', field: 'cj_name', remoteUrl: '/admin/Changjing/index', multiple: true }"
                            :placeholder="'请选择服务场景'"
                        />
                        <FormItem
                            :label="'服务类型'"
                            type="remoteSelect"
                            v-model="baTable.form.items!.service_types_select"
                            prop="service_types_select"
                            :input-attr="{ pk: 'id', field: 'ft_name', remoteUrl: '/admin/Futype/index', multiple: true }"
                            :placeholder="'请选择服务类型'"
                        />
                        <FormItem
                            :label="'价格'"
                            type="number"
                            v-model="baTable.form.items!.price"
                            prop="price"
                            :input-attr="{ step: 0.01 }"
                            :placeholder="'请输入价格'"
                        />
                        <FormItem
                            :label="'购买数量'"
                            type="number"
                            v-model="baTable.form.items!.purchase_count"
                            prop="purchase_count"
                            :input-attr="{ step: 1 }"
                            :placeholder="'请输入购买数量'"
                        />
                        <FormItem
                            :label="t('content.link_url')"
                            type="string"
                            v-model="baTable.form.items!.link_url"
                            prop="link_url"
                            :placeholder="t('Please input field', { field: t('content.link_url') })"
                        />
                    </div>

                    <!-- 专家智库字段 -->
                    <div v-if="currentColumnType === 'expert'">
                        <el-divider content-position="left">专家智库字段</el-divider>
                        <FormItem
                            :label="'专家姓名'"
                            type="string"
                            v-model="baTable.form.items!.expert_name"
                            prop="expert_name"
                            :placeholder="'请输入专家姓名'"
                        />
                        <FormItem
                            :label="'专家编号'"
                            type="string"
                            v-model="baTable.form.items!.expert_number"
                            prop="expert_number"
                            :placeholder="'请输入专家编号'"
                        />
                        <FormItem
                            :label="t('content.avatar_url')"
                            type="image"
                            v-model="baTable.form.items!.avatar_url"
                            prop="avatar_url"
                            :placeholder="t('Please input field', { field: t('content.avatar_url') })"
                        />
                        <FormItem
                            :label="t('content.institution')"
                            type="string"
                            v-model="baTable.form.items!.institution"
                            prop="institution"
                            :placeholder="t('Please input field', { field: t('content.institution') })"
                        />
                        <FormItem
                            :label="t('content.research_area')"
                            type="string"
                            v-model="baTable.form.items!.research_area"
                            prop="research_area"
                            :placeholder="t('Please input field', { field: t('content.research_area') })"
                        />
                        <FormItem
                            :label="'学术成就'"
                            type="textarea"
                            v-model="baTable.form.items!.academic_achievements"
                            prop="academic_achievements"
                            :input-attr="{ rows: 3 }"
                            :placeholder="'请输入学术成就'"
                        />
                        <FormItem
                            :label="'指导经验'"
                            type="textarea"
                            v-model="baTable.form.items!.tutoring_experience"
                            prop="tutoring_experience"
                            :input-attr="{ rows: 3 }"
                            :placeholder="'请输入指导经验'"
                        />
                        <FormItem
                            :label="'专业技能'"
                            type="textarea"
                            v-model="baTable.form.items!.expertise_skills"
                            prop="expertise_skills"
                            :input-attr="{ rows: 3 }"
                            :placeholder="'请输入专业技能'"
                        />
                        <FormItem
                            :label="'服务类型'"
                            type="string"
                            v-model="baTable.form.items!.service_types"
                            prop="service_types"
                            :placeholder="'请输入服务类型'"
                        />
                    </div>

                    <!-- 学术期刊字段 -->
                    <div v-if="currentColumnType === 'journal'">
                        <el-divider content-position="left">学术期刊字段</el-divider>
                        <FormItem
                            :label="t('content.impact_factor')"
                            type="number"
                            v-model="baTable.form.items!.impact_factor"
                            prop="impact_factor"
                            :input-attr="{ step: 0.01 }"
                            :placeholder="t('Please input field', { field: t('content.impact_factor') })"
                        />
                        <FormItem
                            :label="'大类学科'"
                            type="string"
                            v-model="baTable.form.items!.big_type"
                            prop="big_type"
                            :placeholder="'请输入大类学科'"
                        />
                        <FormItem
                            :label="'小类学科'"
                            type="string"
                            v-model="baTable.form.items!.small_type"
                            prop="small_type"
                            :placeholder="'请输入小类学科'"
                        />
                        <FormItem
                            :label="'期刊类型'"
                            type="string"
                            v-model="baTable.form.items!.journal_type"
                            prop="journal_type"
                            :placeholder="'请输入期刊类型'"
                        />
                        <FormItem
                            :label="'期刊数据库'"
                            type="string"
                            v-model="baTable.form.items!.journal_datatype"
                            prop="journal_datatype"
                            :placeholder="'请输入期刊数据库'"
                        />
                        <FormItem
                            :label="'中科院分区'"
                            type="string"
                            v-model="baTable.form.items!.journal_zky"
                            prop="journal_zky"
                            :placeholder="'请输入中科院分区'"
                        />
                        <FormItem
                            :label="'JCR分区'"
                            type="string"
                            v-model="baTable.form.items!.journal_jcr"
                            prop="journal_jcr"
                            :placeholder="'请输入JCR分区'"
                        />
                    </div>

                    <!-- 学术头条字段 -->
                    <div v-if="currentColumnType === 'headline'">
                        <el-divider content-position="left">学术头条字段</el-divider>
                        <FormItem :label="'作者'" type="string" v-model="baTable.form.items!.author" prop="author" :placeholder="'请输入作者'" />
                        <FormItem
                            :label="'发布日期'"
                            type="date"
                            v-model="baTable.form.items!.publish_date"
                            prop="publish_date"
                            :placeholder="'请选择发布日期'"
                        />
                        <FormItem
                            :label="'所属分类'"
                            type="remoteSelect"
                            v-model="baTable.form.items!.headline_types"
                            prop="headline_types"
                            :input-attr="{ pk: 'id', field: 'name', remoteUrl: '/admin/Toutiaotype/index', multiple: true }"
                            :placeholder="'请选择头条分类'"
                        />
                        <FormItem
                            :label="'点赞数'"
                            type="number"
                            v-model="baTable.form.items!.like_count"
                            prop="like_count"
                            :input-attr="{ step: 1 }"
                            :placeholder="'请输入点赞数'"
                        />
                        <FormItem
                            :label="'收藏数'"
                            type="number"
                            v-model="baTable.form.items!.collect_count"
                            prop="collect_count"
                            :input-attr="{ step: 1 }"
                            :placeholder="'请输入收藏数'"
                        />
                        <FormItem
                            :label="t('content.link_url')"
                            type="string"
                            v-model="baTable.form.items!.link_url"
                            prop="link_url"
                            :placeholder="t('Please input field', { field: t('content.link_url') })"
                        />
                    </div>

                    <!-- 通用字段 -->
                    <FormItem
                        :label="'标签'"
                        type="string"
                        v-model="baTable.form.items!.tags"
                        prop="tags"
                        :placeholder="'请输入标签，多个用逗号分隔'"
                    />

                    <!-- 富文本编辑器 - 和column模块保持一致的实现 -->
                    <el-form-item :label="'内容/正文'" prop="content">
                        <FormItem type="editor" v-model="baTable.form.items!.content" placeholder="请输入内容/正文" />
                    </el-form-item>

                    <FormItem
                        :label="t('content.sort')"
                        type="number"
                        v-model="baTable.form.items!.sort"
                        prop="sort"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('content.sort') })"
                    />
                    <FormItem
                        :label="t('content.status')"
                        type="radio"
                        v-model="baTable.form.items!.status"
                        prop="status"
                        :input-attr="{ content: { '0': t('content.status 0'), '1': t('content.status 1') } }"
                        :placeholder="t('Please select field', { field: t('content.status') })"
                    />
                    <FormItem
                        :label="'首页推荐'"
                        type="radio"
                        v-model="baTable.form.items!.homepage_recommend"
                        prop="homepage_recommend"
                        :input-attr="{ content: { '0': '否', '1': '是' } }"
                        :placeholder="'请选择是否首页推荐'"
                    />
                    <FormItem
                        :label="t('content.click_count')"
                        type="number"
                        v-model="baTable.form.items!.click_count"
                        prop="click_count"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('content.click_count') })"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormItemRule } from 'element-plus'
import { inject, reactive, useTemplateRef, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { buildValidatorData } from '/@/utils/validate'

const config = useConfig()
const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass

const { t } = useI18n()

const rules: Partial<Record<string, FormItemRule[]>> = reactive({
    column_id: [buildValidatorData({ name: 'required', message: '请选择栏目' })],
    content_type: [buildValidatorData({ name: 'required', message: '请选择内容类型' })],
})

// 计算当前栏目类型
const currentColumnType = computed(() => {
    // 直接从content_type获取类型
    const contentType = baTable.form.items?.content_type
    if (contentType) {
        return contentType.toLowerCase()
    }
    return null
})
</script>

<style scoped lang="scss"></style>
