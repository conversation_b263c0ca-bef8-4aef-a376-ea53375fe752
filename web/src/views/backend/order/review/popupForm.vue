<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
        width="70%"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '' : 'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <!-- <FormItem
                        :label="t('order.review.order_id')"
                        type="remoteSelect"
                        v-model="baTable.form.items!.order_id"
                        prop="order_id"
                        :input-attr="{ pk: 'orderlist.id', field: 'order_no', remoteUrl: '/admin/order.Orderlist/index' }"
                        :placeholder="t('Please select field', { field: t('order.review.order_id') })"
                    /> -->
                    <FormItem
                        :label="t('order.review.user_id')"
                        type="remoteSelect"
                        v-model="baTable.form.items!.user_id"
                        prop="user_id"
                        :input-attr="{ pk: 'user.id', field: 'username', remoteUrl: '/admin/user.User/index' }"
                        :placeholder="t('Please select field', { field: t('order.review.user_id') })"
                    />
                    <FormItem
                        :label="t('order.review.service_id')"
                        type="remoteSelect"
                        v-model="baTable.form.items!.service_id"
                        prop="service_id"
                        :input-attr="{
                            pk: 'content.id',
                            field: 'title',
                            remoteUrl: '/admin/Content/index',
                            params: { content_type: 'SERVICE' },
                        }"
                        :placeholder="t('Please select field', { field: t('order.review.service_id') })"
                    />
                    <FormItem
                        :label="t('order.review.rating')"
                        type="number"
                        v-model="baTable.form.items!.rating"
                        prop="rating"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('order.review.rating') })"
                    />
                    <FormItem
                        :label="t('order.review.content')"
                        type="editor"
                        v-model="baTable.form.items!.content"
                        prop="content"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('order.review.content') })"
                    />
                    <!-- <FormItem :label="t('order.review.images')" type="images" v-model="baTable.form.items!.images" prop="images" /> -->
                    <!-- <FormItem
                        :label="t('order.review.reply_content')"
                        type="editor"
                        v-model="baTable.form.items!.reply_content"
                        prop="reply_content"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('order.review.reply_content') })"
                    /> -->
                    <!-- <FormItem
                        :label="t('order.review.reply_time')"
                        type="datetime"
                        v-model="baTable.form.items!.reply_time"
                        prop="reply_time"
                        :placeholder="t('Please select field', { field: t('order.review.reply_time') })"
                    /> -->
                    <FormItem
                        :label="t('order.review.status')"
                        type="radio"
                        v-model="baTable.form.items!.status"
                        prop="status"
                        :input-attr="{ content: { normal: t('order.review.status normal'), hidden: t('order.review.status hidden') } }"
                        :placeholder="t('Please select field', { field: t('order.review.status') })"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormItemRule } from 'element-plus'
import { inject, reactive, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { buildValidatorData } from '/@/utils/validate'

const config = useConfig()
const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass

const { t } = useI18n()

const rules: Partial<Record<string, FormItemRule[]>> = reactive({
    rating: [buildValidatorData({ name: 'number', title: t('order.review.rating') })],
    content: [buildValidatorData({ name: 'editorRequired', title: t('order.review.content') })],
    reply_content: [buildValidatorData({ name: 'editorRequired', title: t('order.review.reply_content') })],
    reply_time: [buildValidatorData({ name: 'date', title: t('order.review.reply_time') })],
    create_time: [buildValidatorData({ name: 'date', title: t('order.review.create_time') })],
    update_time: [buildValidatorData({ name: 'date', title: t('order.review.update_time') })],
})
</script>

<style scoped lang="scss"></style>
