<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '' : 'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem
                        :label="t('order.statuslog.order_id')"
                        type="remoteSelect"
                        v-model="baTable.form.items!.order_id"
                        prop="order_id"
                        :input-attr="{ pk: 'orderlist.id', field: 'order_no', remoteUrl: '/admin/order.Orderlist/index' }"
                        :placeholder="t('Please select field', { field: t('order.statuslog.order_id') })"
                    />
                    <FormItem
                        :label="t('order.statuslog.order_no')"
                        type="string"
                        v-model="baTable.form.items!.order_no"
                        prop="order_no"
                        :placeholder="t('Please input field', { field: t('order.statuslog.order_no') })"
                    />
                    <FormItem
                        :label="t('order.statuslog.from_status')"
                        type="string"
                        v-model="baTable.form.items!.from_status"
                        prop="from_status"
                        :placeholder="t('Please input field', { field: t('order.statuslog.from_status') })"
                    />
                    <FormItem
                        :label="t('order.statuslog.to_status')"
                        type="string"
                        v-model="baTable.form.items!.to_status"
                        prop="to_status"
                        :placeholder="t('Please input field', { field: t('order.statuslog.to_status') })"
                    />
                    <FormItem
                        :label="t('order.statuslog.operate_type')"
                        type="radio"
                        v-model="baTable.form.items!.operate_type"
                        prop="operate_type"
                        :input-attr="{
                            content: {
                                user: t('order.statuslog.operate_type user'),
                                admin: t('order.statuslog.operate_type admin'),
                                system: t('order.statuslog.operate_type system'),
                                payment: t('order.statuslog.operate_type payment'),
                            },
                        }"
                        :placeholder="t('Please select field', { field: t('order.statuslog.operate_type') })"
                    />
                    <FormItem
                        :label="t('order.statuslog.operator_id')"
                        type="remoteSelect"
                        v-model="baTable.form.items!.operator_id"
                        prop="operator_id"
                        :input-attr="{ pk: 'id', field: 'name', remoteUrl: '' }"
                        :placeholder="t('Please select field', { field: t('order.statuslog.operator_id') })"
                    />
                    <FormItem
                        :label="t('order.statuslog.operator_name')"
                        type="string"
                        v-model="baTable.form.items!.operator_name"
                        prop="operator_name"
                        :placeholder="t('Please input field', { field: t('order.statuslog.operator_name') })"
                    />
                    <FormItem
                        :label="t('order.statuslog.remark')"
                        type="string"
                        v-model="baTable.form.items!.remark"
                        prop="remark"
                        :placeholder="t('Please input field', { field: t('order.statuslog.remark') })"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormItemRule } from 'element-plus'
import { inject, reactive, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { buildValidatorData } from '/@/utils/validate'

const config = useConfig()
const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass

const { t } = useI18n()

const rules: Partial<Record<string, FormItemRule[]>> = reactive({
    create_time: [buildValidatorData({ name: 'date', title: t('order.statuslog.create_time') })],
})
</script>

<style scoped lang="scss"></style>
