<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '' : 'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem
                        :label="t('order.orderlist.order_no')"
                        type="string"
                        v-model="baTable.form.items!.order_no"
                        prop="order_no"
                        :placeholder="t('Please input field', { field: t('order.orderlist.order_no') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.user_id')"
                        type="remoteSelect"
                        v-model="baTable.form.items!.user_id"
                        prop="user_id"
                        :input-attr="{ pk: 'user.id', field: 'username', remoteUrl: '/admin/user.User/index' }"
                        :placeholder="t('Please select field', { field: t('order.orderlist.user_id') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.service_id')"
                        type="remoteSelect"
                        v-model="baTable.form.items!.service_id"
                        prop="service_id"
                        :input-attr="{ pk: 'content.id', field: 'title', remoteUrl: '/admin/Content/index' }"
                        :placeholder="t('Please select field', { field: t('order.orderlist.service_id') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.service_title')"
                        type="string"
                        v-model="baTable.form.items!.service_title"
                        prop="service_title"
                        :placeholder="t('Please input field', { field: t('order.orderlist.service_title') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.service_desc')"
                        type="textarea"
                        v-model="baTable.form.items!.service_desc"
                        prop="service_desc"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('order.orderlist.service_desc') })"
                    />
                    <FormItem :label="t('order.orderlist.cover_image')" type="image" v-model="baTable.form.items!.cover_image" prop="cover_image" />
                    <FormItem
                        :label="t('order.orderlist.contact_mobile')"
                        type="string"
                        v-model="baTable.form.items!.contact_mobile"
                        prop="contact_mobile"
                        :placeholder="t('Please input field', { field: t('order.orderlist.contact_mobile') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.contact_email')"
                        type="string"
                        v-model="baTable.form.items!.contact_email"
                        prop="contact_email"
                        :placeholder="t('Please input field', { field: t('order.orderlist.contact_email') })"
                    />
                    <!-- <FormItem
                        :label="t('order.orderlist.contact_qq')"
                        type="string"
                        v-model="baTable.form.items!.contact_qq"
                        prop="contact_qq"
                        :placeholder="t('Please input field', { field: t('order.orderlist.contact_qq') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.contact_wechat')"
                        type="string"
                        v-model="baTable.form.items!.contact_wechat"
                        prop="contact_wechat"
                        :placeholder="t('Please input field', { field: t('order.orderlist.contact_wechat') })"
                    /> -->
                    <FormItem
                        :label="t('order.orderlist.service_price')"
                        type="number"
                        v-model="baTable.form.items!.service_price"
                        prop="service_price"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('order.orderlist.service_price') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.discount_amount')"
                        type="number"
                        v-model="baTable.form.items!.discount_amount"
                        prop="discount_amount"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('order.orderlist.discount_amount') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.total_amount')"
                        type="number"
                        v-model="baTable.form.items!.total_amount"
                        prop="total_amount"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('order.orderlist.total_amount') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.status')"
                        type="radio"
                        v-model="baTable.form.items!.status"
                        prop="status"
                        :input-attr="{
                            content: {
                                pending: t('order.orderlist.status pending'),
                                paid: t('order.orderlist.status paid'),
                                in_progress: t('order.orderlist.status in_progress'),
                                completed: t('order.orderlist.status completed'),
                                cancelled: t('order.orderlist.status cancelled'),
                                refunded: t('order.orderlist.status refunded'),
                            },
                        }"
                        :placeholder="t('Please select field', { field: t('order.orderlist.status') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.payment_status')"
                        type="radio"
                        v-model="baTable.form.items!.payment_status"
                        prop="payment_status"
                        :input-attr="{
                            content: {
                                unpaid: t('order.orderlist.payment_status unpaid'),
                                paying: t('order.orderlist.payment_status paying'),
                                paid: t('order.orderlist.payment_status paid'),
                                failed: t('order.orderlist.payment_status failed'),
                                refunded: t('order.orderlist.payment_status refunded'),
                            },
                        }"
                        :placeholder="t('Please select field', { field: t('order.orderlist.payment_status') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.payment_method')"
                        type="radio"
                        v-model="baTable.form.items!.payment_method"
                        prop="payment_method"
                        :input-attr="{
                            content: {
                                wechat: t('order.orderlist.payment_method wechat'),
                                alipay: t('order.orderlist.payment_method alipay'),
                                balance: t('order.orderlist.payment_method balance'),
                                other: t('order.orderlist.payment_method other'),
                            },
                        }"
                        :placeholder="t('Please select field', { field: t('order.orderlist.payment_method') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.trade_no')"
                        type="string"
                        v-model="baTable.form.items!.trade_no"
                        prop="trade_no"
                        :placeholder="t('Please input field', { field: t('order.orderlist.trade_no') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.out_trade_no')"
                        type="string"
                        v-model="baTable.form.items!.out_trade_no"
                        prop="out_trade_no"
                        :placeholder="t('Please input field', { field: t('order.orderlist.out_trade_no') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.payment_time')"
                        type="datetime"
                        v-model="baTable.form.items!.payment_time"
                        prop="payment_time"
                        :placeholder="t('Please select field', { field: t('order.orderlist.payment_time') })"
                    />
                    <!-- <FormItem
                        :label="t('order.orderlist.payment_data')"
                        type="select"
                        v-model="baTable.form.items!.payment_data"
                        prop="payment_data"
                        :input-attr="{ content: {} }"
                        :placeholder="t('Please select field', { field: t('order.orderlist.payment_data') })"
                    /> -->
                    <FormItem
                        :label="t('order.orderlist.user_remark')"
                        type="textarea"
                        v-model="baTable.form.items!.user_remark"
                        prop="user_remark"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('order.orderlist.user_remark') })"
                    />
                    <FormItem
                        :label="t('order.orderlist.admin_remark')"
                        type="textarea"
                        v-model="baTable.form.items!.admin_remark"
                        prop="admin_remark"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('order.orderlist.admin_remark') })"
                    />
                    <!-- <FormItem
                        :label="t('order.orderlist.expire_time')"
                        type="datetime"
                        v-model="baTable.form.items!.expire_time"
                        prop="expire_time"
                        :placeholder="t('Please select field', { field: t('order.orderlist.expire_time') })"
                    /> -->
                    <!-- <FormItem
                        :label="t('order.orderlist.delete_time')"
                        type="datetime"
                        v-model="baTable.form.items!.delete_time"
                        prop="delete_time"
                        :placeholder="t('Please select field', { field: t('order.orderlist.delete_time') })"
                    /> -->
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormItemRule } from 'element-plus'
import { inject, reactive, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { buildValidatorData } from '/@/utils/validate'

const config = useConfig()
const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass

const { t } = useI18n()

const rules: Partial<Record<string, FormItemRule[]>> = reactive({
    service_price: [buildValidatorData({ name: 'number', title: t('order.orderlist.service_price') })],
    discount_amount: [buildValidatorData({ name: 'number', title: t('order.orderlist.discount_amount') })],
    total_amount: [buildValidatorData({ name: 'number', title: t('order.orderlist.total_amount') })],
    payment_time: [buildValidatorData({ name: 'date', title: t('order.orderlist.payment_time') })],
    expire_time: [buildValidatorData({ name: 'date', title: t('order.orderlist.expire_time') })],
    create_time: [buildValidatorData({ name: 'date', title: t('order.orderlist.create_time') })],
    update_time: [buildValidatorData({ name: 'date', title: t('order.orderlist.update_time') })],
    delete_time: [buildValidatorData({ name: 'date', title: t('order.orderlist.delete_time') })],
})
</script>

<style scoped lang="scss"></style>
