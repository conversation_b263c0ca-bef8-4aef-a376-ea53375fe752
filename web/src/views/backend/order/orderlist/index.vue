<template>
    <div class="default-main ba-table-box">
        <el-alert class="ba-table-alert" v-if="baTable.table.remark" :title="baTable.table.remark" type="info" show-icon />

        <!-- 状态筛选tabs -->
        <div class="order-status-tabs">
            <el-tabs v-model="activeTab" @tab-click="handleTabChange" class="mb-4">
                <el-tab-pane :label="`全部 (${statusCounts.total})`" name="all"></el-tab-pane>
                <el-tab-pane :label="`待付款 (${statusCounts.pending})`" name="pending"></el-tab-pane>
                <el-tab-pane :label="`已付款 (${statusCounts.paid})`" name="paid"></el-tab-pane>
                <el-tab-pane :label="`进行中 (${statusCounts.in_progress})`" name="in_progress"></el-tab-pane>
                <el-tab-pane :label="`已完成 (${statusCounts.completed})`" name="completed"></el-tab-pane>
                <el-tab-pane :label="`已取消 (${statusCounts.cancelled})`" name="cancelled"></el-tab-pane>
                <el-tab-pane :label="`已退款 (${statusCounts.refunded})`" name="refunded"></el-tab-pane>
            </el-tabs>
        </div>

        <!-- 表格顶部菜单 -->
        <!-- 自定义按钮请使用插槽，甚至公共搜索也可以使用具名插槽渲染，参见文档 -->
        <TableHeader
            :buttons="['refresh', 'add', 'edit', 'delete', 'comSearch', 'quickSearch', 'columnDisplay']"
            :quick-search-placeholder="t('Quick search placeholder', { fields: t('order.orderlist.quick Search Fields') })"
        ></TableHeader>

        <!-- 表格 -->
        <!-- 表格列有多种自定义渲染方式，比如自定义组件、具名插槽等，参见文档 -->
        <!-- 要使用 el-table 组件原有的属性，直接加在 Table 标签上即可 -->
        <Table ref="tableRef"></Table>

        <!-- 表单 -->
        <PopupForm />

        <!-- 状态更新模态框 -->
        <el-dialog v-model="statusModalVisible" title="更新订单状态" width="450px" :before-close="closeStatusModal" center>
            <div v-if="currentOrder" class="status-modal-content">
                <div class="order-info">
                    <div class="info-row">
                        <span class="label">订单号：</span>
                        <span class="value">{{ currentOrder.order_no }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">当前状态：</span>
                        <el-tag :type="currentOrder.status === 'paid' ? 'primary' : currentOrder.status === 'in_progress' ? 'warning' : 'success'">
                            {{ currentOrder.status === 'paid' ? '已付款' : currentOrder.status === 'in_progress' ? '进行中' : '已完成' }}
                        </el-tag>
                    </div>
                </div>

                <div class="status-options">
                    <div class="options-title">选择新状态：</div>
                    <div class="status-buttons">
                        <el-button
                            v-if="currentOrder.status !== 'in_progress'"
                            type="warning"
                            @click="updateOrderStatus(currentOrder.id, 'in_progress')"
                            size="large"
                        >
                            <i class="fa fa-play"></i>
                            <span>设为进行中</span>
                        </el-button>

                        <el-button
                            v-if="currentOrder.status !== 'completed'"
                            type="success"
                            @click="updateOrderStatus(currentOrder.id, 'completed')"
                            size="large"
                        >
                            <i class="fa fa-check"></i>
                            <span>设为已完成</span>
                        </el-button>

                        <el-button
                            v-if="currentOrder.status !== 'cancelled'"
                            type="danger"
                            @click="updateOrderStatus(currentOrder.id, 'cancelled')"
                            size="large"
                        >
                            <i class="fa fa-times"></i>
                            <span>设为已取消</span>
                        </el-button>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeStatusModal">取消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { onMounted, provide, ref, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import PopupForm from './popupForm.vue'
import { baTableApi } from '/@/api/common'
import { defaultOptButtons } from '/@/components/table'
import TableHeader from '/@/components/table/header/index.vue'
import Table from '/@/components/table/index.vue'
import baTableClass from '/@/utils/baTable'
import createAxios from '/@/utils/axios'

defineOptions({
    name: 'order/orderlist',
})

const { t } = useI18n()
const tableRef = useTemplateRef('tableRef')
const optButtons: OptButton[] = defaultOptButtons(['edit', 'delete'])

// 状态筛选相关
const activeTab = ref('all')
const statusCounts = ref({
    total: 0,
    pending: 0,
    paid: 0,
    in_progress: 0,
    completed: 0,
    cancelled: 0,
    refunded: 0,
})

// 获取状态统计数据
const getStatusCounts = async () => {
    try {
        const response = await createAxios(
            {
                url: '/admin/order.Orderlist/getStatusStats',
                method: 'post',
                data: {},
            },
            {
                showSuccessMessage: false,
            }
        )
        if (response.code === 1) {
            statusCounts.value = response.data
        }
    } catch (error) {
        console.error('获取状态统计失败:', error)
        // 如果接口调用失败，设置默认值
        statusCounts.value = {
            total: 0,
            pending: 0,
            paid: 0,
            in_progress: 0,
            completed: 0,
            cancelled: 0,
            refunded: 0,
        }
    }
}

// tab切换事件
const handleTabChange = (tab: any) => {
    const status = tab.props.name
    if (status === 'all') {
        // 清除状态筛选
        baTable.table.filter!.status = undefined
    } else {
        // 设置状态筛选
        baTable.table.filter!.status = status
    }
    // 重新获取表格数据（后置钩子会自动更新统计数据）
    baTable.getData()
}

// 状态模态框相关
const statusModalVisible = ref(false)
const currentOrder = ref<any>(null)

// 打开状态选择模态框
const openStatusModal = (row: any) => {
    currentOrder.value = row
    statusModalVisible.value = true
}

// 关闭状态模态框
const closeStatusModal = () => {
    statusModalVisible.value = false
    currentOrder.value = null
}

// 更新订单状态
const updateOrderStatus = (orderId: number, newStatus: string) => {
    createAxios(
        {
            url: '/admin/order.Orderlist/updateStatus',
            method: 'post',
            data: {
                id: orderId,
                status: newStatus,
            },
        },
        {
            showSuccessMessage: true,
        }
    )
        .then((response) => {
            if (response.code === 1) {
                // 关闭模态框
                closeStatusModal()
                // 刷新表格数据
                baTable.getData()
            }
        })
        .catch((error) => {
            console.error('更新订单状态失败:', error)
        })
}

/**
 * baTable 内包含了表格的所有数据且数据具备响应性，然后通过 provide 注入给了后代组件
 */
const baTable = new baTableClass(
    new baTableApi('/admin/order.Orderlist/'),
    {
        pk: 'id',
        column: [
            { type: 'selection', align: 'center', operator: false },
            { label: t('order.orderlist.id'), prop: 'id', align: 'center', width: 70, operator: 'RANGE', sortable: 'custom' },
            {
                label: t('order.orderlist.order_no'),
                prop: 'order_no',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },

            {
                label: t('order.orderlist.user__username'),
                prop: 'user.username',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                render: 'tags',
                operator: 'LIKE',
            },
            { label: t('order.orderlist.service_id'), prop: 'service_id', align: 'center', operatorPlaceholder: t('Fuzzy query'), operator: 'LIKE' },

            {
                label: t('order.orderlist.service_title'),
                prop: 'service_title',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },
            { label: t('order.orderlist.cover_image'), prop: 'cover_image', align: 'center', render: 'image', operator: false },
            {
                label: t('order.orderlist.contact_mobile'),
                prop: 'contact_mobile',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },
            {
                label: t('联系人'),
                prop: 'contact_name',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },

            {
                label: t('order.orderlist.contact_mobile'),
                prop: 'contact_mobile',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },
            {
                label: t('order.orderlist.contact_email'),
                prop: 'contact_email',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },
            // {
            //     label: t('order.orderlist.contact_qq'),
            //     prop: 'contact_qq',
            //     align: 'center',
            //     operatorPlaceholder: t('Fuzzy query'),
            //     operator: 'LIKE',
            //     sortable: false,
            // },
            // {
            //     label: t('order.orderlist.contact_wechat'),
            //     prop: 'contact_wechat',
            //     align: 'center',
            //     operatorPlaceholder: t('Fuzzy query'),
            //     operator: 'LIKE',
            //     sortable: false,
            // },
            // { label: t('order.orderlist.service_price'), prop: 'service_price', align: 'center', operator: 'RANGE', sortable: false },
            { label: t('order.orderlist.discount_amount'), prop: 'discount_amount', align: 'center', operator: 'RANGE', sortable: false },
            { label: t('order.orderlist.total_amount'), prop: 'total_amount', align: 'center', operator: 'RANGE', sortable: false },
            {
                label: t('order.orderlist.status'),
                prop: 'status',
                align: 'center',
                render: 'tag',
                operator: 'eq',
                sortable: false,
                custom: {
                    pending: 'warning', // 橙色 - 待付款
                    paid: 'primary', // 蓝色 - 已付款
                    in_progress: 'success', // 绿色 - 进行中
                    completed: 'success', // 绿色 - 已完成
                    cancelled: 'danger', // 红色 - 已取消
                    refunded: 'info', // 灰色 - 已退款
                },
                replaceValue: {
                    pending: t('order.orderlist.status pending'),
                    paid: t('order.orderlist.status paid'),
                    in_progress: t('order.orderlist.status in_progress'),
                    completed: t('order.orderlist.status completed'),
                    cancelled: t('order.orderlist.status cancelled'),
                    refunded: t('order.orderlist.status refunded'),
                },
            },
            {
                label: t('order.orderlist.payment_status'),
                prop: 'payment_status',
                align: 'center',
                render: 'tag',
                operator: 'eq',
                sortable: false,
                custom: {
                    unpaid: 'warning', // 橙色 - 未支付
                    paying: 'primary', // 蓝色 - 支付中
                    paid: 'success', // 绿色 - 已支付
                    failed: 'danger', // 红色 - 支付失败
                    refunded: 'info', // 灰色 - 已退款
                },
                replaceValue: {
                    unpaid: t('order.orderlist.payment_status unpaid'),
                    paying: t('order.orderlist.payment_status paying'),
                    paid: t('order.orderlist.payment_status paid'),
                    failed: t('order.orderlist.payment_status failed'),
                    refunded: t('order.orderlist.payment_status refunded'),
                },
            },
            {
                label: t('order.orderlist.payment_method'),
                prop: 'payment_method',
                align: 'center',
                render: 'tag',
                operator: 'eq',
                sortable: false,
                replaceValue: {
                    wechat: t('order.orderlist.payment_method wechat'),
                    alipay: t('order.orderlist.payment_method alipay'),
                    balance: t('order.orderlist.payment_method balance'),
                    other: t('order.orderlist.payment_method other'),
                },
            },
            {
                label: t('状态操作'),
                align: 'center',
                width: 100,
                render: 'buttons',
                buttons: [
                    {
                        name: 'updateStatus',
                        render: 'tipButton',
                        title: '处理订单',
                        type: 'primary',
                        icon: 'fa fa-edit',
                        text: '处理订单',
                        click: (row: any) => openStatusModal(row),
                        display: (row: any) => ['paid', 'in_progress'].includes(row.status),
                    },
                ],
                operator: false,
            },
            {
                label: t('order.orderlist.trade_no'),
                prop: 'trade_no',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },
            // {
            //     label: t('order.orderlist.out_trade_no'),
            //     prop: 'out_trade_no',
            //     align: 'center',
            //     operatorPlaceholder: t('Fuzzy query'),
            //     operator: 'LIKE',
            //     sortable: false,
            // },
            {
                label: t('order.orderlist.payment_time'),
                prop: 'payment_time',
                align: 'center',
                render: 'datetime',
                operator: 'RANGE',
                sortable: 'custom',
                width: 160,
                timeFormat: 'yyyy-mm-dd hh:MM:ss',
            },
            // {
            //     label: t('order.orderlist.payment_data'),
            //     prop: 'payment_data',
            //     align: 'center',
            //     render: 'tag',
            //     operator: 'eq',
            //     sortable: false,
            //     replaceValue: {},
            // },
            // {
            //     label: t('order.orderlist.expire_time'),
            //     prop: 'expire_time',
            //     align: 'center',
            //     render: 'datetime',
            //     operator: 'RANGE',
            //     sortable: 'custom',
            //     width: 160,
            //     timeFormat: 'yyyy-mm-dd hh:MM:ss',
            // },
            {
                label: t('order.orderlist.create_time'),
                prop: 'create_time',
                align: 'center',
                render: 'datetime',
                operator: 'RANGE',
                sortable: 'custom',
                width: 160,
                timeFormat: 'yyyy-mm-dd hh:MM:ss',
            },
            {
                label: t('order.orderlist.update_time'),
                prop: 'update_time',
                align: 'center',
                render: 'datetime',
                operator: 'RANGE',
                sortable: 'custom',
                width: 160,
                timeFormat: 'yyyy-mm-dd hh:MM:ss',
            },
            // {
            //     label: t('order.orderlist.delete_time'),
            //     prop: 'delete_time',
            //     align: 'center',
            //     render: 'datetime',
            //     operator: 'RANGE',
            //     sortable: 'custom',
            //     width: 160,
            //     timeFormat: 'yyyy-mm-dd hh:MM:ss',
            // },

            { label: t('Operate'), align: 'center', width: 100, render: 'buttons', buttons: optButtons, operator: false },
        ],
        dblClickNotEditColumn: [undefined],
    },
    {
        defaultItems: { status: 'pending', payment_status: 'unpaid' },
    }
)

provide('baTable', baTable)

onMounted(() => {
    baTable.table.ref = tableRef.value
    baTable.mount()

    // 设置后置钩子：每次获取数据后都更新统计
    baTable.after.getData = () => {
        getStatusCounts()
    }

    baTable.getData()?.then(() => {
        baTable.initSort()
        baTable.dragSort()
    })
})
</script>

<style scoped lang="scss">
.order-status-tabs {
    margin-bottom: 16px;

    :deep(.el-tabs__header) {
        margin-bottom: 0;
    }

    :deep(.el-tabs__nav-wrap::after) {
        height: 1px;
    }

    :deep(.el-tabs__item) {
        font-weight: 500;

        &.is-active {
            color: var(--el-color-primary);
            font-weight: 600;
        }
    }
}

.mb-4 {
    margin-bottom: 1rem;
}

/* 模态框专用样式 - 使用更高优先级 */
.el-dialog .status-modal-content {
    .order-info {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid var(--el-color-primary);
        margin-bottom: 24px;

        .info-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }

            .label {
                font-weight: 600;
                color: #606266;
                min-width: 80px;
                margin-right: 12px;
            }

            .value {
                color: #303133;
                font-family: monospace;
            }
        }
    }

    .status-options {
        .options-title {
            font-weight: 600;
            color: #303133;
            margin-bottom: 16px;
            font-size: 16px;
        }

        .status-buttons {
            display: flex !important;
            flex-direction: column !important;
            gap: 12px;

            .el-button {
                width: 100% !important;
                height: 48px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: flex-start !important;
                padding: 0 !important;
                padding-left: 24px !important;
                padding-right: 24px !important;
                margin: 0 !important;
                font-size: 14px !important;
                box-sizing: border-box !important;

                /* 覆盖Element Plus默认样式 */
                &.el-button--large {
                    height: 48px !important;
                    padding: 0 !important;
                    padding-left: 24px !important;
                    padding-right: 24px !important;
                }

                i {
                    font-size: 16px !important;
                    width: 20px !important;
                    text-align: center !important;
                    margin-right: 12px !important;
                    margin-left: 0 !important;
                    flex-shrink: 0 !important;
                    display: inline-block !important;
                }

                span {
                    font-weight: 500 !important;
                    flex: 1 !important;
                    text-align: left !important;
                    display: inline-block !important;
                }

                /* 确保图标和文字在同一行 */
                > * {
                    vertical-align: middle;
                }

                /* 重置可能的样式冲突 */
                &:hover,
                &:focus,
                &:active {
                    transform: none !important;
                }

                /* 重置Element Plus按钮内部样式 */
                .el-button__text {
                    display: flex !important;
                    align-items: center !important;
                    width: 100% !important;
                }
            }
        }
    }
}
</style>
