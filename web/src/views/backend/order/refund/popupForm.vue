<template>
    <!-- 对话框表单 -->
    <!-- 建议使用 Prettier 格式化代码 -->
    <!-- el-form 内可以混用 el-form-item、FormItem、ba-input 等输入组件 -->
    <el-dialog
        class="ba-operate-dialog"
        :close-on-click-modal="false"
        :model-value="['Add', 'Edit'].includes(baTable.form.operate!)"
        @close="baTable.toggleForm"
    >
        <template #header>
            <div class="title" v-drag="['.ba-operate-dialog', '.el-dialog__header']" v-zoom="'.ba-operate-dialog'">
                {{ baTable.form.operate ? t(baTable.form.operate) : '' }}
            </div>
        </template>
        <el-scrollbar v-loading="baTable.form.loading" class="ba-table-form-scrollbar">
            <div
                class="ba-operate-form"
                :class="'ba-' + baTable.form.operate + '-form'"
                :style="config.layout.shrink ? '' : 'width: calc(100% - ' + baTable.form.labelWidth! / 2 + 'px)'"
            >
                <el-form
                    v-if="!baTable.form.loading"
                    ref="formRef"
                    @submit.prevent=""
                    @keyup.enter="baTable.onSubmit(formRef)"
                    :model="baTable.form.items"
                    :label-position="config.layout.shrink ? 'top' : 'right'"
                    :label-width="baTable.form.labelWidth + 'px'"
                    :rules="rules"
                >
                    <FormItem
                        :label="t('order.refund.order_id')"
                        type="remoteSelect"
                        v-model="baTable.form.items!.order_id"
                        prop="order_id"
                        :input-attr="{ pk: 'orderlist.id', field: 'order_no', remoteUrl: '/admin/order.Orderlist/index' }"
                        :placeholder="t('Please select field', { field: t('order.refund.order_id') })"
                    />
                    <FormItem
                        :label="t('order.refund.order_no')"
                        type="string"
                        v-model="baTable.form.items!.order_no"
                        prop="order_no"
                        :placeholder="t('Please input field', { field: t('order.refund.order_no') })"
                    />
                    <FormItem
                        :label="t('order.refund.user_id')"
                        type="remoteSelect"
                        v-model="baTable.form.items!.user_id"
                        prop="user_id"
                        :input-attr="{ pk: 'user.id', field: 'username', remoteUrl: '/admin/user.User/index' }"
                        :placeholder="t('Please select field', { field: t('order.refund.user_id') })"
                    />
                    <FormItem
                        :label="t('order.refund.refund_no')"
                        type="string"
                        v-model="baTable.form.items!.refund_no"
                        prop="refund_no"
                        :placeholder="t('Please input field', { field: t('order.refund.refund_no') })"
                    />
                    <FormItem
                        :label="t('order.refund.refund_type')"
                        type="radio"
                        v-model="baTable.form.items!.refund_type"
                        prop="refund_type"
                        :input-attr="{
                            content: {
                                refund_only: t('order.refund.refund_type refund_only'),
                                return_refund: t('order.refund.refund_type return_refund'),
                                exchange: t('order.refund.refund_type exchange'),
                            },
                        }"
                        :placeholder="t('Please select field', { field: t('order.refund.refund_type') })"
                    />
                    <FormItem
                        :label="t('order.refund.refund_amount')"
                        type="number"
                        v-model="baTable.form.items!.refund_amount"
                        prop="refund_amount"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('order.refund.refund_amount') })"
                    />
                    <FormItem
                        :label="t('order.refund.reason')"
                        type="string"
                        v-model="baTable.form.items!.reason"
                        prop="reason"
                        :placeholder="t('Please input field', { field: t('order.refund.reason') })"
                    />
                    <FormItem
                        :label="t('order.refund.description')"
                        type="textarea"
                        v-model="baTable.form.items!.description"
                        prop="description"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('order.refund.description') })"
                    />
                    <FormItem
                        :label="t('order.refund.evidence_images')"
                        type="images"
                        v-model="baTable.form.items!.evidence_images"
                        prop="evidence_images"
                    />
                    <FormItem
                        :label="t('order.refund.status')"
                        type="radio"
                        v-model="baTable.form.items!.status"
                        prop="status"
                        :input-attr="{
                            content: {
                                pending: t('order.refund.status pending'),
                                approved: t('order.refund.status approved'),
                                rejected: t('order.refund.status rejected'),
                                processing: t('order.refund.status processing'),
                                completed: t('order.refund.status completed'),
                                cancelled: t('order.refund.status cancelled'),
                            },
                        }"
                        :placeholder="t('Please select field', { field: t('order.refund.status') })"
                    />
                    <FormItem
                        :label="t('order.refund.admin_remark')"
                        type="textarea"
                        v-model="baTable.form.items!.admin_remark"
                        prop="admin_remark"
                        :input-attr="{ rows: 3 }"
                        @keyup.enter.stop=""
                        @keyup.ctrl.enter="baTable.onSubmit(formRef)"
                        :placeholder="t('Please input field', { field: t('order.refund.admin_remark') })"
                    />
                    <FormItem
                        :label="t('order.refund.process_time')"
                        type="datetime"
                        v-model="baTable.form.items!.process_time"
                        prop="process_time"
                        :placeholder="t('Please select field', { field: t('order.refund.process_time') })"
                    />
                    <FormItem
                        :label="t('order.refund.completion_time')"
                        type="datetime"
                        v-model="baTable.form.items!.completion_time"
                        prop="completion_time"
                        :placeholder="t('Please select field', { field: t('order.refund.completion_time') })"
                    />
                    <FormItem
                        :label="t('order.refund.actual_refund_amount')"
                        type="number"
                        v-model="baTable.form.items!.actual_refund_amount"
                        prop="actual_refund_amount"
                        :input-attr="{ step: 1 }"
                        :placeholder="t('Please input field', { field: t('order.refund.actual_refund_amount') })"
                    />
                    <FormItem
                        :label="t('order.refund.refund_method')"
                        type="string"
                        v-model="baTable.form.items!.refund_method"
                        prop="refund_method"
                        :placeholder="t('Please input field', { field: t('order.refund.refund_method') })"
                    />
                    <FormItem
                        :label="t('order.refund.refund_transaction_id')"
                        type="string"
                        v-model="baTable.form.items!.refund_transaction_id"
                        prop="refund_transaction_id"
                        :placeholder="t('Please input field', { field: t('order.refund.refund_transaction_id') })"
                    />
                    <FormItem
                        :label="t('order.refund.refund_time')"
                        type="datetime"
                        v-model="baTable.form.items!.refund_time"
                        prop="refund_time"
                        :placeholder="t('Please select field', { field: t('order.refund.refund_time') })"
                    />
                </el-form>
            </div>
        </el-scrollbar>
        <template #footer>
            <div :style="'width: calc(100% - ' + baTable.form.labelWidth! / 1.8 + 'px)'">
                <el-button @click="baTable.toggleForm()">{{ t('Cancel') }}</el-button>
                <el-button v-blur :loading="baTable.form.submitLoading" @click="baTable.onSubmit(formRef)" type="primary">
                    {{ baTable.form.operateIds && baTable.form.operateIds.length > 1 ? t('Save and edit next item') : t('Save') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormItemRule } from 'element-plus'
import { inject, reactive, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import FormItem from '/@/components/formItem/index.vue'
import { useConfig } from '/@/stores/config'
import type baTableClass from '/@/utils/baTable'
import { buildValidatorData } from '/@/utils/validate'

const config = useConfig()
const formRef = useTemplateRef('formRef')
const baTable = inject('baTable') as baTableClass

const { t } = useI18n()

const rules: Partial<Record<string, FormItemRule[]>> = reactive({
    refund_amount: [buildValidatorData({ name: 'number', title: t('order.refund.refund_amount') })],
    process_time: [buildValidatorData({ name: 'date', title: t('order.refund.process_time') })],
    completion_time: [buildValidatorData({ name: 'date', title: t('order.refund.completion_time') })],
    actual_refund_amount: [buildValidatorData({ name: 'number', title: t('order.refund.actual_refund_amount') })],
    refund_time: [buildValidatorData({ name: 'date', title: t('order.refund.refund_time') })],
    create_time: [buildValidatorData({ name: 'date', title: t('order.refund.create_time') })],
    update_time: [buildValidatorData({ name: 'date', title: t('order.refund.update_time') })],
})
</script>

<style scoped lang="scss"></style>
