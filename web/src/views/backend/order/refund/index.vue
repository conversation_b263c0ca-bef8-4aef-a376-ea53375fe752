<template>
    <div class="default-main ba-table-box">
        <el-alert class="ba-table-alert" v-if="baTable.table.remark" :title="baTable.table.remark" type="info" show-icon />

        <!-- 表格顶部菜单 -->
        <!-- 自定义按钮请使用插槽，甚至公共搜索也可以使用具名插槽渲染，参见文档 -->
        <TableHeader
            :buttons="['refresh', 'add', 'edit', 'delete', 'comSearch', 'quickSearch', 'columnDisplay']"
            :quick-search-placeholder="t('Quick search placeholder', { fields: t('order.refund.quick Search Fields') })"
        ></TableHeader>

        <!-- 表格 -->
        <!-- 表格列有多种自定义渲染方式，比如自定义组件、具名插槽等，参见文档 -->
        <!-- 要使用 el-table 组件原有的属性，直接加在 Table 标签上即可 -->
        <Table ref="tableRef"></Table>

        <!-- 表单 -->
        <PopupForm />

        <!-- 处理退款模态框 -->
        <el-dialog v-model="processModalVisible" title="处理退款申请" width="600px" :before-close="closeProcessModal">
            <div v-if="currentRefund" class="process-modal-content">
                <div class="refund-info">
                    <h4>退款申请信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>退款单号：</label>
                            <span>{{ currentRefund.refund_no }}</span>
                        </div>
                        <div class="info-item">
                            <label>订单号：</label>
                            <span>{{ currentRefund.order_no }}</span>
                        </div>
                        <div class="info-item">
                            <label>退款类型：</label>
                            <el-tag>{{ getRefundTypeText(currentRefund.refund_type) }}</el-tag>
                        </div>
                        <div class="info-item">
                            <label>申请金额：</label>
                            <span class="amount">¥{{ currentRefund.refund_amount }}</span>
                        </div>
                        <div class="info-item">
                            <label>申请原因：</label>
                            <span>{{ currentRefund.reason }}</span>
                        </div>
                        <div class="info-item" v-if="currentRefund.description">
                            <label>详细说明：</label>
                            <span>{{ currentRefund.description }}</span>
                        </div>
                    </div>
                </div>

                <el-form :model="processForm" ref="processFormRef" label-width="100px">
                    <el-form-item label="处理结果" prop="action" :rules="[{ required: true, message: '请选择处理结果' }]">
                        <el-radio-group v-model="processForm.action">
                            <el-radio value="approve">同意退款</el-radio>
                            <el-radio value="reject">拒绝退款</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item
                        v-if="processForm.action === 'approve'"
                        label="实际退款金额"
                        prop="actual_refund_amount"
                        :rules="[{ required: true, message: '请输入实际退款金额' }]"
                    >
                        <el-input
                            v-model="processForm.actual_refund_amount"
                            placeholder="请输入实际退款金额"
                            type="number"
                            :max="currentRefund.refund_amount"
                        >
                            <template #prepend>¥</template>
                        </el-input>
                        <div class="form-tip">最大可退款金额：¥{{ currentRefund.refund_amount }}</div>
                    </el-form-item>

                    <el-form-item label="处理备注" prop="admin_remark">
                        <el-input
                            v-model="processForm.admin_remark"
                            type="textarea"
                            :rows="3"
                            :placeholder="processForm.action === 'approve' ? '请输入退款备注（可选）' : '请输入拒绝原因'"
                        />
                    </el-form-item>
                </el-form>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeProcessModal">取消</el-button>
                    <el-button type="primary" @click="submitProcess" :loading="submitLoading">确认处理</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { onMounted, provide, ref, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import PopupForm from './popupForm.vue'
import { baTableApi } from '/@/api/common'
import { defaultOptButtons } from '/@/components/table'
import TableHeader from '/@/components/table/header/index.vue'
import Table from '/@/components/table/index.vue'
import baTableClass from '/@/utils/baTable'
import createAxios from '/@/utils/axios'
import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({
    name: 'order/refund',
})

const { t } = useI18n()
const tableRef = useTemplateRef('tableRef')
const optButtons: OptButton[] = defaultOptButtons(['edit', 'delete'])

// 处理退款相关
const processModalVisible = ref(false)
const currentRefund = ref<any>(null)
const processFormRef = useTemplateRef('processFormRef')
const submitLoading = ref(false)
const processForm = ref({
    action: '',
    actual_refund_amount: '',
    admin_remark: '',
})

// 打开处理模态框
const openProcessModal = (row: any) => {
    currentRefund.value = row
    processForm.value = {
        action: '',
        actual_refund_amount: row.refund_amount,
        admin_remark: '',
    }
    processModalVisible.value = true
}

// 关闭处理模态框
const closeProcessModal = () => {
    processModalVisible.value = false
    currentRefund.value = null
    processForm.value = {
        action: '',
        actual_refund_amount: '',
        admin_remark: '',
    }
}

// 获取退款类型文本
const getRefundTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
        refund_only: '仅退款',
        return_refund: '退货退款',
        exchange: '换货',
    }
    return typeMap[type] || type
}

// 提交处理
const submitProcess = async () => {
    if (!processFormRef.value) return

    try {
        await processFormRef.value.validate()

        if (processForm.value.action === 'reject' && !processForm.value.admin_remark) {
            ElMessage.error('拒绝退款时必须填写拒绝原因')
            return
        }

        submitLoading.value = true

        const response = await createAxios(
            {
                url: '/admin/order.Refund/processRefund',
                method: 'post',
                data: {
                    refund_id: currentRefund.value.id,
                    action: processForm.value.action,
                    actual_refund_amount: processForm.value.actual_refund_amount,
                    admin_remark: processForm.value.admin_remark,
                },
            },
            {
                showSuccessMessage: true,
            }
        )

        if (response.code === 1) {
            closeProcessModal()
            baTable.getData() // 刷新表格数据
        }
    } catch (error) {
        console.error('处理退款失败:', error)
    } finally {
        submitLoading.value = false
    }
}

/**
 * baTable 内包含了表格的所有数据且数据具备响应性，然后通过 provide 注入给了后代组件
 */
const baTable = new baTableClass(
    new baTableApi('/admin/order.Refund/'),
    {
        pk: 'id',
        column: [
            { type: 'selection', align: 'center', operator: false },
            { label: t('order.refund.id'), prop: 'id', align: 'center', width: 70, operator: 'RANGE', sortable: 'custom' },
            // { label: t('order.refund.order_id'), prop: 'order_id', align: 'center', operatorPlaceholder: t('Fuzzy query'), operator: 'LIKE' },
            {
                label: t('order.refund.order__service_title'),
                prop: 'order.service_title',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                render: 'tags',
                operator: 'LIKE',
            },
            // {
            //     label: t('order.refund.order__service_desc'),
            //     prop: 'order.service_desc',
            //     align: 'center',
            //     operatorPlaceholder: t('Fuzzy query'),
            //     render: 'tags',
            //     operator: 'LIKE',
            // },
            {
                label: t('order.refund.order__contact_mobile'),
                prop: 'order.contact_mobile',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                render: 'tags',
                operator: 'LIKE',
            },
            {
                label: t('order.refund.order__contact_email'),
                prop: 'order.contact_email',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                render: 'tags',
                operator: 'LIKE',
            },
            // {
            //     label: t('order.refund.order__contact_qq'),
            //     prop: 'order.contact_qq',
            //     align: 'center',
            //     operatorPlaceholder: t('Fuzzy query'),
            //     render: 'tags',
            //     operator: 'LIKE',
            // },
            // {
            //     label: t('order.refund.order__contact_wechat'),
            //     prop: 'order.contact_wechat',
            //     align: 'center',
            //     operatorPlaceholder: t('Fuzzy query'),
            //     render: 'tags',
            //     operator: 'LIKE',
            // },
            // {
            //     label: t('order.refund.order__service_price'),
            //     prop: 'order.service_price',
            //     align: 'center',
            //     operatorPlaceholder: t('Fuzzy query'),
            //     render: 'tags',
            //     operator: 'LIKE',
            // },
            {
                label: t('order.refund.order__total_amount'),
                prop: 'order.total_amount',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                render: 'tags',
                operator: 'LIKE',
            },
            // {
            //     label: t('order.refund.order__create_time'),
            //     prop: 'order.create_time',
            //     align: 'center',
            //     operatorPlaceholder: t('Fuzzy query'),
            //     render: 'tags',
            //     operator: 'LIKE',
            // },
            // {
            //     label: t('order.refund.order__update_time'),
            //     prop: 'order.update_time',
            //     align: 'center',
            //     operatorPlaceholder: t('Fuzzy query'),
            //     render: 'tags',
            //     operator: 'LIKE',
            // },
            {
                label: t('order.refund.order_no'),
                prop: 'order_no',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },
            { label: t('order.refund.user_id'), prop: 'user_id', align: 'center', operatorPlaceholder: t('Fuzzy query'), operator: 'LIKE' },
            {
                label: t('order.refund.user__username'),
                prop: 'user.username',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                render: 'tags',
                operator: 'LIKE',
            },
            {
                label: t('order.refund.refund_no'),
                prop: 'refund_no',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },
            {
                label: t('order.refund.refund_type'),
                prop: 'refund_type',
                align: 'center',
                render: 'tag',
                operator: 'eq',
                sortable: false,
                replaceValue: {
                    refund_only: t('order.refund.refund_type refund_only'),
                    return_refund: t('order.refund.refund_type return_refund'),
                    exchange: t('order.refund.refund_type exchange'),
                },
            },
            { label: t('order.refund.refund_amount'), prop: 'refund_amount', align: 'center', operator: 'RANGE', sortable: false },
            {
                label: t('order.refund.reason'),
                prop: 'reason',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },
            { label: t('order.refund.evidence_images'), prop: 'evidence_images', align: 'center', render: 'images', operator: false },
            {
                label: t('order.refund.status'),
                prop: 'status',
                align: 'center',
                render: 'tag',
                operator: 'eq',
                sortable: false,
                replaceValue: {
                    pending: t('order.refund.status pending'),
                    approved: t('order.refund.status approved'),
                    rejected: t('order.refund.status rejected'),
                    processing: t('order.refund.status processing'),
                    completed: t('order.refund.status completed'),
                    cancelled: t('order.refund.status cancelled'),
                },
            },
            {
                label: t('order.refund.process_time'),
                prop: 'process_time',
                align: 'center',
                render: 'datetime',
                operator: 'RANGE',
                sortable: 'custom',
                width: 160,
                timeFormat: 'yyyy-mm-dd hh:MM:ss',
            },
            {
                label: t('order.refund.completion_time'),
                prop: 'completion_time',
                align: 'center',
                render: 'datetime',
                operator: 'RANGE',
                sortable: 'custom',
                width: 160,
                timeFormat: 'yyyy-mm-dd hh:MM:ss',
            },
            { label: t('order.refund.actual_refund_amount'), prop: 'actual_refund_amount', align: 'center', operator: 'RANGE', sortable: false },
            {
                label: t('order.refund.refund_method'),
                prop: 'refund_method',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },
            {
                label: t('order.refund.refund_transaction_id'),
                prop: 'refund_transaction_id',
                align: 'center',
                operatorPlaceholder: t('Fuzzy query'),
                operator: 'LIKE',
                sortable: false,
            },
            {
                label: t('order.refund.refund_time'),
                prop: 'refund_time',
                align: 'center',
                render: 'datetime',
                operator: 'RANGE',
                sortable: 'custom',
                width: 160,
                timeFormat: 'yyyy-mm-dd hh:MM:ss',
            },
            {
                label: t('order.refund.create_time'),
                prop: 'create_time',
                align: 'center',
                render: 'datetime',
                operator: 'RANGE',
                sortable: 'custom',
                width: 160,
                timeFormat: 'yyyy-mm-dd hh:MM:ss',
            },
            {
                label: t('order.refund.update_time'),
                prop: 'update_time',
                align: 'center',
                render: 'datetime',
                operator: 'RANGE',
                sortable: 'custom',
                width: 160,
                timeFormat: 'yyyy-mm-dd hh:MM:ss',
            },
            {
                label: t('处理操作'),
                align: 'center',
                width: 120,
                render: 'buttons',
                buttons: [
                    {
                        name: 'processRefund',
                        render: 'tipButton',
                        title: '处理退款',
                        type: 'primary',
                        icon: 'fa fa-edit',
                        text: '处理',
                        click: (row: any) => openProcessModal(row),
                        display: (row: any) => row.status === 'pending',
                    },
                ],
                operator: false,
            },
            { label: t('Operate'), align: 'center', width: 100, render: 'buttons', buttons: optButtons, operator: false },
        ],
        dblClickNotEditColumn: [undefined],
    },
    {
        defaultItems: { status: 'pending' },
    }
)

provide('baTable', baTable)

onMounted(() => {
    baTable.table.ref = tableRef.value
    baTable.mount()
    baTable.getData()?.then(() => {
        baTable.initSort()
        baTable.dragSort()
    })
})
</script>

<style scoped lang="scss">
.process-modal-content {
    .refund-info {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 24px;

        h4 {
            margin: 0 0 16px 0;
            color: #303133;
            font-size: 16px;
            font-weight: 600;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;

            @media (max-width: 768px) {
                grid-template-columns: 1fr;
            }
        }

        .info-item {
            display: flex;
            align-items: center;

            label {
                font-weight: 600;
                color: #606266;
                min-width: 80px;
                margin-right: 12px;
            }

            span {
                color: #303133;

                &.amount {
                    font-weight: 600;
                    color: #f56c6c;
                    font-size: 16px;
                }
            }
        }
    }

    .form-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
    }

    .el-form {
        .el-form-item {
            margin-bottom: 22px;
        }

        .el-radio-group {
            .el-radio {
                margin-right: 24px;
            }
        }
    }
}

.dialog-footer {
    text-align: right;

    .el-button {
        margin-left: 12px;
    }
}
</style>
