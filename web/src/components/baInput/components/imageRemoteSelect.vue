<template>
    <div class="image-remote-select-component">
                <div class="image-section">
            <div class="section-label">图片</div>
            <FormItem
                type="image"
                v-model="imageValue"
                :input-attr="{
                    ...attrs,
                    onChange: handleImageChange,
                }"
                :placeholder="attrs.imagePlaceholder || '请选择图片'"
            />
        </div>
        <div class="remote-select-section">
            <div class="section-label">远程选择</div>
            <FormItem
                type="remoteSelect"
                v-model="remoteValue"
                :input-attr="{
                    ...attrs,
                    onChange: handleRemoteChange,
                    placeholder: attrs.remotePlaceholder || '请选择',
                }"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import FormItem from '/@/components/formItem/index.vue'
import type { InputAttr } from '/@/components/baInput'

interface Props {
    modelValue: any
    attr: InputAttr
}

const props = defineProps<Props>()
const emit = defineEmits<{
    (e: 'update:modelValue', value: any): void
}>()

// 获取属性
const attrs = computed(() => props.attr || {})

// 默认值结构
const defaultValue = {
    image: '',
    remoteValue: '',
}

// 当前值
const currentValue = computed(() => {
    if (!props.modelValue) return defaultValue

    // 如果是字符串，尝试解析JSON
    if (typeof props.modelValue === 'string') {
        try {
            const parsed = JSON.parse(props.modelValue)
            // 确保image和remoteValue都是字符串
            return {
                ...defaultValue,
                image: typeof parsed.image === 'string' ? parsed.image : '',
                remoteValue: typeof parsed.remoteValue === 'string' ? parsed.remoteValue : '',
            }
        } catch {
            // 如果解析失败，可能是单个值，尝试作为图片路径处理
            return { ...defaultValue, image: props.modelValue }
        }
    }

    // 如果是对象，直接使用，但确保值是字符串
    if (typeof props.modelValue === 'object') {
        return {
            ...defaultValue,
            image: typeof props.modelValue.image === 'string' ? props.modelValue.image : '',
            remoteValue: typeof props.modelValue.remoteValue === 'string' ? props.modelValue.remoteValue : '',
        }
    }

    return defaultValue
})

// 图片值
const imageValue = computed({
    get: () => currentValue.value.image,
    set: (value) => {
        const newValue = { ...currentValue.value, image: value }
        // 确保数据格式正确，系统可能期望JSON字符串
        emit('update:modelValue', JSON.stringify(newValue))
    },
})

// 远程选择值
const remoteValue = computed({
    get: () => currentValue.value.remoteValue,
    set: (value) => {
        const newValue = { ...currentValue.value, remoteValue: value }
        // 确保数据格式正确，系统可能期望JSON字符串
        emit('update:modelValue', JSON.stringify(newValue))
    },
})

// 处理图片变化
const handleImageChange = (value: any) => {
    // 如果value是数组（选择图片操作），取第一个元素
    if (Array.isArray(value)) {
        const imageUrl = value[0] || ''
        imageValue.value = imageUrl
    } else if (typeof value === 'object' && value !== null) {
        // 如果value是对象（图片上传的完整响应），提取serverUrl或url
        const imageUrl = value.serverUrl || value.url || ''
        imageValue.value = imageUrl
    } else if (typeof value === 'string') {
        // 如果value是字符串，可能是单个URL或逗号分隔的URL列表
        const imageUrl = value.split(',')[0] || ''
        imageValue.value = imageUrl
    } else {
        // 其他情况，清空值
        imageValue.value = ''
    }
}

// 处理远程选择变化
const handleRemoteChange = (value: any) => {
    remoteValue.value = value
}

// 监听外部值变化
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue && typeof newValue === 'string') {
            try {
                const parsed = JSON.parse(newValue)
                if (parsed.image !== imageValue.value) {
                    imageValue.value = parsed.image || ''
                }
                if (parsed.remoteValue !== remoteValue.value) {
                    remoteValue.value = parsed.remoteValue || ''
                }
            } catch {
                // 如果解析失败，保持当前值
            }
        }
    },
    { deep: true }
)
</script>

<style scoped lang="scss">
.image-remote-select-component {
    display: flex;
    gap: 16px;
    align-items: flex-start;

    .image-section {
        flex: 0 0 auto;
        min-width: 120px;

        .section-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
            font-weight: 500;
        }

        // 自定义图片上传组件样式
        :deep(.ba-upload) {
            .el-upload--picture-card {
                width: 100px !important;
                height: 32px !important;
                line-height: 32px !important;
                border-radius: 4px !important;
            }

            .ba-upload-select-image {
                width: 100px !important;
                height: 32px !important;
                line-height: 32px !important;
                font-size: 12px !important;
                border-radius: 4px !important;
                z-index: 10 !important;
                position: relative !important;
            }

            .ba-upload-icon {
                font-size: 16px !important;
            }

            // 缩略图样式
            .el-upload-list__item {
                width: 100px !important;
                height: 32px !important;
                border-radius: 4px !important;
                overflow: hidden !important;

                img {
                    width: 100% !important;
                    height: 100% !important;
                    object-fit: cover !important;
                    border-radius: 4px !important;
                }
            }

            // 修复选择按钮层级问题
            .el-upload {
                position: relative !important;

                .ba-upload-select-image {
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    z-index: 10 !important;
                    background: rgba(255, 255, 255, 0.9) !important;
                }
            }
        }
    }

    .remote-select-section {
        flex: 1;
        min-width: 0;

        .section-label {
            font-size: 14px;
            color: #909399;
            margin-bottom: 8px;
            font-weight: 500;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .image-remote-select-component {
        flex-direction: column;
        gap: 12px;
    }
}
</style>
