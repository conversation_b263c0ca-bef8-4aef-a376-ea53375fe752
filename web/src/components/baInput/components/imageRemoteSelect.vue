<template>
    <div class="image-remote-select-component">
        <div class="image-section">
            <div class="section-label">图片</div>
            <div class="image-controls">
                <!-- 上传按钮 -->
                <div class="upload-button" @click="showUpload">
                    <i class="el-icon-Upload"></i>
                    上传
                </div>
                <FormItem
                    type="image"
                    v-model="imageValue"
                    :input-attr="{
                        ...attrs,
                        onChange: handleImageChange,
                    }"
                    :placeholder="attrs.imagePlaceholder || '请选择图片'"
                />
            </div>
        </div>
        <div class="remote-select-section">
            <div class="section-label">远程选择</div>
            <FormItem
                type="remoteSelect"
                v-model="remoteValue"
                :input-attr="{
                    ...attrs,
                    onChange: handleRemoteChange,
                    onRow: handleRemoteRowData,
                    placeholder: attrs.remotePlaceholder || '请选择',
                    remoteUrl: attrs.remoteUrl || '',
                    pk: attrs.pk || 'id',
                    field: attrs.field || 'title',
                    params: {
                        ...processedParams,
                        // 避免使用可能导致列名冲突的参数
                        initKey: undefined,
                        initValue: undefined,
                    },
                }"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import FormItem from '/@/components/formItem/index.vue'
import type { InputAttr } from '/@/components/baInput'

interface Props {
    modelValue: any
    attr: InputAttr
}

const props = defineProps<Props>()
const emit = defineEmits<{
    (e: 'update:modelValue', value: any): void
}>()

// 获取属性
const attrs = computed(() => props.attr || {})

// 处理 params 参数，解析 HTML 编码的 JSON 字符串
const processedParams = computed(() => {
    if (typeof attrs.value.params === 'string') {
        try {
            // 先解码 HTML 实体
            const decodedParams = (attrs.value.params as any as string)
                .replace(/&quot;/g, '"')
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
            return JSON.parse(decodedParams)
        } catch (e) {
            console.warn('Failed to parse params JSON:', attrs.value.params)
            return {}
        }
    }
    return attrs.value.params || {}
})

// 获取自定义字段配置
const customFields = computed(() => {
    if (attrs.value.customFields) {
        try {
            let customFieldsStr = attrs.value.customFields
            if (typeof customFieldsStr === 'string') {
                // 先解码 HTML 实体
                customFieldsStr = customFieldsStr
                    .replace(/&quot;/g, '"')
                    .replace(/&amp;/g, '&')
                    .replace(/&lt;/g, '<')
                    .replace(/&gt;/g, '>')
                const parsed = JSON.parse(customFieldsStr)
                return parsed
            }
            return attrs.value.customFields
        } catch (e) {
            console.warn('Failed to parse customFields:', attrs.value.customFields, e)
            return []
        }
    }
    return []
})

// 默认值结构
const getDefaultValue = () => {
    const value: any = {
        image: '',
        remoteValue: '',
    }

    // 根据配置添加自定义字段
    customFields.value.forEach((field: any) => {
        value[field.name] = field.defaultValue || ''
    })

    return value
}

// 当前值
const currentValue = computed(() => {
    if (!props.modelValue) return getDefaultValue()

    // 如果是字符串，尝试解析JSON
    if (typeof props.modelValue === 'string') {
        try {
            const parsed = JSON.parse(props.modelValue)
            // 确保image和remoteValue都是字符串
            const newValue = { ...getDefaultValue() }
            // 合并现有数据
            Object.keys(newValue).forEach((key) => {
                if (parsed[key] !== undefined) {
                    newValue[key] = typeof parsed[key] === 'string' ? parsed[key] : newValue[key]
                }
            })
            return newValue
        } catch {
            // 如果解析失败，可能是单个值，尝试作为图片路径处理
            return { ...getDefaultValue(), image: props.modelValue }
        }
    }

    // 如果是对象，直接使用，但确保值是字符串
    if (typeof props.modelValue === 'object') {
        const newValue = { ...getDefaultValue() }
        // 合并现有数据
        Object.keys(newValue).forEach((key) => {
            if (props.modelValue[key] !== undefined) {
                newValue[key] = typeof props.modelValue[key] === 'string' ? props.modelValue[key] : newValue[key]
            }
        })
        return newValue
    }

    return getDefaultValue()
})

// 图片值
const imageValue = computed({
    get: () => currentValue.value.image,
    set: (value) => {
        const newValue = { ...currentValue.value, image: value }
        // 确保数据格式正确，系统可能期望JSON字符串
        emit('update:modelValue', JSON.stringify(newValue))
    },
})

// 远程选择值
const remoteValue = computed({
    get: () => currentValue.value.remoteValue,
    set: (value) => {
        // 如果有远程数据，使用远程数据中的自定义字段
        let newValue = { ...currentValue.value, remoteValue: value }

        // 如果有保存的远程数据，使用其中的自定义字段
        if (lastRemoteData && customFields.value.length > 0) {
            customFields.value.forEach((field: any) => {
                if (lastRemoteData[field.name] !== undefined) {
                    newValue[field.name] = lastRemoteData[field.name]
                }
            })
        }

        // 确保数据格式正确，系统可能期望JSON字符串
        emit('update:modelValue', JSON.stringify(newValue))
    },
})

// 存储远程数据的临时变量
let lastRemoteData: any = null

// 处理图片变化
const handleImageChange = (value: any) => {
    // 如果value是数组（选择图片操作），取第一个元素
    if (Array.isArray(value)) {
        const imageUrl = value[0] || ''
        imageValue.value = imageUrl
    } else if (typeof value === 'object' && value !== null) {
        // 如果value是对象（图片上传的完整响应），提取serverUrl或url
        const imageUrl = value.serverUrl || value.url || ''
        imageValue.value = imageUrl
    } else if (typeof value === 'string') {
        // 如果value是字符串，可能是单个URL或逗号分隔的URL列表
        const imageUrl = value.split(',')[0] || ''
        imageValue.value = imageUrl
    } else {
        // 其他情况，清空值
        imageValue.value = ''
    }
}

// 处理远程选择变化
const handleRemoteChange = (value: any) => {
    remoteValue.value = value
}

// 处理远程选择行数据变化
const handleRemoteRowData = (rowData: any) => {
    // 保存远程数据，供后续使用
    lastRemoteData = rowData

    // 如果有自定义字段配置，从远程选择的数据中提取字段值
    if (customFields.value.length > 0 && rowData) {
        // 从当前值开始，而不是重置为默认值
        const newValue = { ...currentValue.value }

        customFields.value.forEach((field: any) => {
            // 从远程数据中提取对应字段的值
            if (rowData[field.name] !== undefined) {
                newValue[field.name] = rowData[field.name]
            }
        })

        emit('update:modelValue', JSON.stringify(newValue))
    }
}

// 显示上传对话框
const showUpload = () => {
    // 触发上传组件
    const uploadEl = document.querySelector('.image-controls .el-upload__input')
    if (uploadEl) {
        ;(uploadEl as HTMLElement).click()
    }
}

// 监听外部值变化
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue && typeof newValue === 'string') {
            try {
                const parsed = JSON.parse(newValue)
                if (parsed.image !== imageValue.value) {
                    imageValue.value = parsed.image || ''
                }
                if (parsed.remoteValue !== remoteValue.value) {
                    remoteValue.value = parsed.remoteValue || ''
                }
            } catch {
                // 如果解析失败，保持当前值
            }
        }
    },
    { deep: true }
)
</script>

<style scoped lang="scss">
.image-remote-select-component {
    display: flex;
    gap: 16px;
    align-items: flex-start;
    width: 100%;
    max-width: 100%;
    overflow: hidden;

    .image-section {
        flex: 0 0 auto;
        min-width: 324px;
        max-width: 450px;

        .section-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
            font-weight: 500;
        }

        // 图片控制区域样式
        .image-controls {
            display: flex;
            align-items: flex-start;
            gap: 8px;

            .upload-button {
                width: 100px !important;
                height: 32px !important;
                line-height: 32px !important;
                font-size: 12px !important;
                border-radius: 4px !important;
                background-color: #409eff !important;
                color: white !important;
                text-align: center !important;
                cursor: pointer !important;
                border: none !important;
                position: relative !important;

                &:hover {
                    background-color: #66b1ff !important;
                }

                i {
                    font-size: 16px !important;
                    margin-right: 4px;
                }
            }
        }

        // 自定义图片上传组件样式
        :deep(.ba-upload) {
            .el-upload--picture-card {
                width: 100px !important;
                height: 32px !important;
                line-height: 32px !important;
                border-radius: 4px !important;
            }

            .ba-upload-select-image {
                width: 100px !important;
                height: 32px !important;
                line-height: 32px !important;
                font-size: 12px !important;
                border-radius: 4px !important;
                z-index: 10 !important;
                position: relative !important;
            }

            .ba-upload-icon {
                font-size: 16px !important;
            }

            // 缩略图样式
            .el-upload-list__item {
                width: 100px !important;
                height: 32px !important;
                border-radius: 4px !important;
                overflow: hidden !important;

                img {
                    width: 100% !important;
                    height: 100% !important;
                    object-fit: cover !important;
                    border-radius: 4px !important;
                }
            }

            // 修复选择按钮层级问题
            .el-upload {
                position: relative !important;

                .ba-upload-select-image {
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    z-index: 10 !important;
                    background: rgba(255, 255, 255, 0.9) !important;
                }
            }
        }
    }

    .remote-select-section {
        flex: 1;
        min-width: 0;
        max-width: calc(100% - 460px);

        .section-label {
            font-size: 14px;
            color: #909399;
            margin-bottom: 8px;
            font-weight: 500;
        }

        // 调整远程选择输入框的宽度
        :deep(.el-select) {
            width: 100% !important;
            min-width: 0 !important;
            max-width: 100% !important;
        }

        :deep(.el-input) {
            width: 100% !important;
            min-width: 0 !important;
            max-width: 100% !important;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .image-remote-select-component {
        flex-direction: column;
        gap: 12px;

        .image-section {
            max-width: 100%;
        }

        .remote-select-section {
            max-width: 100%;
        }
    }
}

@media (max-width: 480px) {
    .image-remote-select-component {
        .image-controls {
            flex-direction: column;
            gap: 4px;
        }

        .upload-button {
            width: 100% !important;
        }

        :deep(.ba-upload) {
            .el-upload--picture-card,
            .ba-upload-select-image,
            .el-upload-list__item {
                width: 100% !important;
            }
        }
    }
}
</style>
