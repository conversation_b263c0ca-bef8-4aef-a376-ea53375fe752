<template>
    <div class="text-url-component">
        <div class="text-section">
            <div class="section-label">显示文字</div>
            <FormItem
                type="string"
                v-model="textValue"
                :input-attr="{
                    ...attrs,
                    onChange: handleTextChange,
                    placeholder: attrs.textPlaceholder || '请输入显示文字',
                }"
            />
        </div>
        <div class="url-section">
            <div class="section-label">跳转链接</div>
            <FormItem
                type="string"
                v-model="urlValue"
                :input-attr="{
                    ...attrs,
                    onChange: handleUrlChange,
                    placeholder: attrs.urlPlaceholder || '请输入跳转链接',
                }"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import FormItem from '/@/components/formItem/index.vue'
import type { InputAttr } from '/@/components/baInput'

interface Props {
    modelValue: any
    attr: InputAttr
}

const props = defineProps<Props>()
const emit = defineEmits<{
    (e: 'update:modelValue', value: any): void
}>()

// 获取属性
const attrs = computed(() => props.attr || {})

// 默认值结构
const defaultValue = {
    text: '',
    url: '',
}

// 当前值
const currentValue = computed(() => {
    if (!props.modelValue) return defaultValue

    // 如果是字符串，尝试解析JSON
    if (typeof props.modelValue === 'string') {
        try {
            const parsed = JSON.parse(props.modelValue)
            // 确保text和url都是字符串
            return {
                ...defaultValue,
                text: typeof parsed.text === 'string' ? parsed.text : '',
                url: typeof parsed.url === 'string' ? parsed.url : '',
            }
        } catch {
            // 如果解析失败，可能是单个值，尝试作为文本处理
            return { ...defaultValue, text: props.modelValue }
        }
    }

    // 如果是对象，直接使用，但确保值是字符串
    if (typeof props.modelValue === 'object') {
        return {
            ...defaultValue,
            text: typeof props.modelValue.text === 'string' ? props.modelValue.text : '',
            url: typeof props.modelValue.url === 'string' ? props.modelValue.url : '',
        }
    }

    return defaultValue
})

// 文本值
const textValue = computed({
    get: () => currentValue.value.text,
    set: (value) => {
        const newValue = { ...currentValue.value, text: value }
        // 确保数据格式正确，系统可能期望JSON字符串
        emit('update:modelValue', JSON.stringify(newValue))
    },
})

// URL值
const urlValue = computed({
    get: () => currentValue.value.url,
    set: (value) => {
        const newValue = { ...currentValue.value, url: value }
        // 确保数据格式正确，系统可能期望JSON字符串
        emit('update:modelValue', JSON.stringify(newValue))
    },
})

// 处理文本变化
const handleTextChange = (value: string) => {
    textValue.value = value
}

// 处理URL变化
const handleUrlChange = (value: string) => {
    urlValue.value = value
}

// 监听外部值变化
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue && typeof newValue === 'string') {
            try {
                const parsed = JSON.parse(newValue)
                if (parsed.text !== textValue.value) {
                    textValue.value = parsed.text || ''
                }
                if (parsed.url !== urlValue.value) {
                    urlValue.value = parsed.url || ''
                }
            } catch {
                // 如果解析失败，保持当前值
            }
        }
    },
    { deep: true }
)
</script>

<style scoped lang="scss">
.text-url-component {
    display: flex;
    gap: 16px;
    align-items: flex-start;

    .text-section,
    .url-section {
        flex: 1;
        min-width: 0;
    }

    .section-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .url-section {
        .section-label {
            color: #909399;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .text-url-component {
        flex-direction: column;
        gap: 12px;
    }
}
</style>
