<template>
    <div class="image-url-component">
        <div class="image-section">
            <div class="section-label">图片</div>
            <div class="image-controls">
                <FormItem
                    type="image"
                    v-model="imageValue"
                    :input-attr="{
                        ...attrs,
                        onChange: handleImageChange,
                    }"
                    :placeholder="attrs.imagePlaceholder || '请选择图片'"
                />
                <!-- 上传按钮 -->
                <el-button size="small" type="primary" @click="showUpload">
                    <i class="el-icon-Upload"></i>
                    上传
                </el-button>
            </div>
        </div>
        <div class="url-section">
            <div class="section-label">跳转链接</div>
            <FormItem
                type="string"
                v-model="urlValue"
                :input-attr="{
                    ...attrs,
                    onChange: handleUrlChange,
                    placeholder: attrs.urlPlaceholder || '请输入跳转链接',
                }"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import FormItem from '/@/components/formItem/index.vue'
import type { InputAttr } from '/@/components/baInput'

interface Props {
    modelValue: any
    attr: InputAttr
}

const props = defineProps<Props>()
const emit = defineEmits<{
    (e: 'update:modelValue', value: any): void
}>()

// 获取属性
const attrs = computed(() => props.attr || {})

// 默认值结构
const defaultValue = {
    image: '',
    url: '',
}

// 当前值
const currentValue = computed(() => {
    if (!props.modelValue) return defaultValue

    // 如果是字符串，尝试解析JSON
    if (typeof props.modelValue === 'string') {
        try {
            const parsed = JSON.parse(props.modelValue)
            // 确保image和url都是字符串
            return {
                ...defaultValue,
                image: typeof parsed.image === 'string' ? parsed.image : '',
                url: typeof parsed.url === 'string' ? parsed.url : '',
            }
        } catch {
            // 如果解析失败，可能是单个值，尝试作为图片路径处理
            return { ...defaultValue, image: props.modelValue }
        }
    }

    // 如果是对象，直接使用，但确保值是字符串
    if (typeof props.modelValue === 'object') {
        return {
            ...defaultValue,
            image: typeof props.modelValue.image === 'string' ? props.modelValue.image : '',
            url: typeof props.modelValue.url === 'string' ? props.modelValue.url : '',
        }
    }

    return defaultValue
})

// 图片值
const imageValue = computed({
    get: () => currentValue.value.image,
    set: (value) => {
        const newValue = { ...currentValue.value, image: value }
        // 确保数据格式正确，系统可能期望JSON字符串
        emit('update:modelValue', JSON.stringify(newValue))
    },
})

// URL值
const urlValue = computed({
    get: () => currentValue.value.url,
    set: (value) => {
        const newValue = { ...currentValue.value, url: value }
        // 确保数据格式正确，系统可能期望JSON字符串
        emit('update:modelValue', JSON.stringify(newValue))
    },
})

// 处理图片变化
const handleImageChange = (value: any) => {
    console.log('handleImageChange received:', value) // 调试日志

    // 如果value是数组（选择图片操作），取第一个元素
    if (Array.isArray(value)) {
        const imageUrl = value[0] || ''
        console.log('Extracted imageUrl from array:', imageUrl) // 调试日志
        imageValue.value = imageUrl
    } else if (typeof value === 'object' && value !== null) {
        // 如果value是对象（图片上传的完整响应），提取serverUrl或url
        const imageUrl = value.serverUrl || value.url || ''
        console.log('Extracted imageUrl from object:', imageUrl) // 调试日志
        imageValue.value = imageUrl
    } else if (typeof value === 'string') {
        // 如果value是字符串，可能是单个URL或逗号分隔的URL列表
        console.log('Using string value directly:', value) // 调试日志
        // 如果是逗号分隔的多个URL，取第一个
        const imageUrl = value.split(',')[0] || ''
        imageValue.value = imageUrl
    } else {
        // 其他情况，清空值
        console.log('Clearing image value') // 调试日志
        imageValue.value = ''
    }
}

// 处理URL变化
const handleUrlChange = (value: string) => {
    urlValue.value = value
}

// 显示上传对话框
const showUpload = () => {
    // 触发上传组件
    const uploadEl = document.querySelector('.image-controls .el-upload__input')
    if (uploadEl) {
        ;(uploadEl as HTMLElement).click()
    }
}

// 监听外部值变化
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue && typeof newValue === 'string') {
            try {
                const parsed = JSON.parse(newValue)
                if (parsed.image !== imageValue.value) {
                    imageValue.value = parsed.image || ''
                }
                if (parsed.url !== urlValue.value) {
                    urlValue.value = parsed.url || ''
                }
            } catch {
                // 如果解析失败，保持当前值
            }
        }
    },
    { deep: true }
)
</script>

<style scoped lang="scss">
.image-url-component {
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: flex-start;

    .image-section {
        flex: 0 0 auto;
        min-width: 120px;

        .section-label {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
            font-weight: 500;
        }

        // 图片控制区域样式
        .image-controls {
            display: flex;
            align-items: center;
            gap: 8px;

            .el-button {
                height: 32px;
                font-size: 12px;
                padding: 6px 12px;
            }
        }

        // 自定义图片上传组件样式 - 只调整高度到32px
        :deep(.ba-upload) {
            .el-upload--picture-card {
                width: 100px !important;
                height: 32px !important;
                line-height: 32px !important;
                border-radius: 4px !important;
            }

            .ba-upload-select-image {
                width: 100px !important;
                height: 32px !important;
                line-height: 32px !important;
                font-size: 12px !important;
                border-radius: 4px !important;
                z-index: 10 !important;
                position: relative !important;
            }

            .ba-upload-icon {
                font-size: 16px !important;
            }

            // 缩略图样式
            .el-upload-list__item {
                width: 100px !important;
                height: 32px !important;
                border-radius: 4px !important;
                overflow: hidden !important;

                img {
                    width: 100% !important;
                    height: 100% !important;
                    object-fit: cover !important;
                    border-radius: 4px !important;
                }
            }

            // 修复选择按钮层级问题
            .el-upload {
                position: relative !important;

                .ba-upload-select-image {
                    position: absolute !important;
                    top: 0 !important;
                    left: 0 !important;
                    z-index: 10 !important;
                    background: rgba(255, 255, 255, 0.9) !important;
                }
            }
        }
    }

    .url-section {
        flex: 1;
        min-width: 0;

        .section-label {
            font-size: 14px;
            color: #909399;
            margin-bottom: 8px;
            font-weight: 500;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .image-url-component {
        flex-direction: column;
        gap: 12px;
    }
}
</style>
