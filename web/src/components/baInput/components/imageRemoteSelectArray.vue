<template>
    <div class="image-remote-select-array-component">
        <div class="array-header">
            <el-button type="primary" size="small" @click="addRow" style="min-width: 80px; flex-shrink: 0;">
                <i class="el-icon-Plus"></i>
                添加行
            </el-button>
        </div>

        <div class="array-content">
            <div v-for="(row, index) in arrayValue" :key="index" class="array-row">
                <div class="row-header">
                    <span class="row-index">第 {{ index + 1 }} 行</span>
                    <el-button type="danger" size="small" @click="removeRow(index)" :disabled="arrayValue.length <= 1" style="min-width: 60px; flex-shrink: 0;">
                        <i class="el-icon-Delete"></i>
                        删除
                    </el-button>
                </div>

                <div class="row-content">
                    <!-- 图片部分 -->
                    <div class="image-section">
                        <div class="field-label">图片</div>
                        <div class="image-controls">
                            <div class="upload-button" @click="showUpload(index)">
                                <i class="el-icon-Upload"></i>
                                上传
                            </div>
                            <FormItem
                                type="image"
                                v-model="row.image"
                                :input-attr="{
                                    ...attrs,
                                    onChange: (value: any) => handleImageChange(index, value),
                                }"
                                :placeholder="attrs.imagePlaceholder || '请选择图片'"
                            />
                        </div>
                    </div>

                    <!-- 远程选择部分 -->
                    <div class="remote-select-section">
                        <div class="field-label">远程选择</div>
                        <FormItem
                            type="remoteSelect"
                            v-model="row.remoteValue"
                            :input-attr="{
                                ...attrs,
                                onChange: (value: any) => handleRemoteChange(index, value),
                                onRow: (rowData: any) => handleRemoteRowData(index, rowData),
                                placeholder: attrs.remotePlaceholder || '请选择',
                                remoteUrl: attrs.remoteUrl || '',
                                pk: attrs.pk || 'id',
                                field: attrs.field || 'title',
                                params: {
                                    ...processedParams,
                                    initKey: undefined,
                                    initValue: undefined,
                                },
                            }"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import FormItem from '/@/components/formItem/index.vue'
import type { InputAttr } from '/@/components/baInput'

interface Props {
    modelValue: any
    attr: InputAttr
}

const props = defineProps<Props>()
const emit = defineEmits<{
    (e: 'update:modelValue', value: any): void
}>()

// 获取属性
const attrs = computed(() => props.attr || {})

// 处理 params 参数，解析 HTML 编码的 JSON 字符串
const processedParams = computed(() => {
    if (typeof attrs.value.params === 'string') {
        try {
            // 先解码 HTML 实体
            const decodedParams = (attrs.value.params as any as string)
                .replace(/&quot;/g, '"')
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
            return JSON.parse(decodedParams)
        } catch (e) {
            console.warn('Failed to parse params JSON:', attrs.value.params)
            return {}
        }
    }
    return attrs.value.params || {}
})

// 获取自定义字段配置
const customFields = computed(() => {
    if (attrs.value.customFields) {
        try {
            let customFieldsStr = attrs.value.customFields
            if (typeof customFieldsStr === 'string') {
                // 先解码 HTML 实体
                customFieldsStr = customFieldsStr
                    .replace(/&quot;/g, '"')
                    .replace(/&amp;/g, '&')
                    .replace(/&lt;/g, '<')
                    .replace(/&gt;/g, '>')
                const parsed = JSON.parse(customFieldsStr)
                return parsed
            }
            return attrs.value.customFields
        } catch (e) {
            console.warn('Failed to parse customFields:', attrs.value.customFields, e)
            return []
        }
    }
    return []
})

// 默认行结构
const defaultRow = computed(() => {
    const row: any = {
        image: '',
        remoteValue: '',
    }

    // 根据配置添加自定义字段
    customFields.value.forEach((field: any) => {
        row[field.name] = field.defaultValue || ''
    })

    return row
})

// 当前数组值
const arrayValue = computed({
    get: () => {
        if (!props.modelValue) return [defaultRow.value]

        // 如果是字符串，尝试解析JSON
        if (typeof props.modelValue === 'string') {
            try {
                const parsed = JSON.parse(props.modelValue)
                if (Array.isArray(parsed)) {
                    return parsed.map((row) => {
                        const newRow = { ...defaultRow.value }
                        // 合并现有数据
                        Object.keys(newRow).forEach((key) => {
                            newRow[key] = typeof row[key] === 'string' ? row[key] : newRow[key]
                        })
                        return newRow
                    })
                }
            } catch {
                // 如果解析失败，返回默认行
            }
        }

        // 如果是对象数组，直接使用
        if (Array.isArray(props.modelValue)) {
            return props.modelValue.map((row) => {
                const newRow = { ...defaultRow.value }
                // 合并现有数据
                Object.keys(newRow).forEach((key) => {
                    newRow[key] = typeof row[key] === 'string' ? row[key] : newRow[key]
                })
                return newRow
            })
        }

        return [defaultRow.value]
    },
    set: (value) => {
        emit('update:modelValue', JSON.stringify(value))
    },
})

// 添加行
const addRow = () => {
    const newArray = [...arrayValue.value, { ...defaultRow.value }]
    arrayValue.value = newArray
}

// 删除行
const removeRow = (index: number) => {
    if (arrayValue.value.length > 1) {
        const newArray = arrayValue.value.filter((_, i) => i !== index)
        arrayValue.value = newArray
    }
}

// 处理图片变化
const handleImageChange = (index: number, value: any) => {
    const newArray = [...arrayValue.value]

    // 如果value是数组（选择图片操作），取第一个元素
    if (Array.isArray(value)) {
        const imageUrl = value[0] || ''
        newArray[index].image = imageUrl
    } else if (typeof value === 'object' && value !== null) {
        // 如果value是对象（图片上传的完整响应），提取serverUrl或url
        const imageUrl = value.serverUrl || value.url || ''
        newArray[index].image = imageUrl
    } else if (typeof value === 'string') {
        // 如果value是字符串，可能是单个URL或逗号分隔的URL列表
        const imageUrl = value.split(',')[0] || ''
        newArray[index].image = imageUrl
    } else {
        // 其他情况，清空值
        newArray[index].image = ''
    }

    arrayValue.value = newArray
}

// 处理远程选择变化
const handleRemoteChange = (index: number, value: any) => {
    const newArray = [...arrayValue.value]
    newArray[index].remoteValue = value
    arrayValue.value = newArray
}

// 处理远程选择行数据变化
const handleRemoteRowData = (index: number, rowData: any) => {
    const newArray = [...arrayValue.value]

    // 如果有自定义字段配置，从远程选择的数据中提取字段值
    if (customFields.value.length > 0 && rowData) {
        customFields.value.forEach((field: any) => {
            // 从远程数据中提取对应字段的值
            if (rowData[field.name] !== undefined) {
                newArray[index][field.name] = rowData[field.name]
            }
        })
    }

    arrayValue.value = newArray
}

// 显示上传对话框
const showUpload = (index: number) => {
    // 触发对应行的上传组件
    const uploadEls = document.querySelectorAll('.image-controls .el-upload__input')
    if (uploadEls[index]) {
        ;(uploadEls[index] as HTMLElement).click()
    }
}

// 监听外部值变化
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue && typeof newValue === 'string') {
            try {
                const parsed = JSON.parse(newValue)
                if (Array.isArray(parsed)) {
                    // 如果外部传入的是数组，更新内部值
                    const newArray = parsed.map((row) => {
                        const newRow = { ...defaultRow.value }
                        // 合并现有数据
                        Object.keys(newRow).forEach((key) => {
                            newRow[key] = typeof row[key] === 'string' ? row[key] : newRow[key]
                        })
                        return newRow
                    })
                    if (JSON.stringify(newArray) !== JSON.stringify(arrayValue.value)) {
                        arrayValue.value = newArray
                    }
                }
            } catch {
                // 如果解析失败，保持当前值
            }
        }
    },
    { deep: true }
)
</script>

<style scoped lang="scss">
.image-remote-select-array-component {
    width: 100%;
    max-width: 100%;
    overflow: hidden;

    .array-header {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-bottom: 16px;
    }

    .array-content {
        .array-row {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            margin-bottom: 16px;
            padding: 16px;
            background-color: #fafafa;
            width: 100%;
            max-width: 100%;
            overflow: hidden;

            .row-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                padding-bottom: 8px;
                border-bottom: 1px solid #e4e7ed;
                min-height: 40px;

                .row-index {
                    font-size: 14px;
                    color: #606266;
                    font-weight: 500;
                    flex-shrink: 0;
                }
            }

            .row-content {
                display: flex;
                gap: 16px;
                align-items: flex-start;
                width: 100%;
                max-width: 100%;
                overflow: hidden;

                .image-section {
                    flex: 0 0 auto;
                    min-width: 324px;
                    max-width: 450px;

                    .field-label {
                        font-size: 14px;
                        color: #606266;
                        margin-bottom: 8px;
                        font-weight: 500;
                    }

                    .image-controls {
                        display: flex;
                        align-items: flex-start;
                        gap: 8px;

                        .upload-button {
                            width: 100px !important;
                            height: 32px !important;
                            line-height: 32px !important;
                            font-size: 12px !important;
                            border-radius: 4px !important;
                            background-color: #409eff !important;
                            color: white !important;
                            text-align: center !important;
                            cursor: pointer !important;
                            border: none !important;
                            position: relative !important;

                            &:hover {
                                background-color: #66b1ff !important;
                            }

                            i {
                                font-size: 16px !important;
                                margin-right: 4px;
                            }
                        }
                    }

                    // 自定义图片上传组件样式
                    :deep(.ba-upload) {
                        .el-upload--picture-card {
                            width: 100px !important;
                            height: 32px !important;
                            line-height: 32px !important;
                            border-radius: 4px !important;
                        }

                        .ba-upload-select-image {
                            width: 100px !important;
                            height: 32px !important;
                            line-height: 32px !important;
                            font-size: 12px !important;
                            border-radius: 4px !important;
                            z-index: 10 !important;
                            position: relative !important;
                        }

                        .ba-upload-icon {
                            font-size: 16px !important;
                        }

                        .el-upload-list__item {
                            width: 100px !important;
                            height: 32px !important;
                            border-radius: 4px !important;
                            overflow: hidden !important;

                            img {
                                width: 100% !important;
                                height: 100% !important;
                                object-fit: cover !important;
                                border-radius: 4px !important;
                            }
                        }

                        .el-upload {
                            position: relative !important;

                            .ba-upload-select-image {
                                position: absolute !important;
                                top: 0 !important;
                                left: 0 !important;
                                z-index: 10 !important;
                                background: rgba(255, 255, 255, 0.9) !important;
                            }
                        }
                    }
                }

                .remote-select-section {
                    flex: 1;
                    min-width: 0;
                    max-width: calc(100% - 460px);

                    .field-label {
                        font-size: 14px;
                        color: #606266;
                        margin-bottom: 8px;
                        font-weight: 500;
                    }

                    // 调整远程选择输入框的宽度
                    :deep(.el-select) {
                        width: 100% !important;
                        min-width: 0 !important;
                        max-width: 100% !important;
                    }

                    :deep(.el-input) {
                        width: 100% !important;
                        min-width: 0 !important;
                        max-width: 100% !important;
                    }
                }

                .custom-field-section {
                    flex: 1;
                    min-width: 0;
                    max-width: calc(100% - 460px);

                    .field-label {
                        font-size: 14px;
                        color: #606266;
                        margin-bottom: 8px;
                        font-weight: 500;
                    }

                    // 调整自定义字段输入框的宽度
                    :deep(.el-input) {
                        width: 100% !important;
                        min-width: 0 !important;
                        max-width: 100% !important;
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .image-remote-select-array-component {
        .array-content .array-row {
            .row-header {
                flex-wrap: wrap;
                gap: 8px;
            }
            
            .row-content {
                flex-direction: column;
                gap: 12px;
                
                .image-section {
                    max-width: 100%;
                }
                
                .remote-select-section,
                .custom-field-section {
                    max-width: 100%;
                }
            }
        }
    }
}

@media (max-width: 480px) {
    .image-remote-select-array-component {
        .array-content .array-row {
            .row-header {
                flex-direction: column;
                gap: 8px;
                align-items: flex-start;
            }
            
            .row-content {
                .image-section {
                    .image-controls {
                        flex-direction: column;
                        gap: 4px;
                    }
                    
                    .upload-button {
                        width: 100% !important;
                    }
                    
                    :deep(.ba-upload) {
                        .el-upload--picture-card,
                        .ba-upload-select-image,
                        .el-upload-list__item {
                            width: 100% !important;
                        }
                    }
                }
            }
        }
    }
}
</style>
