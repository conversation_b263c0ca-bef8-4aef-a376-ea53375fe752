<template>
    <div class="text-remote-select-component">
        <div class="text-section">
            <div class="section-label">显示文字</div>
            <FormItem
                type="string"
                v-model="textValue"
                :input-attr="{
                    ...attrs,
                    onChange: handleTextChange,
                    placeholder: attrs.textPlaceholder || '请输入显示文字',
                }"
            />
        </div>
        <div class="remote-select-section">
            <div class="section-label">远程选择</div>
            <FormItem
                type="remoteSelect"
                v-model="remoteValue"
                :input-attr="{
                    ...attrs,
                    onChange: handleRemoteChange,
                    placeholder: attrs.remotePlaceholder || '请选择',
                    remoteUrl: attrs.remoteUrl || '',
                }"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import FormItem from '/@/components/formItem/index.vue'
import type { InputAttr } from '/@/components/baInput'

interface Props {
    modelValue: any
    attr: InputAttr
}

const props = defineProps<Props>()
const emit = defineEmits<{
    (e: 'update:modelValue', value: any): void
}>()

// 获取属性
const attrs = computed(() => props.attr || {})

// 默认值结构
const defaultValue = {
    text: '',
    remoteValue: '',
}

// 当前值
const currentValue = computed(() => {
    if (!props.modelValue) return defaultValue

    // 如果是字符串，尝试解析JSON
    if (typeof props.modelValue === 'string') {
        try {
            const parsed = JSON.parse(props.modelValue)
            // 确保text和remoteValue都是字符串
            return {
                ...defaultValue,
                text: typeof parsed.text === 'string' ? parsed.text : '',
                remoteValue: typeof parsed.remoteValue === 'string' ? parsed.remoteValue : '',
            }
        } catch {
            // 如果解析失败，可能是单个值，尝试作为文本处理
            return { ...defaultValue, text: props.modelValue }
        }
    }

    // 如果是对象，直接使用，但确保值是字符串
    if (typeof props.modelValue === 'object') {
        return {
            ...defaultValue,
            text: typeof props.modelValue.text === 'string' ? props.modelValue.text : '',
            remoteValue: typeof props.modelValue.remoteValue === 'string' ? props.modelValue.remoteValue : '',
        }
    }

    return defaultValue
})

// 文本值
const textValue = computed({
    get: () => currentValue.value.text,
    set: (value) => {
        const newValue = { ...currentValue.value, text: value }
        // 确保数据格式正确，系统可能期望JSON字符串
        emit('update:modelValue', JSON.stringify(newValue))
    },
})

// 远程选择值
const remoteValue = computed({
    get: () => currentValue.value.remoteValue,
    set: (value) => {
        const newValue = { ...currentValue.value, remoteValue: value }
        // 确保数据格式正确，系统可能期望JSON字符串
        emit('update:modelValue', JSON.stringify(newValue))
    },
})

// 处理文本变化
const handleTextChange = (value: string) => {
    textValue.value = value
}

// 处理远程选择变化
const handleRemoteChange = (value: any) => {
    remoteValue.value = value
}

// 监听外部值变化
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue && typeof newValue === 'string') {
            try {
                const parsed = JSON.parse(newValue)
                if (parsed.text !== textValue.value) {
                    textValue.value = parsed.text || ''
                }
                if (parsed.remoteValue !== remoteValue.value) {
                    remoteValue.value = parsed.remoteValue || ''
                }
            } catch {
                // 如果解析失败，保持当前值
            }
        }
    },
    { deep: true }
)
</script>

<style scoped lang="scss">
.text-remote-select-component {
    display: flex;
    gap: 16px;
    align-items: flex-start;

    .text-section,
    .remote-select-section {
        flex: 1;
        min-width: 0;
    }

    .section-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .remote-select-section {
        .section-label {
            color: #909399;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .text-remote-select-component {
        flex-direction: column;
        gap: 12px;
    }
}
</style>
