<template>
    <div class="dynamic-form">
        <el-form
            v-if="!loading && formFields.length > 0"
            ref="formRef"
            :model="formData"
            :label-position="labelPosition"
            :label-width="labelWidth + 'px'"
            :rules="formRules"
        >
            <template v-for="field in formFields" :key="field.field_name">
                <!-- 字符串输入 -->
                <el-form-item v-if="field.field_type === 'string'" :label="field.field_label" :prop="field.field_name">
                    <el-input
                        v-model="formData[field.field_name]"
                        :placeholder="field.config.placeholder || `请输入${field.field_label}`"
                        :maxlength="field.config.maxlength"
                        clearable
                    />
                </el-form-item>

                <!-- 文本域 -->
                <el-form-item v-else-if="field.field_type === 'textarea'" :label="field.field_label" :prop="field.field_name">
                    <el-input
                        v-model="formData[field.field_name]"
                        type="textarea"
                        :placeholder="field.config.placeholder || `请输入${field.field_label}`"
                        :rows="field.config.rows || 3"
                        clearable
                    />
                </el-form-item>

                <!-- 数字输入 -->
                <el-form-item v-else-if="field.field_type === 'number'" :label="field.field_label" :prop="field.field_name">
                    <el-input-number
                        v-model="formData[field.field_name]"
                        :placeholder="field.config.placeholder || `请输入${field.field_label}`"
                        :min="field.config.min"
                        :max="field.config.max"
                        :step="field.config.step || 1"
                        controls-position="right"
                        style="width: 100%"
                    />
                </el-form-item>

                <!-- 浮点数输入 -->
                <el-form-item v-else-if="field.field_type === 'float'" :label="field.field_label" :prop="field.field_name">
                    <el-input-number
                        v-model="formData[field.field_name]"
                        :placeholder="field.config.placeholder || `请输入${field.field_label}`"
                        :min="field.config.min"
                        :max="field.config.max"
                        :step="field.config.step || 0.01"
                        :precision="2"
                        controls-position="right"
                        style="width: 100%"
                    />
                </el-form-item>

                <!-- 下拉选择 -->
                <el-form-item v-else-if="field.field_type === 'select'" :label="field.field_label" :prop="field.field_name">
                    <el-select
                        v-model="formData[field.field_name]"
                        :placeholder="field.config.placeholder || `请选择${field.field_label}`"
                        clearable
                        style="width: 100%"
                    >
                        <el-option v-for="option in field.config.options || []" :key="option.value" :label="option.label" :value="option.value" />
                    </el-select>
                </el-form-item>

                <!-- 图片上传 -->
                <el-form-item v-else-if="field.field_type === 'image'" :label="field.field_label" :prop="field.field_name">
                    <el-upload
                        class="avatar-uploader"
                        action="/admin/Upload/image"
                        :show-file-list="false"
                        :on-success="(response: any) => handleImageSuccess(response, field.field_name)"
                        :before-upload="beforeImageUpload"
                    >
                        <img v-if="formData[field.field_name]" :src="formData[field.field_name]" class="avatar" />
                        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                    </el-upload>
                </el-form-item>
            </template>
        </el-form>
        <div v-else-if="loading" class="loading-container">
            <el-skeleton :rows="5" animated />
        </div>
        <div v-else class="no-fields">
            <el-empty description="暂无表单字段配置" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules, UploadRawFile } from 'element-plus'

interface FieldConfig {
    placeholder?: string
    maxlength?: number
    rows?: number
    min?: number
    max?: number
    step?: number
    options?: Array<{ label: string; value: string | number }>
}

interface FormField {
    field_name: string
    field_label: string
    field_type: string
    is_required: boolean
    config: FieldConfig
    sort: number
}

interface Props {
    modelValue: Record<string, any>
    fields: FormField[]
    labelPosition?: 'left' | 'right' | 'top'
    labelWidth?: number
}

const props = withDefaults(defineProps<Props>(), {
    labelPosition: 'right',
    labelWidth: 120,
})

const emit = defineEmits<{
    'update:modelValue': [value: Record<string, any>]
}>()

const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const formData = reactive<Record<string, any>>({})

// 表单字段（按sort排序）
const formFields = computed(() => {
    return [...props.fields].sort((a, b) => a.sort - b.sort)
})

// 表单验证规则
const formRules = computed<FormRules>(() => {
    const rules: FormRules = {}

    formFields.value.forEach((field) => {
        if (field.is_required) {
            rules[field.field_name] = [
                {
                    required: true,
                    message: `${field.field_label}不能为空`,
                    trigger: ['blur', 'change'],
                },
            ]
        }
    })

    return rules
})

// 监听props.modelValue变化
watch(
    () => props.modelValue,
    (newVal) => {
        Object.assign(formData, newVal)
    },
    { immediate: true, deep: true }
)

// 监听formData变化，同步到父组件
watch(
    formData,
    (newVal) => {
        emit('update:modelValue', { ...newVal })
    },
    { deep: true }
)

// 图片上传成功处理
const handleImageSuccess = (response: any, fieldName: string) => {
    if (response.code === 1) {
        formData[fieldName] = response.data.file.url
    } else {
        ElMessage.error(response.msg || '上传失败')
    }
}

// 图片上传前验证
const beforeImageUpload = (rawFile: UploadRawFile) => {
    const isJPG = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png'
    const isLt2M = rawFile.size / 1024 / 1024 < 2

    if (!isJPG) {
        ElMessage.error('只能上传 JPG/PNG 格式的图片!')
        return false
    }
    if (!isLt2M) {
        ElMessage.error('图片大小不能超过 2MB!')
        return false
    }
    return true
}

// 表单验证
const validate = () => {
    return formRef.value?.validate()
}

// 重置表单
const resetFields = () => {
    formRef.value?.resetFields()
}

// 暴露方法给父组件
defineExpose({
    validate,
    resetFields,
})
</script>

<style scoped>
.dynamic-form {
    width: 100%;
}

.loading-container {
    padding: 20px;
}

.no-fields {
    padding: 40px 20px;
    text-align: center;
}

.avatar-uploader .avatar {
    width: 100px;
    height: 100px;
    display: block;
    border-radius: 6px;
}

.avatar-uploader .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
    line-height: 100px;
}
</style>
