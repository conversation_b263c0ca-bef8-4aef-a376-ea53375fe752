export default {
    id: 'ID',
    order_id: '订单ID',
    order__service_title: '服务标题',
    order__service_desc: '服务描述',
    order__contact_mobile: '联系手机号',
    order__contact_email: '联系邮箱',
    order__contact_qq: '联系QQ',
    order__contact_wechat: '联系微信',
    order__service_price: '服务单价',
    order__total_amount: '订单总金额',
    order__create_time: '创建时间',
    order__update_time: '更新时间',
    order_no: '订单编号',
    user_id: '申请用户ID',
    user__username: '用户名',
    refund_no: '退款单号',
    refund_type: '申请类型',
    'refund_type refund_only': '仅退款',
    'refund_type return_refund': '退货退款',
    'refund_type exchange': '换货',
    refund_amount: '申请退款金额',
    reason: '申请原因',
    description: '详细说明',
    evidence_images: '凭证图片（JSON数组）',
    status: '处理状态',
    'status pending': '待处理',
    'status approved': '已同意',
    'status rejected': '已拒绝',
    'status processing': '处理中',
    'status completed': '已完成',
    'status cancelled': '已取消',
    admin_remark: '管理员备注',
    process_time: '处理时间',
    completion_time: '完成时间',
    actual_refund_amount: '实际退款金额',
    refund_method: '退款方式',
    refund_transaction_id: '退款交易号',
    refund_time: '退款时间',
    create_time: '创建时间',
    update_time: '更新时间',
    'quick Search Fields': 'ID',
}
