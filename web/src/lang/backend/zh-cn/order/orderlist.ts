export default {
    id: '订单ID',
    order_no: '订单编号',
    user_id: '用户ID',
    user__username: '用户名',
    service_id: '购买的服务ID（关联ba_content表）',
    service__title: '主标题',
    service_title: '服务标题',
    service_desc: '服务描述',
    cover_image: '服务封面图片',
    contact_mobile: '联系手机号',
    contact_email: '联系邮箱',
    contact_qq: '联系QQ',
    contact_wechat: '联系微信',
    service_price: '服务单价',
    discount_amount: '优惠金额',
    total_amount: '订单总金额',
    status: '订单状态',
    'status pending': '待付款',
    'status paid': '已付款',
    'status in_progress': '进行中',
    'status completed': '已完成',
    'status cancelled': '已取消',
    'status refunded': '已退款',
    payment_status: '支付状态',
    'payment_status unpaid': '未支付',
    'payment_status paying': '支付中',
    'payment_status paid': '已支付',
    'payment_status failed': '支付失败',
    'payment_status refunded': '已退款',
    payment_method: '支付方式',
    'payment_method wechat': '微信支付',
    'payment_method alipay': '支付宝',
    'payment_method balance': '余额支付',
    'payment_method other': '其他方式',
    trade_no: '第三方交易号',
    out_trade_no: '商户订单号（支付模块使用）',
    payment_time: '支付时间',
    payment_data: '支付回调数据（JSON格式）',
    user_remark: '用户备注（购买时填写的需求说明）',
    admin_remark: '管理员备注',
    expire_time: '订单过期时间（未支付订单30分钟过期）',
    create_time: '创建时间',
    update_time: '更新时间',
    delete_time: '删除时间',
    'quick Search Fields': '订单ID',
}
