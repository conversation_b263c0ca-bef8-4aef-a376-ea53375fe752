export default {
    content: 'Content',
    id: 'ID',
    'Quick Search Fields': 'Quick Search Fields',
    'quick Search Fields': 'title, subtitle, tags, content',
    column_id: 'column_id',
    column__name: 'name',
    content_type: 'content_type',
    'content_type SERVICE': 'SERVICE',
    'content_type EXPERT': 'EXPERT',
    'content_type JOURNAL': 'JOURNAL',
    'content_type HEADLINE': 'HEADLINE',
    title: 'title',
    subtitle: 'subtitle',
    summary: 'summary',
    content_field: 'content',
    cover_image: 'cover_image',
    link_url: 'link_url',
    sort: 'sort',
    status: 'status',
    'status 0': 'Disable',
    'status 1': 'Enable',
    click_count: 'click_count',
    like_count: 'like_count',
    collect_count: 'collect_count',
    tags: 'tags',
    top_flag: 'top_flag',
    'top_flag opt0': 'Normal',
    'top_flag opt1': 'Top',
    'top_flag opt2': 'Global Top',
    homepage_recommend: 'homepage_recommend',
    'homepage_recommend 0': 'No',
    'homepage_recommend 1': 'Yes',
    del_flag: 'del_flag',
    'del_flag 0': 'Not deleted',
    'del_flag 1': 'Deleted',
    create_time: 'create_time',
    update_time: 'update_time',
    author: 'author',
    publish_date: 'publish_date',
    service_icon: 'service_icon',
    service_desc: 'service_desc',
    service_tags: 'service_tags',
    price: 'price',
    purchase_count: 'purchase_count',
    avatar_url: 'avatar_url',
    expert_name: 'expert_name',
    expert_number: 'expert_number',
    position: 'position',
    institution: 'institution',
    research_area: 'research_area',
    academic_achievements: 'academic_achievements',
    tutoring_experience: 'tutoring_experience',
    expertise_skills: 'expertise_skills',
    service_types: 'service_types',
    domain: 'domain',
    big_type: 'big_type',
    small_type: 'small_type',
    journal_type: 'journal_type',
    journal_datatype: 'journal_datatype',
    journal_zky: 'journal_zky',
    journal_jcr: 'journal_jcr',
    impact_factor: 'impact_factor',
    journal_data: 'journal_data',
    paper_requirements: 'paper_requirements',
    submission_guidelines: 'submission_guidelines',
    related_services: 'related_services',
    related_experts: 'related_experts',
    related_journals: 'related_journals',
}
