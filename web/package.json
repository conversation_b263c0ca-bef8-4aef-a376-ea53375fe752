{"name": "build-admin", "version": "2.3.3", "license": "Apache-2.0", "type": "module", "scripts": {"dev": "esno ./src/utils/build.ts && vite --force", "build": "vite build && esno ./src/utils/build.ts", "lint": "eslint .", "lint-fix": "eslint --fix .", "format": "npx prettier --write .", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueuse/core": "12.0.0", "axios": "1.9.0", "echarts": "5.5.1", "element-plus": "2.9.1", "font-awesome": "4.7.0", "lodash-es": "4.17.21", "mitt": "3.0.1", "nprogress": "0.2.0", "pinia": "2.3.0", "pinia-plugin-persistedstate": "4.2.0", "qrcode.vue": "3.6.0", "screenfull": "6.0.2", "sortablejs": "1.15.6", "v-code-diff": "1.13.1", "vue": "3.5.13", "vue-i18n": "11.1.3", "vue-router": "4.5.0", "@wangeditor-next/editor": "5.6.4", "@wangeditor-next/editor-for-vue": "5.1.14"}, "devDependencies": {"@eslint/js": "9.17.0", "@types/lodash-es": "4.17.12", "@types/node": "22.10.2", "@types/nprogress": "0.2.3", "@types/sortablejs": "1.15.8", "@vitejs/plugin-vue": "5.2.3", "async-validator": "4.2.5", "eslint": "9.17.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-vue": "9.32.0", "esno": "4.8.0", "globals": "15.14.0", "prettier": "3.4.2", "sass": "1.83.0", "typescript": "5.7.2", "typescript-eslint": "8.18.1", "vite": "6.3.5", "vue-tsc": "2.1.10"}, "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "esbuild", "v-code-diff", "vue-demi"]}}