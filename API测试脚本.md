# 🧪 API测试脚本

## 📋 测试准备

### 1. 获取认证Token
```bash
# 用户登录获取Token
curl -X POST "https://yanzhi.dlxingyun.com/api/User/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

### 2. 设置环境变量
```bash
# 设置Token（替换为实际Token）
export TOKEN="your_actual_token_here"
export BASE_URL="https://yanzhi.dlxingyun.com"
```

## 🛒 订单管理测试

### 1. 创建测试订单（沙箱模式）
```bash
curl -X POST "${BASE_URL}/api/User/createOrderTest" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "service_id": 1,
    "quantity": 1,
    "contact_mobile": "13800138000",
    "contact_email": "<EMAIL>",
    "contact_qq": "123456789",
    "contact_wechat": "test_wechat",
    "user_remark": "沙箱模式测试订单"
  }'
```

**预期返回：**
```json
{
  "code": 1,
  "msg": "订单创建成功（沙箱模式）",
  "data": {
    "order_id": 35,
    "order_no": "ORD202507111811148392",
    "total_amount": 208,
    "qr_code": "weixin://wxpay/bizpayurl?pr=sandboxORD202507111811148392",
    "expire_time": "2025-07-11 19:11:14",
    "note": "当前为沙箱模式，配置AppID后启用真实支付"
  }
}
```

### 2. 创建正式订单
```bash
curl -X POST "${BASE_URL}/api/User/createOrderAndPay" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "service_id": 1,
    "quantity": 1,
    "contact_mobile": "13800138000",
    "contact_email": "<EMAIL>",
    "user_remark": "正式订单测试"
  }'
```

### 3. 查询支付状态
```bash
curl -X POST "${BASE_URL}/api/User/checkPayStatus" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "order_id": 35
  }'
```

### 4. 取消订单
```bash
curl -X POST "${BASE_URL}/api/User/cancelOrder" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "order_id": 35,
    "reason": "测试取消"
  }'
```

### 5. 获取我的订单列表
```bash
curl -X GET "${BASE_URL}/api/User/myOrders?page=1&limit=10&status=pending" \
  -H "Authorization: Bearer ${TOKEN}"
```

### 6. 获取订单详情
```bash
curl -X GET "${BASE_URL}/api/User/orderDetail?order_id=35" \
  -H "Authorization: Bearer ${TOKEN}"
```

## 📊 数据统计测试

### 1. 我的订单统计
```bash
curl -X GET "${BASE_URL}/api/User/myOrderStats" \
  -H "Authorization: Bearer ${TOKEN}"
```

## 🔧 调试工具

### 1. 简单连接测试
```bash
curl -X POST "${BASE_URL}/api/User/simpleTest" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "service_id": 1
  }'
```

### 2. 数据库测试
```bash
curl -X POST "${BASE_URL}/api/User/testDatabase" \
  -H "Authorization: Bearer ${TOKEN}"
```

### 3. 支付配置检查
```bash
curl -X POST "${BASE_URL}/api/User/checkPayConfig" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}"
```

**预期返回：**
```json
{
  "code": 1,
  "msg": "支付配置检查完成",
  "data": {
    "payment_mode": "沙箱模式",
    "completion_rate": "83.3%",
    "missing_configs": ["App的AppID(app_id)"],
    "is_ready": false,
    "summary": "❌ 配置不完整，缺少 1 项配置"
  }
}
```

## 🎯 完整测试流程

### 测试脚本（完整版）
```bash
#!/bin/bash

# 配置
TOKEN="your_token_here"
BASE_URL="https://yanzhi.dlxingyun.com"

echo "=== 开始支付功能测试 ==="

# 0. 检查支付配置
echo "0. 检查支付配置..."
CONFIG_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/User/checkPayConfig" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}")

echo "配置检查结果: $CONFIG_RESPONSE"

# 检查配置是否完整
IS_READY=$(echo $CONFIG_RESPONSE | jq -r '.data.is_ready')
if [ "$IS_READY" = "false" ]; then
    echo "⚠️ 警告：支付配置不完整，将会收到配置错误"
    echo "缺少配置：$(echo $CONFIG_RESPONSE | jq -r '.data.missing_configs[]')"
fi

# 1. 创建订单
echo "1. 创建测试订单..."
ORDER_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/User/createOrderTest" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "service_id": 1,
    "quantity": 1,
    "contact_mobile": "13800138000",
    "contact_email": "<EMAIL>",
    "user_remark": "自动化测试订单"
  }')

echo "订单创建结果: $ORDER_RESPONSE"

# 提取订单ID
ORDER_ID=$(echo $ORDER_RESPONSE | jq -r '.data.order_id')
echo "订单ID: $ORDER_ID"

# 2. 查询支付状态
echo "2. 查询支付状态..."
STATUS_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/User/checkPayStatus" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d "{\"order_id\": $ORDER_ID}")

echo "支付状态: $STATUS_RESPONSE"

# 3. 获取订单列表
echo "3. 获取订单列表..."
LIST_RESPONSE=$(curl -s -X GET "${BASE_URL}/api/User/myOrders?page=1&limit=5" \
  -H "Authorization: Bearer ${TOKEN}")

echo "订单列表: $LIST_RESPONSE"

echo "=== 测试完成 ==="
```

## 📱 前端JavaScript测试

### 1. 完整支付流程
```javascript
class PaymentTest {
    constructor(token, baseUrl) {
        this.token = token;
        this.baseUrl = baseUrl;
    }

    async createOrder(serviceId = 1) {
        const response = await fetch(`${this.baseUrl}/api/User/createOrderTest`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify({
                service_id: serviceId,
                quantity: 1,
                contact_mobile: '13800138000',
                contact_email: '<EMAIL>',
                user_remark: '前端测试订单'
            })
        });

        const result = await response.json();
        console.log('创建订单结果:', result);
        return result;
    }

    async generateQRCode(codeUrl) {
        // 生成二维码
        const QRCode = require('qrcode');
        const canvas = document.getElementById('qrcode-canvas');
        
        await QRCode.toCanvas(canvas, codeUrl, {
            width: 256,
            margin: 2,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        });
    }

    async pollPaymentStatus(orderId) {
        return new Promise((resolve) => {
            const timer = setInterval(async () => {
                try {
                    const response = await fetch(`${this.baseUrl}/api/User/checkPayStatus`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.token}`
                        },
                        body: JSON.stringify({ order_id: orderId })
                    });

                    const result = await response.json();
                    console.log('支付状态查询:', result);

                    if (result.data.payment_status === 'paid') {
                        clearInterval(timer);
                        resolve(result);
                    }
                } catch (error) {
                    console.error('查询支付状态失败:', error);
                    clearInterval(timer);
                    resolve(null);
                }
            }, 3000);
        });
    }

    async testFullPaymentFlow() {
        console.log('开始完整支付流程测试...');
        
        // 1. 创建订单
        const orderResult = await this.createOrder();
        if (orderResult.code !== 1) {
            console.error('创建订单失败:', orderResult.msg);
            return;
        }

        const { order_id, qr_code } = orderResult.data;
        
        // 2. 生成二维码
        await this.generateQRCode(qr_code);
        
        // 3. 轮询支付状态
        console.log('开始轮询支付状态...');
        const paymentResult = await this.pollPaymentStatus(order_id);
        
        if (paymentResult) {
            console.log('支付成功!', paymentResult);
        } else {
            console.log('支付超时或失败');
        }
    }
}

// 使用示例
const payTest = new PaymentTest('your_token_here', 'https://yanzhi.dlxingyun.com');
payTest.testFullPaymentFlow();
```

## 🔍 错误排查

### 1. 常见错误处理
```bash
# 检查服务器日志
tail -f /www/wwwroot/yanzhi.dlxingyun.com/runtime/log/$(date +%Y%m%d).log

# 检查支付日志
tail -f /www/wwwroot/yanzhi.dlxingyun.com/runtime/pay/pay.log

# 检查PHP错误日志
tail -f /var/log/php_errors.log
```

### 2. 配置验证
```bash
# 验证配置文件
php -l /www/wwwroot/yanzhi.dlxingyun.com/config/pay.php

# 检查证书文件
ls -la /www/wwwroot/yanzhi.dlxingyun.com/creat/
```

---

**使用说明：**
1. 替换 `your_token_here` 为实际的认证Token
2. 根据实际情况调整 `service_id` 参数
3. 观察返回结果，验证功能是否正常
4. 如有问题，检查日志文件进行排查 