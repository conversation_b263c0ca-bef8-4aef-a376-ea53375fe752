<?php

namespace app\admin\controller;

use ba\Tree;
use app\common\controller\Backend;

/**
 * 学科分类
 */
class Subjectcategory extends Backend
{
    /**
     * Subjectcategory模型对象
     * @var object
     * @phpstan-var \app\admin\model\Subjectcategory
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time', 'update_time'];

    protected string|array $quickSearchField = ['id', 'name'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\admin\model\Subjectcategory();
    }

    /**
     * 查看
     * @throws \think\db\exception\DbException
     */
    public function index(): void
    {
        // 如果是select请求，返回下拉数据
        if ($this->request->param('select')) {
            $this->select();
            return;
        }

        // 获取所有数据
        $res = $this->model
            ->withoutGlobalScope()
            ->order('sort desc, id desc')
            ->select();

        // 转换为树形结构
        $data = $res->toArray();
        $tree = Tree::instance()->assembleChild($data, 'parent_id', 'id');
        
        $this->success('', [
            'list'   => $tree,
            'total'  => count($res),
            'remark' => get_route_remark(),
        ]);
    }

    /**
     * 添加
     * @throws \think\Exception
     */
    public function add(): void
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!$data) {
                $this->error(__('Parameter %s can not be empty', ['']));
            }

            $data = $this->excludeFields($data);
            if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                $data[$this->dataLimitField] = $this->auth->id;
            }

            $result = false;
            $this->model->startTrans();
            try {
                // 写入数据
                $result = $this->model->save($data);
                $this->model->commit();
            } catch (\Throwable $e) {
                $this->model->rollback();
                $this->error($e->getMessage());
            }
            if ($result !== false) {
                $this->success(__('Added successfully'));
            } else {
                $this->error(__('No rows were added'));
            }
        }

        $this->error(__('Parameter error'));
    }

    /**
     * 删除
     * @param array $ids
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function del(array $ids = []): void
    {
        if (!$this->request->isDelete() || !$ids) {
            $this->error(__('Parameter error'));
        }

        // 获取所有需要删除的ID（包括子级）
        $allIds = $this->getAllChildIds($ids);
        
        $pk          = $this->model->getPk();
        $adminIds    = $this->getDataLimitAdminIds();
        $count       = 0;
        $data        = $this->model->where($pk, 'in', $allIds);
        if ($adminIds) {
            $data = $data->where($this->dataLimitField, 'in', $adminIds);
        }

        $list = $data->select();
        $this->model->startTrans();
        try {
            foreach ($list as $v) {
                $count += $v->delete();
            }
            $this->model->commit();
        } catch (\Throwable $e) {
            $this->model->rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success(__('Deleted successfully'));
        } else {
            $this->error(__('No rows were deleted'));
        }
    }

    /**
     * 获取所有子级ID（递归）
     * @param array $parentIds
     * @return array
     */
    private function getAllChildIds(array $parentIds): array
    {
        $allIds = $parentIds;
        
        // 查找所有子级
        $children = $this->model->where('parent_id', 'in', $parentIds)->column('id');
        
        if (!empty($children)) {
            // 递归获取更深层的子级
            $deepChildren = $this->getAllChildIds($children);
            $allIds = array_merge($allIds, $deepChildren);
        }
        
        return array_unique($allIds);
    }

    /**
     * 获取下拉选择数据
     */
    public function select(): void
    {
        $isTree = $this->request->param('isTree', true);
        $parent_id = $this->request->param('parent_id', null);
        
        $where = [['status', '=', 1]];
        
        // 如果指定了parent_id，筛选特定父级下的子分类
        if ($parent_id !== null) {
            $where[] = ['parent_id', '=', $parent_id];
        }
        
        $data = $this->model->where($where)->order('sort desc, id desc')->select()->toArray();
        
        if ($isTree && $parent_id === null) {
            // 只有在没有指定parent_id且要求树形结构时才构建树
            $tree = Tree::instance()->assembleChild($data, 'parent_id', 'id');
        } else {
            $tree = $data;
        }
        
        $this->success('', [
            'options' => $tree
        ]);
    }

    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
}