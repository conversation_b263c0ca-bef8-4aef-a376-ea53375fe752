<?php

namespace app\admin\controller\order;

use Throwable;
use app\common\controller\Backend;

/**
 * 订单主管理
 */
class Orderlist extends Backend
{
    /**
     * Orderlist模型对象
     * @var object
     * @phpstan-var \app\admin\model\order\Orderlist
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time', 'update_time'];

    protected array $withJoinTable = ['user', 'service'];

    protected string|array $quickSearchField = ['id'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\admin\model\order\Orderlist();
    }

    /**
     * 查看
     * @throws Throwable
     */
    public function index(): void
    {
        // 如果是 select 则转发到 select 方法，若未重写该方法，其实还是继续执行 index
        if ($this->request->param('select')) {
            $this->select();
        }

        /**
         * 1. withJoin 不可使用 alias 方法设置表别名，别名将自动使用关联模型名称（小写下划线命名规则）
         * 2. 以下的别名设置了主表别名，同时便于拼接查询参数等
         * 3. paginate 数据集可使用链式操作 each(function($item, $key) {}) 遍历处理
         */
        list($where, $alias, $limit, $order) = $this->queryBuilder();
        $res = $this->model
            ->withJoin($this->withJoinTable, $this->withJoinType)
            ->alias($alias)
            ->where($where)
            ->order($order)
            ->paginate($limit);
        $res->visible(['user' => ['username'], 'service' => ['title']]);

        $this->success('', [
            'list'   => $res->items(),
            'total'  => $res->total(),
            'remark' => get_route_remark(),
        ]);
    }

    /**
     * 获取订单状态统计
     */
    public function getStatusStats(): void
    {
        $stats = [
            'total' => $this->model->count(),
            'pending' => $this->model->where('status', 'pending')->count(),
            'paid' => $this->model->where('status', 'paid')->count(),
            'in_progress' => $this->model->where('status', 'in_progress')->count(),
            'completed' => $this->model->where('status', 'completed')->count(),
            'cancelled' => $this->model->where('status', 'cancelled')->count(),
            'refunded' => $this->model->where('status', 'refunded')->count(),
        ];

        $this->success('获取成功', $stats);
    }

    /**
     * 更新订单状态
     */
    public function updateStatus(): void
    {
        $id = $this->request->post('id/d', 0);
        $status = $this->request->post('status', '');
        
        if (empty($id)) {
            $this->error('订单ID不能为空');
        }
        
        // 验证状态值
        $allowedStatus = ['pending', 'paid', 'in_progress', 'completed', 'cancelled', 'refunded'];
        if (!in_array($status, $allowedStatus)) {
            $this->error('无效的订单状态');
        }
        
        // 查找订单
        $order = $this->model->find($id);
        if (!$order) {
            $this->error('订单不存在');
        }
        
        // 验证状态转换是否合法（允许更灵活的状态转换）
        $validTransitions = [
            'paid' => ['in_progress', 'completed', 'cancelled'], // 已付款可以转为：进行中、已完成、已取消
            'in_progress' => ['completed', 'cancelled'], // 进行中可以转为：已完成、已取消
        ];
        
        if (!isset($validTransitions[$order->status]) || !in_array($status, $validTransitions[$order->status])) {
            $this->error('当前订单状态不允许此操作');
        }
        
       
            \think\facade\Db::startTrans();
            
            // 更新订单状态
            $oldStatus = $order->status;
            $order->status = $status;
            $order->update_time = time();
            $order->save();
            
            // 记录状态变更日志
            $statusLog = new \app\admin\model\order\Statuslog();
            $statusLog->order_id = $id;
            $statusLog->order_no = $order->order_no;
            $statusLog->from_status = $oldStatus;
            $statusLog->to_status = $status;
            $statusLog->operate_type = 'admin';
            $statusLog->operator_id = $this->auth->id;
            $statusLog->operator_name = $this->auth->nickname ?: $this->auth->username;
            
            $statusText = [
                'in_progress' => '订单设为进行中',
                'completed' => '订单设为已完成',
                'cancelled' => '订单已取消'
            ];
            $statusLog->remark = $statusText[$status] ?? '状态更新';
            $statusLog->save();
            
            \think\facade\Db::commit();
            
            $this->success('订单状态更新成功');
            
       
    }

    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
}