<?php

namespace app\admin\controller\order;

use Throwable;
use app\common\controller\Backend;

/**
 * 订单评价管理
 */
class Review extends Backend
{
    /**
     * Review模型对象
     * @var object
     * @phpstan-var \app\admin\model\order\Review
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time', 'update_time'];

    protected array $withJoinTable = ['order', 'user', 'service'];

    protected string|array $quickSearchField = ['id', 'service.title', 'user.username', 'content'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\admin\model\order\Review();
        $this->request->filter('clean_xss');
    }

    /**
     * 查看
     * @throws Throwable
     */
    public function index(): void
    {
        // 如果是 select 则转发到 select 方法，若未重写该方法，其实还是继续执行 index
        if ($this->request->param('select')) {
            $this->select();
        }

        /**
         * 1. withJoin 不可使用 alias 方法设置表别名，别名将自动使用关联模型名称（小写下划线命名规则）
         * 2. 以下的别名设置了主表别名，同时便于拼接查询参数等
         * 3. paginate 数据集可使用链式操作 each(function($item, $key) {}) 遍历处理
         */
        list($where, $alias, $limit, $order) = $this->queryBuilder();
        $res = $this->model
            ->withJoin($this->withJoinTable, $this->withJoinType)
            ->alias($alias)
            ->where($where)
            ->order($order)
            ->paginate($limit);
        $res->visible(['order' => ['service_title'], 'user' => ['username'], 'service' => ['title']]);

        $this->success('', [
            'list'   => $res->items(),
            'total'  => $res->total(),
            'remark' => get_route_remark(),
        ]);
    }

    /**
     * 添加
     * @throws Throwable
     */
    public function add(): void
    {
        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!$data) {
                $this->error(__('Parameter %s can not be empty', ['']));
            }

            // 数据验证
            if (empty($data['service_id'])) {
                $this->error('服务ID不能为空');
            }
            if (empty($data['user_id'])) {
                $this->error('用户ID不能为空');
            }
            if (empty($data['content'])) {
                $this->error('评价内容不能为空');
            }

            // 订单ID可以为空，支持添加虚拟评价
            if (empty($data['order_id'])) {
                $data['order_id'] = null;
            }

            // 设置默认值
            $data['status'] = $data['status'] ?? 1; // 默认审核通过
            $data['rating'] = $data['rating'] ?? 5; // 默认5星
            
            $data = $this->excludeFields($data);
            $result = false;
            $this->model->startTrans();
            try {
                $result = $this->model->save($data);
                $this->model->commit();
            } catch (Throwable $e) {
                $this->model->rollback();
                $this->error($e->getMessage());
            }
            if ($result !== false) {
                $this->success(__('Added successfully'));
            } else {
                $this->error(__('No rows were added'));
            }
        }

        $this->success('', [
            'remark' => get_route_remark(),
        ]);
    }

    /**
     * 编辑
     * @throws Throwable
     */
    public function edit(): void
    {
        $id  = $this->request->param($this->model->getPk());
        $row = $this->model->find($id);
        if (!$row) {
            $this->error(__('Record not found'));
        }

        if ($this->request->isPost()) {
            $data = $this->request->post();
            if (!$data) {
                $this->error(__('Parameter %s can not be empty', ['']));
            }

            // 订单ID可以为空，支持虚拟评价
            if (empty($data['order_id'])) {
                $data['order_id'] = null;
            }

            $data = $this->excludeFields($data);
            $result = false;
            $this->model->startTrans();
            try {
                $result = $row->save($data);
                $this->model->commit();
            } catch (Throwable $e) {
                $this->model->rollback();
                $this->error($e->getMessage());
            }
            if ($result !== false) {
                $this->success(__('Update successful'));
            } else {
                $this->error(__('No rows updated'));
            }
        }

        $this->success('', [
            'row'    => $row->toArray(),
            'remark' => get_route_remark(),
        ]);
    }

    /**
     * 若需重写查看、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
}