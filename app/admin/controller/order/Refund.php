<?php

namespace app\admin\controller\order;

use Throwable;
use app\common\controller\Backend;

/**
 * 售后退款申请管理
 */
class Refund extends Backend
{
    /**
     * Refund模型对象
     * @var object
     * @phpstan-var \app\admin\model\order\Refund
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time', 'update_time'];

    protected array $withJoinTable = ['order', 'user'];

    protected string|array $quickSearchField = ['id'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\admin\model\order\Refund();
    }

    /**
     * 查看
     * @throws Throwable
     */
    public function index(): void
    {
        // 如果是 select 则转发到 select 方法，若未重写该方法，其实还是继续执行 index
        if ($this->request->param('select')) {
            $this->select();
        }

        /**
         * 1. withJoin 不可使用 alias 方法设置表别名，别名将自动使用关联模型名称（小写下划线命名规则）
         * 2. 以下的别名设置了主表别名，同时便于拼接查询参数等
         * 3. paginate 数据集可使用链式操作 each(function($item, $key) {}) 遍历处理
         */
        list($where, $alias, $limit, $order) = $this->queryBuilder();
        $res = $this->model
            ->withJoin($this->withJoinTable, $this->withJoinType)
            ->alias($alias)
            ->where($where)
            ->order($order)
            ->paginate($limit);
        $res->visible(['order' => ['service_title','service_desc','contact_mobile','contact_email','contact_qq','contact_wechat','service_price','total_amount','create_time','update_time'], 'user' => ['username']]);

        $this->success('', [
            'list'   => $res->items(),
            'total'  => $res->total(),
            'remark' => get_route_remark(),
        ]);
    }

    /**
     * 处理退款申请（同意/拒绝）
     * @throws Throwable
     */
    public function processRefund(): void
    {
        $refundId = $this->request->post('refund_id/d', 0);
        $action = $this->request->post('action', ''); // approve=同意, reject=拒绝
        $adminRemark = $this->request->post('admin_remark', '');
        $actualRefundAmount = $this->request->post('actual_refund_amount', '');
        
        if (empty($refundId)) {
            $this->error('退款申请ID不能为空');
        }
        
        if (!in_array($action, ['approve', 'reject'])) {
            $this->error('操作类型错误');
        }
        
        if ($action === 'approve' && (empty($actualRefundAmount) || $actualRefundAmount <= 0)) {
            $this->error('同意退款时，实际退款金额不能为空');
        }
        
        // 查找退款申请
        $refund = \think\facade\Db::table('ba_order_refund')->where('id', $refundId)->find();
        if (!$refund) {
            $this->error('退款申请不存在');
        }
        
        if ($refund['status'] !== 'pending') {
            $this->error('只能处理待处理状态的退款申请');
        }
        
        // 查找对应订单
        $order = \think\facade\Db::table('ba_order')->where('id', $refund['order_id'])->find();
        if (!$order) {
            $this->error('对应订单不存在');
        }

        try {
            \think\facade\Db::startTrans();
            
            if ($action === 'approve') {
                // 同意退款
                $this->approveRefund($refund, $order, $actualRefundAmount, $adminRemark);
            } else {
                // 拒绝退款
                $this->rejectRefund($refund, $order, $adminRemark);
            }
            
            \think\facade\Db::commit();
            
            $actionText = $action === 'approve' ? '同意' : '拒绝';
            $this->success($actionText . '退款申请成功');
            
        } catch (\think\exception\HttpResponseException $e) {
            throw $e;
        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            $this->error('处理失败：' . $e->getMessage());
        }
    }
    
    /**
     * 同意退款
     */
    private function approveRefund($refund, $order, $actualRefundAmount, $adminRemark): void
    {
        // 更新退款申请状态
        \think\facade\Db::table('ba_order_refund')
            ->where('id', $refund['id'])
            ->update([
                'status' => 'approved',
                'actual_refund_amount' => $actualRefundAmount,
                'admin_remark' => $adminRemark,
                'process_time' => time(),
                'update_time' => time()
            ]);
        
        // 调用第三方退款接口
        $this->processThirdPartyRefund($order, $actualRefundAmount);
        
        // 更新订单状态为已退款
        \think\facade\Db::table('ba_order')
            ->where('id', $order['id'])
            ->update([
                'status' => 'refunded',
                'update_time' => time()
            ]);
        
        // 记录订单状态变更日志
        $statusLog = new \app\admin\model\order\Statuslog();
        $statusLog->order_id = $order['id'];
        $statusLog->order_no = $order['order_no'];
        $statusLog->from_status = $order['status'];
        $statusLog->to_status = 'refunded';
        $statusLog->operate_type = 'admin';
        $statusLog->operator_id = $this->auth->id;
        $statusLog->operator_name = $this->auth->nickname ?: $this->auth->username;
        $statusLog->remark = '管理员同意退款申请 - 退款金额：' . $actualRefundAmount . '元，备注：' . $adminRemark;
        $statusLog->save();
    }
    
    /**
     * 拒绝退款
     */
    private function rejectRefund($refund, $order, $adminRemark): void
    {
        // 更新退款申请状态
        \think\facade\Db::table('ba_order_refund')
            ->where('id', $refund['id'])
            ->update([
                'status' => 'rejected',
                'admin_remark' => $adminRemark,
                'process_time' => time(),
                'update_time' => time()
            ]);
        
        // 记录订单状态变更日志
        $statusLog = new \app\admin\model\order\Statuslog();
        $statusLog->order_id = $order['id'];
        $statusLog->order_no = $order['order_no'];
        $statusLog->from_status = $order['status'];
        $statusLog->to_status = $order['status']; // 订单状态不变
        $statusLog->operate_type = 'admin';
        $statusLog->operator_id = $this->auth->id;
        $statusLog->operator_name = $this->auth->nickname ?: $this->auth->username;
        $statusLog->remark = '管理员拒绝退款申请 - 原因：' . $adminRemark;
        $statusLog->save();
    }
    
    /**
     * 处理第三方退款（微信/支付宝）
     */
    private function processThirdPartyRefund($order, $refundAmount): void
    {
        if (empty($order['payment_method']) || empty($order['trade_no'])) {
            // 如果没有支付方式或交易号，只做系统内退款标记
            return;
        }
        
        $config = \ba\PayLib::getConfig();
        $refundNo = 'TK' . date('YmdHis') . mt_rand(1000, 9999);
        
        try {
            if ($order['payment_method'] === 'wechat') {
                // 微信退款
                $refundData = [
                    'out_trade_no' => $order['order_no'],
                    'out_refund_no' => $refundNo,
                    'amount' => [
                        'refund' => intval(bcmul($refundAmount, 100)), // 退款金额（分）
                        'total' => intval(bcmul($order['total_amount'], 100)), // 订单总金额（分）
                        'currency' => 'CNY'
                    ],
                    'reason' => '用户申请退款'
                ];
                
                $result = \Yansongda\Pay\Pay::wechat($config)->refund($refundData);
                
                // 更新退款记录
                \think\facade\Db::table('ba_order_refund')
                    ->where('order_id', $order['id'])
                    ->where('status', 'approved')
                    ->update([
                        'refund_method' => 'wechat',
                        'refund_transaction_id' => $result['refund_id'] ?? '',
                        'refund_time' => time(),
                        'status' => 'completed'
                    ]);
                
            } elseif ($order['payment_method'] === 'alipay') {
                // 支付宝退款
                $refundData = [
                    'out_trade_no' => $order['order_no'],
                    'refund_amount' => sprintf('%.2f', $refundAmount),
                    'out_request_no' => $refundNo,
                    'refund_reason' => '用户申请退款'
                ];
                
                $result = \Yansongda\Pay\Pay::alipay($config)->refund($refundData);
                
                // 更新退款记录
                \think\facade\Db::table('ba_order_refund')
                    ->where('order_id', $order['id'])
                    ->where('status', 'approved')
                    ->update([
                        'refund_method' => 'alipay',
                        'refund_transaction_id' => $result['trade_no'] ?? '',
                        'refund_time' => time(),
                        'status' => 'completed'
                    ]);
            }
            
            \think\facade\Log::info('退款成功：订单' . $order['order_no'] . '，金额' . $refundAmount . '元');
            
        } catch (\Exception $e) {
            \think\facade\Log::error('第三方退款失败：' . $e->getMessage());
            
            // 退款失败，更新状态为处理中，需要人工处理
            \think\facade\Db::table('ba_order_refund')
                ->where('order_id', $order['id'])
                ->where('status', 'approved')
                ->update([
                    'status' => 'processing',
                    'admin_remark' => '自动退款失败，需要人工处理：' . $e->getMessage()
                ]);
                
            throw new \Exception('第三方退款调用失败，请人工处理：' . $e->getMessage());
        }
    }

    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
}