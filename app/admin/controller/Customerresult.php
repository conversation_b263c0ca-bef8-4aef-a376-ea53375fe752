<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 客户成果管理
 */
class Customerresult extends Backend
{
    /**
     * Customerresult模型对象
     * @var object
     * @phpstan-var \app\admin\model\Customerresult
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time', 'update_time'];

    protected string $weighField = 'sort';

    protected string|array $quickSearchField = ['id'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\admin\model\Customerresult();
    }


    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
}