<?php

namespace app\admin\controller;

use Throwable;
use app\common\controller\Backend;

/**
 * 内容模型管理
 */
class ContentModel extends Backend
{
    /**
     * ContentModel模型对象
     * @var object
     * @phpstan-var \app\admin\model\ContentModel
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time', 'update_time'];

    protected array $withJoinTable = ['fields'];

    protected string|array $quickSearchField = ['id', 'name', 'code'];

    public function initialize(): void
    {
        parent::initialize();
        $this->model = new \app\admin\model\ContentModel();
    }

    /**
     * 获取内容模型的字段配置
     */
    public function getModelFields(): void
    {
        $modelId = $this->request->param('model_id');
        if (!$modelId) {
            $this->error('模型ID不能为空');
        }

        $model = $this->model->find($modelId);
        if (!$model) {
            $this->error('模型不存在');
        }

        $formFields = $model->getFormFields();
        $this->success('', [
            'fields' => $formFields,
            'model' => $model->toArray()
        ]);
    }

    /**
     * 根据栏目获取内容模型
     */
    public function getByColumn(): void
    {
        $columnId = $this->request->param('column_id');

        if (!$columnId) {
            $this->error('栏目ID不能为空');
        }

        // 从栏目获取内容模型
        $column = \app\admin\model\Column::with('contentModel')->find($columnId);
        if (!$column || !$column->contentModel) {
            $this->error('未找到对应的内容模型');
        }

        $contentModel = $column->contentModel;
        $formFields = $contentModel->getFormFields();
        $this->success('', [
            'fields' => $formFields,
            'model' => $contentModel->toArray()
        ]);
    }
} 