<?php

namespace app\admin\controller;

use Throwable;
use app\common\controller\Backend;
use think\facade\Db;

/**
 * 内容管理
 */
class Content extends Backend
{
    /**
     * Content模型对象
     * @var object
     * @phpstan-var \app\admin\model\Content
     */
    protected object $model;

    protected array|string $preExcludeFields = ['id', 'create_time', 'update_time'];

    protected array $withJoinTable = ['column'];

    protected string|array $quickSearchField = ['id', 'title'];

    public function initialize(): void
    {
        parent::initialize();
        
        // 设置富文本过滤器，保留HTML标签但清理XSS
        $this->request->filter('clean_xss');
        
        $this->model = new \app\admin\model\Content();
    }

    /**
     * 查看
     * @throws Throwable
     */
    public function index(): void
    {
        // 如果是 select 则转发到 select 方法，若未重写该方法，其实还是继续执行 index
        if ($this->request->param('select')) {
            $this->select();
        }

        /**
         * 1. withJoin 不可使用 alias 方法设置表别名，别名将自动使用关联模型名称（小写下划线命名规则）
         * 2. 以下的别名设置了主表别名，同时便于拼接查询参数等
         * 3. paginate 数据集可使用链式操作 each(function($item, $key) {}) 遍历处理
         */
        list($where, $alias, $limit, $order) = $this->queryBuilder();
        
        // 处理内容类型筛选（用于远程选择组件）
        $contentType = $this->request->param('content_type');
        if (!empty($contentType)) {
            $where[] = ['content_type', '=', $contentType];
        }
        
        $res = $this->model
            ->withJoin($this->withJoinTable, $this->withJoinType)
            ->alias($alias)
            ->where($where)
            ->order($order)
            ->paginate($limit);
        $res->visible(['column' => ['name']]);

        $this->success('', [
            'list'   => $res->items(),
            'total'  => $res->total(),
            'remark' => get_route_remark(),
        ]);
    }

    /**
     * 获取期刊配置选项
     * @throws Throwable
     */
    public function getJournalConfig(): void
    {
        $configKeys = ['journal_type', 'journal_datatype', 'journal_zky', 'journal_jcr'];
        $result = [];
        
        foreach ($configKeys as $key) {
            $configValue = get_sys_config($key);
            if ($configValue && is_array($configValue)) {
                // 检查是否是期望的格式 [{"key": "xxx", "value": "xxx"}, ...]
                if (isset($configValue[0]) && is_array($configValue[0]) && isset($configValue[0]['key']) && isset($configValue[0]['value'])) {
                    $result[$key] = $configValue;
                } else {
                    $result[$key] = [];
                }
            } else {
                $result[$key] = [];
            }
        }
        
        // 获取学科分类数据
        $bigTypes = Db::name('subject_category')
            ->where('parent_id', 0)
            ->where('status', 1)
            ->order('sort desc, id desc')
            ->field('id as `key`, name as value')
            ->select()
            ->toArray();
            
        $smallTypes = Db::name('subject_category')
            ->where('parent_id', '>', 0)
            ->where('status', 1)
            ->order('sort desc, id desc')
            ->field('id as `key`, name as value, parent_id')
            ->select()
            ->toArray();
        
        $result['big_type'] = $bigTypes;
        $result['small_type'] = $smallTypes;
        
        $this->success('获取成功', $result);
    }

    /**
     * 获取关联服务选项
     * @throws Throwable
     */
    public function getRelatedServices(): void
    {
        $keyword = $this->request->param('quickSearch', '');
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        
        $where = [
            ['status', '=', 1],
            ['content_type', '=', 'SERVICE']
        ];
        
        if (!empty($keyword)) {
            $where[] = ['title', 'like', '%' . $keyword . '%'];
        }
        
        $result = $this->model
            ->where($where)
            ->field('id,title,subtitle,service_desc,cover_image,price')
            ->order('sort desc, create_time desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);
        
        $list = $result->toArray();
        
        // 格式化数据用于远程下拉选择
        $options = [];
        foreach ($list['data'] as $item) {
            $options[] = [
                'id' => $item['id'],
                'name' => $item['title'] . ($item['subtitle'] ? ' - ' . $item['subtitle'] : ''),
                'title' => $item['title'],
                'subtitle' => $item['subtitle'],
                'service_desc' => $item['service_desc'],
                'cover_image' => $item['cover_image'],
                'price' => $item['price']
            ];
        }
        
        $this->success('', [
            'options' => $options,
            'total' => $list['total']
        ]);
    }
    
    /**
     * 获取关联专家选项
     * @throws Throwable
     */
    public function getRelatedExperts(): void
    {
        $keyword = $this->request->param('quickSearch', '');
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        
        $where = [
            ['status', '=', 1],
            ['content_type', '=', 'EXPERT']
        ];
        
        if (!empty($keyword)) {
            $where[] = ['title|expert_name|position|institution|research_area', 'like', '%' . $keyword . '%'];
        }
        
        $result = $this->model
            ->where($where)
            ->field('id,title,expert_name,position,institution,research_area,avatar_url,summary')
            ->order('sort desc, create_time desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);
        
        $list = $result->toArray();
        
        // 格式化数据用于远程下拉选择
        $options = [];
        foreach ($list['data'] as $item) {
            $displayName = $item['expert_name'] ?: $item['title'];
            if ($item['position']) {
                $displayName .= ' - ' . $item['position'];
            }
            if ($item['institution']) {
                $displayName .= ' (' . $item['institution'] . ')';
            }
            
            $options[] = [
                'id' => $item['id'],
                'name' => $displayName,
                'title' => $item['title'],
                'expert_name' => $item['expert_name'],
                'position' => $item['position'],
                'institution' => $item['institution'],
                'research_area' => $item['research_area'],
                'avatar_url' => $item['avatar_url'],
                'summary' => $item['summary']
            ];
        }
        
        $this->success('', [
            'options' => $options,
            'total' => $list['total']
        ]);
    }
    
    /**
     * 获取关联期刊选项
     * @throws Throwable
     */
    public function getRelatedJournals(): void
    {
        $keyword = $this->request->param('quickSearch', '');
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        
        $where = [
            ['status', '=', 1],
            ['content_type', '=', 'JOURNAL']
        ];
        
        if (!empty($keyword)) {
            $where[] = ['title|domain|big_type|small_type', 'like', '%' . $keyword . '%'];
        }
        
        $result = $this->model
            ->where($where)
            ->field('id,title,domain,big_type,small_type,journal_type,impact_factor,cover_image,summary')
            ->order('sort desc, create_time desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);
        
        $list = $result->toArray();
        
        // 格式化数据用于远程下拉选择
        $options = [];
        foreach ($list['data'] as $item) {
            $displayName = $item['title'];
            if ($item['domain']) {
                $displayName .= ' - ' . $item['domain'];
            }
            if ($item['impact_factor']) {
                $displayName .= ' (IF: ' . $item['impact_factor'] . ')';
            }
            
            $options[] = [
                'id' => $item['id'],
                'name' => $displayName,
                'title' => $item['title'],
                'domain' => $item['domain'],
                'big_type' => $item['big_type'],
                'small_type' => $item['small_type'],
                'journal_type' => $item['journal_type'],
                'impact_factor' => $item['impact_factor'],
                'cover_image' => $item['cover_image'],
                'summary' => $item['summary']
            ];
        }
        
        $this->success('', [
            'options' => $options,
            'total' => $list['total']
        ]);
    }

    /**
     * 更新期刊配置选项
     * @throws Throwable
     */
    public function updateJournalConfig(): void
    {
        $data = $this->request->post();
        
        if (empty($data)) {
            $this->error('参数不能为空');
        }
        
        $configKeys = ['journal_type', 'journal_datatype', 'journal_zky', 'journal_jcr'];
        $configModel = new \app\admin\model\Config();
        
        try {
            $configModel->startTrans();
            
            foreach ($configKeys as $key) {
                if (isset($data[$key])) {
                    // 验证数据格式
                    $configData = $data[$key];
                    if (!is_array($configData)) {
                        throw new \Exception("配置项 {$key} 数据格式错误");
                    }
                    
                    // 验证每个配置项的格式
                    foreach ($configData as $item) {
                        if (!is_array($item) || !isset($item['key']) || !isset($item['value'])) {
                            throw new \Exception("配置项 {$key} 数据格式错误，必须包含 key 和 value 字段");
                        }
                        
                        if (empty($item['key']) || empty($item['value'])) {
                            throw new \Exception("配置项 {$key} 的 key 和 value 不能为空");
                        }
                    }
                    
                    // 更新配置项
                    $config = $configModel->where('name', $key)->find();
                    if ($config) {
                        $config->value = json_encode($configData);
                        $config->save();
                    } else {
                        // 如果配置项不存在，创建新的配置项
                        $configModel->save([
                            'name' => $key,
                            'value' => json_encode($configData),
                            'title' => $this->getConfigTitle($key),
                            'type' => 'array',
                            'group' => 'journal',
                            'sort' => 100,
                            'rule' => '',
                            'extend' => '',
                            'remark' => $this->getConfigRemark($key),
                        ]);
                    }
                }
            }
            
            $configModel->commit();
            $this->success('配置更新成功');
            
        } catch (\Exception $e) {
            $configModel->rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * 获取配置项标题
     * @param string $key
     * @return string
     */
    private function getConfigTitle(string $key): string
    {
        $titles = [
            'journal_type' => '期刊类型',
            'journal_datatype' => '数据库类型',
            'journal_zky' => '中科院分区',
            'journal_jcr' => 'JCR分区',
        ];
        
        return $titles[$key] ?? $key;
    }

    /**
     * 获取配置项说明
     * @param string $key
     * @return string
     */
    private function getConfigRemark(string $key): string
    {
        $remarks = [
            'journal_type' => '期刊类型配置选项',
            'journal_datatype' => '数据库类型配置选项',
            'journal_zky' => '中科院分区配置选项',
            'journal_jcr' => 'JCR分区配置选项',
        ];
        
        return $remarks[$key] ?? '';
    }

    /**
     * 若需重写查看、编辑、删除等方法，请复制 @see \app\admin\library\traits\Backend 中对应的方法至此进行重写
     */
}