<?php

namespace app\admin\model;

use think\Model;

/**
 * ContentModel
 */
class ContentModel extends Model
{
    // 表名
    protected $name = 'content_model';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    /**
     * 关联内容模型字段
     */
    public function fields(): \think\model\relation\HasMany
    {
        return $this->hasMany(\app\admin\model\ContentModelField::class, 'model_id', 'id')
            ->where('status', '1')
            ->order('sort', 'asc');
    }

    /**
     * 关联栏目
     */
    public function columns(): \think\model\relation\HasMany
    {
        return $this->hasMany(\app\admin\model\Column::class, 'content_model_id', 'id');
    }



    /**
     * 获取表单字段配置
     */
    public function getFormFields(): array
    {
        $fields = $this->fields()->where('is_form', '1')->select();
        $formFields = [];
        
        foreach ($fields as $field) {
            $config = json_decode($field->field_config, true) ?: [];
            $formFields[] = [
                'field_name' => $field->field_name,
                'field_label' => $field->field_label,
                'field_type' => $field->field_type,
                'is_required' => $field->is_required == '1',
                'config' => $config,
                'sort' => $field->sort
            ];
        }
        
        return $formFields;
    }

    /**
     * 获取列表字段配置
     */
    public function getListFields(): array
    {
        $fields = $this->fields()->where('is_list', '1')->select();
        $listFields = [];
        
        foreach ($fields as $field) {
            $config = json_decode($field->field_config, true) ?: [];
            $listFields[] = [
                'field_name' => $field->field_name,
                'field_label' => $field->field_label,
                'field_type' => $field->field_type,
                'config' => $config,
                'sort' => $field->sort
            ];
        }
        
        return $listFields;
    }
} 