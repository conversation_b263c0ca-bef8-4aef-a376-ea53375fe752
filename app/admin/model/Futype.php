<?php

namespace app\admin\model;

use think\Model;

/**
 * Futype
 */
class Futype extends Model
{
    // 表名
    protected $name = 'futype';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    protected static function onAfterInsert($model): void
    {
        if (is_null($model->weigh)) {
            $pk = $model->getPk();
            if (strlen($model[$pk]) >= 19) {
                $model->where($pk, $model[$pk])->update(['weigh' => $model->count()]);
            } else {
                $model->where($pk, $model[$pk])->update(['weigh' => $model[$pk]]);
            }
        }
    }
}