<?php

namespace app\admin\model;

use think\Model;

/**
 * ContentModelField
 */
class ContentModelField extends Model
{
    // 表名
    protected $name = 'content_model_field';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    /**
     * 关联内容模型
     */
    public function model(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\ContentModel::class, 'model_id', 'id');
    }

    /**
     * 获取字段配置数组
     */
    public function getFieldConfigAttr($value): array
    {
        return json_decode($value, true) ?: [];
    }

    /**
     * 设置字段配置
     */
    public function setFieldConfigAttr($value): string
    {
        return is_array($value) ? json_encode($value) : $value;
    }
} 