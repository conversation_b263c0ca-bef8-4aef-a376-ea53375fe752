<?php

namespace app\admin\model;

use think\Model;

/**
 * Content
 */
class Content extends Model
{
    // 表名
    protected $name = 'content';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;


    public function getImpactFactorAttr($value): ?float
    {
        return is_null($value) ? null : (float)$value;
    }

    public function column(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\Column::class, 'column_id', 'id');
    }


}