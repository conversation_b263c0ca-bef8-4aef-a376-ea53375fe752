<?php

namespace app\admin\model\oauth;

use think\Model;
use app\admin\model\User;
use think\model\relation\BelongsTo;

/**
 * Log
 */
class Log extends Model
{
    // 表名
    protected $name = 'oauth_log';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    protected $updateTime         = false;

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}