<?php

namespace app\admin\model;

use think\Model;

/**
 * Column
 */
class Column extends Model
{
    // 表名
    protected $name = 'column';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    protected static function onAfterInsert($model): void
    {
        if (is_null($model->sort)) {
            $pk = $model->getPk();
            if (strlen($model[$pk]) >= 19) {
                $model->where($pk, $model[$pk])->update(['sort' => $model->count()]);
            } else {
                $model->where($pk, $model[$pk])->update(['sort' => $model[$pk]]);
            }
        }
    }

    /**
     * 关联内容模型
     */
    public function contentModel(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\ContentModel::class, 'content_model_id', 'id');
    }
}