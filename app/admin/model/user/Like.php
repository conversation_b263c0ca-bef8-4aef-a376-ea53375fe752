<?php

namespace app\admin\model\user;

use think\Model;

/**
 * Like
 */
class Like extends Model
{
    // 表名
    protected $name = 'user_like';

 

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 时间字段
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    public function user(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\User::class, 'user_id', 'id');
    }

    public function content(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\Content::class, 'content_id', 'id');
    }

    /**
     * 检查用户是否点赞了指定内容
     * @param int $userId 用户ID
     * @param int $contentId 内容ID
     * @return bool
     */
    public static function isLiked(int $userId, int $contentId): bool
    {
        try {
            $count = self::where([
                ['user_id', '=', $userId],
                ['content_id', '=', $contentId]
            ])->count();
            
            return $count > 0;
        } catch (\Exception $e) {
            throw new \Exception('检查点赞状态失败：' . $e->getMessage());
        }
    }

    /**
     * 添加点赞
     * @param int $userId 用户ID
     * @param int $contentId 内容ID
     * @param string $contentType 内容类型
     * @return bool
     */
    public static function addLike(int $userId, int $contentId, string $contentType): bool
    {
        try {
            // 检查是否已经点赞
            if (self::isLiked($userId, $contentId)) {
                // 如果已经点赞，直接返回true，不重复添加
                return true;
            }

            // 添加点赞记录
            $data = [
                'user_id' => $userId,
                'content_id' => $contentId,
                'content_type' => $contentType,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $result = self::create($data);

            if (!$result) {
                throw new \Exception('点赞记录保存失败');
            }

            return true;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 取消点赞
     * @param int $userId 用户ID
     * @param int $contentId 内容ID
     * @return bool
     */
    public static function removeLike(int $userId, int $contentId): bool
    {
        try {
            // 直接删除，使用更简单的方式
            $affectedRows = self::where([
                ['user_id', '=', $userId],
                ['content_id', '=', $contentId]
            ])->delete();
            
            // 不管删除了多少行，都返回true（目标是未点赞状态）
            return true;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取用户点赞列表
     * @param int $userId 用户ID
     * @param string $contentType 内容类型，为空则获取所有类型
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getUserLikes(int $userId, string $contentType = '', int $page = 1, int $limit = 10): array
    {
        $where = [['user_id', '=', $userId]];
        if (!empty($contentType)) {
            $where[] = ['content_type', '=', $contentType];
        }

        $query = self::where($where)
            ->with(['content'])
            ->order('create_time', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        return [
            'list' => $query->items(),
            'total' => $query->total(),
            'page' => $page,
            'limit' => $limit
        ];
    }
}