<?php

namespace app\admin\model\user;

use think\Model;

/**
 * Collect
 */
class Collect extends Model
{
    // 表名
    protected $name = 'user_collect';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;


    public function user(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\User::class, 'user_id', 'id');
    }

    public function content(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\Content::class, 'content_id', 'id');
    }
}