<?php

namespace app\admin\model\order;

use think\Model;

/**
 * Statuslog
 */
class Statuslog extends Model
{
    // 表名
    protected $name = 'order_status_log';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;
    protected $updateTime = false;


    public function order(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\order\Orderlist::class, 'order_id', 'id');
    }
}