<?php

namespace app\admin\model\order;

use think\Model;

/**
 * Refund
 */
class Refund extends Model
{
    // 表名
    protected $name = 'order_refund';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'process_time'    => 'timestamp:Y-m-d H:i:s',
        'completion_time' => 'timestamp:Y-m-d H:i:s',
        'refund_time'     => 'timestamp:Y-m-d H:i:s',
    ];


    public function getRefundAmountAttr($value): ?float
    {
        return is_null($value) ? null : (float)$value;
    }

    public function getEvidenceImagesAttr($value): array
    {
        if ($value === '' || $value === null) return [];
        if (!is_array($value)) {
            return explode(',', $value);
        }
        return $value;
    }

    public function setEvidenceImagesAttr($value): string
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    public function getActualRefundAmountAttr($value): ?float
    {
        return is_null($value) ? null : (float)$value;
    }

    public function order(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\order\Orderlist::class, 'order_id', 'id');
    }

    public function user(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\User::class, 'user_id', 'id');
    }
}