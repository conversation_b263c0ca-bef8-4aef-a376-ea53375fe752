<?php

namespace app\admin\model\order;

use think\Model;

/**
 * Review
 */
class Review extends Model
{
    // 表名
    protected $name = 'order_review';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'reply_time' => 'timestamp:Y-m-d H:i:s',
    ];


    public function getContentAttr($value): string
    {
        return !$value ? '' : htmlspecialchars_decode($value);
    }

    public function getImagesAttr($value): array
    {
        if ($value === '' || $value === null) return [];
        if (!is_array($value)) {
            return explode(',', $value);
        }
        return $value;
    }

    public function setImagesAttr($value): string
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    public function getReplyContentAttr($value): string
    {
        return !$value ? '' : htmlspecialchars_decode($value);
    }

    public function order(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\order\Orderlist::class, 'order_id', 'id');
    }

    public function user(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\User::class, 'user_id', 'id');
    }

    public function service(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\Content::class, 'service_id', 'id');
    }
}