<?php

namespace app\admin\model\order;

use think\Model;

/**
 * Orderlist
 */
class Orderlist extends Model
{
    // 表名
    protected $name = 'order';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'payment_time' => 'timestamp:Y-m-d H:i:s',
        'expire_time'  => 'timestamp:Y-m-d H:i:s',
        'delete_time'  => 'timestamp:Y-m-d H:i:s',
    ];


    public function getServicePriceAttr($value): ?float
    {
        return is_null($value) ? null : (float)$value;
    }

    public function getDiscountAmountAttr($value): ?float
    {
        return is_null($value) ? null : (float)$value;
    }

    public function getTotalAmountAttr($value): ?float
    {
        return is_null($value) ? null : (float)$value;
    }

    public function user(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\User::class, 'user_id', 'id');
    }

    public function service(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\Content::class, 'service_id', 'id');
    }
}