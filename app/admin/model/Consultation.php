<?php

namespace app\admin\model;

use think\Model;

/**
 * Consultation
 */
class Consultation extends Model
{
    // 表名
    protected $name = 'consultation';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;


    public function user(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\User::class, 'user_id', 'id');
    }

    public function content(): \think\model\relation\BelongsTo
    {
        return $this->belongsTo(\app\admin\model\Content::class, 'content_id', 'id');
    }
}