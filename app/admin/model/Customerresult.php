<?php

namespace app\admin\model;

use think\Model;

/**
 * Customerresult
 */
class Customerresult extends Model
{
    // 表名
    protected $name = 'customer_result';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    protected static function onAfterInsert($model): void
    {
        if (is_null($model->sort)) {
            $pk = $model->getPk();
            if (strlen($model[$pk]) >= 19) {
                $model->where($pk, $model[$pk])->update(['sort' => $model->count()]);
            } else {
                $model->where($pk, $model[$pk])->update(['sort' => $model[$pk]]);
            }
        }
    }

    public function getImpactFactorAttr($value): ?float
    {
        return is_null($value) ? null : (float)$value;
    }
}