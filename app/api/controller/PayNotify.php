<?php

namespace app\api\controller;

use Throwable;
use ba\PayLib;
use think\facade\Log;
use think\facade\Db;
use Yansongda\Pay\Pay;
use app\common\controller\Frontend;
use Psr\Http\Message\ResponseInterface;

class PayNotify extends Frontend
{
    protected array $noNeedLogin = ['wechat', 'alipay'];

    /**
     * @throws Throwable
     */
    public function initialize(): void
    {
        parent::initialize();
    }

    /**
     * 微信支付回调
     * @throws Throwable
     */
    public function wechat(): ResponseInterface
    {
        try {
            Pay::config(PayLib::getConfig());

            $result = Pay::wechat()->callback();

            Log::channel('pay_notify')->info('=== 微信支付回调开始 ===');
            Log::channel('pay_notify')->info('回调数据：' . json_encode($result->toArray(), JSON_UNESCAPED_UNICODE));
            
            // 处理回调数据
            $this->handleWechatPayment($result);
            
            Log::channel('pay_notify')->info('=== 微信支付回调处理完成 ===');
            
        } catch (Throwable $e) {
            Log::channel('pay_notify')->error('支付回调异常：' . $e->getMessage() . ' 文件：' . $e->getFile() . ' 行：' . $e->getLine());
            Log::channel('pay_notify')->error('异常堆栈：' . $e->getTraceAsString());
        }

        return Pay::wechat()->success();
    }

    /**
     * 处理微信支付回调数据
     * @param \Yansongda\Supports\Collection $result
     * @throws Throwable
     */
    private function handleWechatPayment($result): void
    {
        // 获取事件类型
        $eventType = $result->get('event_type');
        
        Log::channel('pay_notify')->info('事件类型：' . $eventType);
        
        // 只处理支付成功的通知
        if ($eventType !== 'TRANSACTION.SUCCESS') {
            Log::channel('pay_notify')->info('非支付成功通知，跳过处理');
            return;
        }

        // 获取支付数据 - 在resource.ciphertext中
        $resource = $result->get('resource', []);
        $paymentData = $resource['ciphertext'] ?? [];
        
        Log::channel('pay_notify')->info('解密后的支付数据：' . json_encode($paymentData, JSON_UNESCAPED_UNICODE));

        // 提取关键信息
        $outTradeNo = $paymentData['out_trade_no'] ?? '';
        $transactionId = $paymentData['transaction_id'] ?? '';
        $tradeState = $paymentData['trade_state'] ?? '';
        $totalAmount = $paymentData['amount']['total'] ?? 0;
        $successTime = $paymentData['success_time'] ?? '';
        $payerOpenid = $paymentData['payer']['openid'] ?? '';

        Log::channel('pay_notify')->info("关键信息 - 订单号：{$outTradeNo}, 交易号：{$transactionId}, 状态：{$tradeState}, 金额：{$totalAmount}分");

        // 验证必要参数
        if (empty($outTradeNo) || empty($transactionId) || $tradeState !== 'SUCCESS') {
            Log::channel('pay_notify')->error('参数验证失败，跳过处理');
            return;
        }

        // 查询订单 - 基于项目的ba_order表结构
        $order = Db::table('ba_order')
            ->where('order_no', $outTradeNo)
            ->where('status', 'pending')
            ->where('payment_status', 'paying')
            ->find();
            
        if (!$order) {
            Log::channel('pay_notify')->error('订单不存在或状态不正确：' . $outTradeNo);
            return;
        }

        Log::channel('pay_notify')->info("找到订单 - ID：{$order['id']}, 用户ID：{$order['user_id']}, 订单金额：{$order['total_amount']}元");

        // 验证金额 - 数据库存储的是元，微信返回的是分
        $expectedAmount = intval(bcmul($order['total_amount'], 100, 0)); // 转换为分
        if ($totalAmount != $expectedAmount) {
            Log::channel('pay_notify')->error("金额不匹配 - 期望：{$expectedAmount}分, 实际：{$totalAmount}分");
            return;
        }

        Log::channel('pay_notify')->info('订单验证通过，开始更新订单状态');

        // 更新订单状态
        Db::startTrans();
        try {
            // 更新订单主表
            Db::table('ba_order')
                ->where('id', $order['id'])
                ->update([
                    'status' => 'paid',
                    'payment_status' => 'paid',
                    'payment_method' => 'wechat',
                    'trade_no' => $transactionId,
                    'payment_time' => strtotime($successTime),
                    'payment_data' => json_encode($paymentData, JSON_UNESCAPED_UNICODE),
                    'update_time' => time()
                ]);

            Log::channel('pay_notify')->info('订单状态更新成功');

            // 记录订单状态变更日志
            Db::table('ba_order_status_log')
                ->insert([
                    'order_id' => $order['id'],
                    'order_no' => $outTradeNo,
                    'from_status' => 'pending',
                    'to_status' => 'paid',
                    'operate_type' => 'payment',
                    'operator_id' => 0,
                    'operator_name' => '微信支付回调',
                    'remark' => "微信支付成功 - 交易号：{$transactionId}，金额：" . ($totalAmount / 100) . "元",
                    'create_time' => time()
                ]);

            Log::channel('pay_notify')->info('订单状态变更日志记录成功');

            Db::commit();
            Log::channel('pay_notify')->info("✅ 微信支付回调处理成功 - 订单号：{$outTradeNo}, 交易号：{$transactionId}, 金额：" . ($totalAmount / 100) . "元");
            
        } catch (Throwable $e) {
            Db::rollback();
            Log::channel('pay_notify')->error('订单状态更新失败：' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 支付宝支付回调
     * @throws Throwable
     */
    public function alipay(): ResponseInterface
    {
        try {
            Pay::config(PayLib::getConfig());

            $result = Pay::alipay()->callback();

            Log::channel('pay_notify')->info('=== 支付宝支付回调开始 ===');
            Log::channel('pay_notify')->info('回调数据：' . json_encode($result->toArray(), JSON_UNESCAPED_UNICODE));
            
            // 处理回调数据
            $this->handleAlipayPayment($result);
            
            Log::channel('pay_notify')->info('=== 支付宝支付回调处理完成 ===');
            
        } catch (Throwable $e) {
            Log::channel('pay_notify')->error('支付回调异常：' . $e->getMessage() . ' 文件：' . $e->getFile() . ' 行：' . $e->getLine());
            Log::channel('pay_notify')->error('异常堆栈：' . $e->getTraceAsString());
        }

        return Pay::alipay()->success();
    }

    /**
     * 处理支付宝支付回调数据
     * @param \Yansongda\Supports\Collection $result
     * @throws Throwable
     */
    private function handleAlipayPayment($result): void
    {
        // 获取关键信息
        $outTradeNo = $result->get('out_trade_no', '');
        $tradeNo = $result->get('trade_no', '');
        $tradeStatus = $result->get('trade_status', '');
        $totalAmount = $result->get('total_amount', '0');
        $gmtPayment = $result->get('gmt_payment', '');
        $buyerLogonId = $result->get('buyer_logon_id', '');

        Log::channel('pay_notify')->info("关键信息 - 订单号：{$outTradeNo}, 交易号：{$tradeNo}, 状态：{$tradeStatus}, 金额：{$totalAmount}元");

        // 验证必要参数
        if (empty($outTradeNo) || empty($tradeNo) || $tradeStatus !== 'TRADE_SUCCESS') {
            Log::channel('pay_notify')->info('非支付成功通知或参数不完整，跳过处理');
            return;
        }

        // 查询订单 - 基于项目的ba_order表结构
        $order = Db::table('ba_order')
            ->where('order_no', $outTradeNo)
            ->where('status', 'pending')
            ->where('payment_status', 'paying')
            ->find();
            
        if (!$order) {
            Log::channel('pay_notify')->error('订单不存在或状态不正确：' . $outTradeNo);
            return;
        }

        Log::channel('pay_notify')->info("找到订单 - ID：{$order['id']}, 用户ID：{$order['user_id']}, 订单金额：{$order['total_amount']}元");

        // 验证金额 - 数据库和支付宝都是以元为单位
        $expectedAmount = sprintf('%.2f', floatval($order['total_amount']));
        $actualAmount = sprintf('%.2f', floatval($totalAmount));
        if ($expectedAmount !== $actualAmount) {
            Log::channel('pay_notify')->error("金额不匹配 - 期望：{$expectedAmount}元, 实际：{$actualAmount}元");
            return;
        }

        Log::channel('pay_notify')->info('订单验证通过，开始更新订单状态');

        // 更新订单状态
        Db::startTrans();
        try {
            // 解析支付时间
            $paymentTime = !empty($gmtPayment) ? strtotime($gmtPayment) : time();
            
            // 更新订单主表
            Db::table('ba_order')
                ->where('id', $order['id'])
                ->update([
                    'status' => 'paid',
                    'payment_status' => 'paid',
                    'payment_method' => 'alipay',
                    'trade_no' => $tradeNo,
                    'payment_time' => $paymentTime,
                    'payment_data' => json_encode($result->toArray(), JSON_UNESCAPED_UNICODE),
                    'update_time' => time()
                ]);

            Log::channel('pay_notify')->info('订单状态更新成功');

            // 记录订单状态变更日志
            Db::table('ba_order_status_log')
                ->insert([
                    'order_id' => $order['id'],
                    'order_no' => $outTradeNo,
                    'from_status' => 'pending',
                    'to_status' => 'paid',
                    'operate_type' => 'payment',
                    'operator_id' => 0,
                    'operator_name' => '支付宝支付回调',
                    'remark' => "支付宝支付成功 - 交易号：{$tradeNo}，金额：{$totalAmount}元，买家：{$buyerLogonId}",
                    'create_time' => time()
                ]);

            Log::channel('pay_notify')->info('订单状态变更日志记录成功');

            Db::commit();
            Log::channel('pay_notify')->info("✅ 支付宝支付回调处理成功 - 订单号：{$outTradeNo}, 交易号：{$tradeNo}, 金额：{$totalAmount}元");
            
        } catch (Throwable $e) {
            Db::rollback();
            Log::channel('pay_notify')->error('订单状态更新失败：' . $e->getMessage());
            throw $e;
        }
    }
}