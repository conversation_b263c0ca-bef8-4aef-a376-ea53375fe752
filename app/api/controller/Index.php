<?php

namespace app\api\controller;

use ba\Tree;
use Throwable;
use think\facade\Db;
use think\facade\Config;
use app\common\controller\Frontend;
use app\common\library\token\TokenExpirationException;
use app\admin\model\UserRule;
use app\admin\model\Content;
use app\admin\model\Column;
use app\admin\model\Toutiaotype;
use app\common\model\UserCollect;
use app\admin\model\user\Like as UserLike;

use app\admin\model\Frindlink;
use app\admin\model\Customerresult;
use app\admin\model\Futype;
use app\admin\model\Changjing;

class Index extends Frontend
{
    protected array $noNeedLogin = ['index', 'commonserach', 'serviceList', 'expertList', 'journalList', 'headlineList', 'contentDetail', 'getSubjectCategories', 'getSubjectOptions'];

    public function initialize(): void
    {
        parent::initialize();
    }

    /**
     * 前台和会员中心的初始化请求
     * @throws Throwable
     */
    public function index(): void
    {
        $menus = [];
        if ($this->auth->isLogin()) {
            $rules     = [];
            $userMenus = $this->auth->getMenus();

            // 首页加载的规则，验权，但过滤掉会员中心菜单
            foreach ($userMenus as $item) {
                if ($item['type'] == 'menu_dir') {
                    $menus[] = $item;
                } elseif ($item['type'] != 'menu') {
                    $rules[] = $item;
                }
            }
            $rules = array_values($rules);
        } else {
            // 若是从前台会员中心内发出的请求，要求必须登录，否则会员中心异常
            $requiredLogin = $this->request->get('requiredLogin/b', false);
            if ($requiredLogin) {

                // 触发可能的 token 过期异常
                try {
                    $token = get_auth_token(['ba', 'user', 'token']);
                    $this->auth->init($token);
                } catch (TokenExpirationException) {
                    $this->error(__('Token expiration'), [], 409);
                }

                $this->error(__('Please login first'), [
                    'type' => $this->auth::NEED_LOGIN
                ], $this->auth::LOGIN_RESPONSE_CODE);
            }

            $rules = Db::name('user_rule')
                ->where('status', 1)
                ->where('no_login_valid', 1)
                ->where('type', 'in', ['route', 'nav', 'button'])
                ->order('weigh', 'desc')
                ->select()
                ->toArray();
            $rules = Tree::instance()->assembleChild($rules);
        }
        $nav = UserRule::where('status', 1)->where('type', 'nav')->order('weigh', 'desc')->select()->toArray();
        // 不再使用分类数据
        $category = [];
        $futype = Futype::where('is_tj', 1)->order('weigh', 'desc')->select()->toArray();

        if ($futype) {
            foreach ($futype as $key => $item) {
                $category[$key]['id'] = $item['id'];
                $category[$key]['image'] = $item['ft_image'];
            }
        }
        // 分别获取不同类型的推荐内容，每种类型6条
        $recommendedServices = Content::where('status', 1)
            ->where('homepage_recommend', 1)
            ->where('content_type', 'SERVICE')
            ->field('id,title,subtitle,cover_image,service_icon,price,purchase_count,click_count,sort,create_time')
            ->order('sort', 'desc')
            ->limit(6)
            ->select()
            ->toArray();

        $recommendedExperts = Content::where('status', 1)
            ->where('homepage_recommend', 1)
            ->where('content_type', 'EXPERT')
            ->field('id,title,expert_name,expert_number,avatar_url,position,institution,research_area,academic_achievements,click_count,sort,create_time')
            ->order('sort', 'desc')
            ->limit(6)
            ->select()
            ->toArray();

        $recommendedJournals = Content::where('status', 1)
            ->where('homepage_recommend', 1)
            ->where('content_type', 'JOURNAL')
            ->field('id,title,cover_image,domain,journal_type,journal_datatype,journal_zky,journal_jcr,impact_factor,click_count,sort,create_time')
            ->order('sort', 'desc')
            ->limit(6)
            ->select()
            ->toArray();

        // 获取推荐的头条数据
        $recommendedHeadlines = Content::where('status', 1)
            ->where('homepage_recommend', 1)
            ->where('content_type', 'HEADLINE')
            ->field('id,title,subtitle,summary,cover_image,author,publish_date,like_count,collect_count,click_count,sort,create_time')
            ->order('sort', 'desc')
            ->limit(12)
            ->select()
            ->toArray();

        // 分离数据：4个带图的和8个不带图的
        $featuredHeadlines = [];
        $listHeadlines = [];

        foreach ($recommendedHeadlines as $headline) {
            // 带图的放入featured（最多4个）
            if (!empty($headline['cover_image']) && count($featuredHeadlines) < 4) {
                $featuredHeadlines[] = $headline;
            }
            // 所有的都放入list（最多8个）
            if (count($listHeadlines) < 8) {
                $listHeadlines[] = $headline;
            }
        }

        $home = get_sys_config('', 'home', true);
        $firind_link = Frindlink::order('id', 'desc')->select()->toArray();
        $customer_result = Customerresult::order('sort', 'desc')->select()->toArray();
        $this->success('', [
            'site'             => [
                'siteName'     => get_sys_config('site_name'),
                'version'      => get_sys_config('version'),
                'cdnUrl'       => full_url(),
                'upload'       => keys_to_camel_case(get_upload_config(), ['max_size', 'save_name', 'allowed_suffixes', 'allowed_mime_types']),
                'recordNumber' => get_sys_config('record_number'),
                'cdnUrlParams' => Config::get('buildadmin.cdn_url_params'),
                'banner'       => array_map(function ($item) {
                    return full_url($item);
                }, array_filter(explode(',', get_sys_config('banner')))),
                'nav'            => $nav,
                'category'       => $category,
                'SERVICE'        => $recommendedServices,
                'EXPERT'         => $recommendedExperts,
                'JOURNAL'        => $recommendedJournals,
                'HEADLINE'       => [
                    'featured' => $featuredHeadlines,
                    'list'     => $listHeadlines
                ],
                'recommended' => [
                    'services'  => $recommendedServices,
                    'experts'   => $recommendedExperts,
                    'journals'  => $recommendedJournals,

                ],
                'home'             => $home,
                'firind_link'      => $firind_link,
                'customer_result'  => $customer_result,

            ],

            'openMemberCenter' => Config::get('buildadmin.open_member_center'),
            'wx_qrcode'        => get_sys_config('wx_qrcode'),
            'kefu_qrcode'    => get_sys_config('kefu_qrcode'),
            'userInfo'         => $this->auth->getUserInfo(),
            'rules'            => $rules,
            'menus'            => $menus,
            'yhxy'            => get_sys_config('yhxy'),
            'bqsm'            => get_sys_config('bqsm'),
            'yszc'            => get_sys_config('yszc'),
            'kefu_url'        => get_sys_config('kefu_url'),
            'base_tel'        => get_sys_config('base_tel'),
            'base_emal'      => get_sys_config('base_emal'),
            'base_jobtime'    => get_sys_config('base_jobtime'),
        ]);
    }

    /**
     * 全局搜索接口
     * @throws Throwable
     */
    public function commonserach(): void
    {
        $keyword = $this->request->get('keyword', '');
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 20);
        $type = $this->request->get('type', ''); // 可选：SERVICE/EXPERT/JOURNAL/HEADLINE

        if (empty($keyword)) {
            $this->error('请输入搜索关键词');
        }

        // 构建查询条件
        $where = [
            ['status', '=', 1]
        ];

        // 如果指定了类型，添加类型筛选
        if (!empty($type)) {
            $where[] = ['content_type', '=', strtoupper($type)];
        }

        // 搜索字段：标题、摘要、内容、标签、专家名字、期刊领域等
        $searchCondition = function ($query) use ($keyword) {
            $query->whereOr([
                ['title', 'like', '%' . $keyword . '%'],
                ['summary', 'like', '%' . $keyword . '%'],
                ['content', 'like', '%' . $keyword . '%'],
                ['tags', 'like', '%' . $keyword . '%'],
                ['expert_name', 'like', '%' . $keyword . '%'],
                ['domain', 'like', '%' . $keyword . '%'],
                ['institution', 'like', '%' . $keyword . '%'],
                ['research_area', 'like', '%' . $keyword . '%'],
                ['author', 'like', '%' . $keyword . '%'],
            ]);
        };

        // 执行查询
        $result = Content::where($where)
            ->where($searchCondition)
            ->field('id,column_id,content_type,title,subtitle,summary,cover_image,link_url,click_count,like_count,collect_count,tags,author,publish_date,expert_name,position,institution,domain,impact_factor,price,purchase_count,create_time')
            ->order([
                'homepage_recommend' => 'desc', // 推荐的排前面
                'sort' => 'desc',
                'create_time' => 'desc'
            ])
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 统计各类型数量
        $typeCount = [
            'all' => 0,
            'SERVICE' => 0,
            'EXPERT' => 0,
            'JOURNAL' => 0,
            'HEADLINE' => 0
        ];

        // 统计总数
        $typeCount['all'] = Content::where([
            ['status', '=', 1]
        ])->where($searchCondition)->count();

        // 统计各类型数量
        foreach (['SERVICE', 'EXPERT', 'JOURNAL', 'HEADLINE'] as $contentType) {
            $typeCount[$contentType] = Content::where([
                ['status', '=', 1],
                ['content_type', '=', $contentType]
            ])->where($searchCondition)->count();
        }

        // 处理返回数据，为不同类型添加特定字段
        $list = $result->toArray();
        foreach ($list['data'] as &$item) {
            // 高亮搜索关键词
            $item['title'] = $this->highlightKeyword($item['title'], $keyword);
            $item['summary'] = $this->highlightKeyword($item['summary'], $keyword);

            // 为封面图片添加完整URL
            if (!empty($item['cover_image'])) {
                $item['cover_image'] = full_url($item['cover_image']);
            }

            // 格式化时间
            $item['create_time'] = date('Y-m-d', strtotime($item['create_time']));
            if (!empty($item['publish_date'])) {
                $item['publish_date'] = date('Y-m-d', strtotime($item['publish_date']));
            }
        }

        $this->success('搜索成功', [
            'keyword' => $keyword,
            'type' => $type,
            'list' => $list['data'],
            'total' => $list['total'],
            'page' => $page,
            'limit' => $limit,
            'typeCount' => $typeCount
        ]);
    }

    /**
     * 高亮搜索关键词
     * @param string|null $text
     * @param string $keyword
     * @return string
     */
    private function highlightKeyword(?string $text, string $keyword): string
    {
        if (empty($text) || empty($keyword)) {
            return $text ?: '';
        }

        return str_ireplace($keyword, '<mark>' . $keyword . '</mark>', $text);
    }

    /**
     * 学术服务列表页面接口
     * @throws Throwable
     */
    public function serviceList(): void
    {
        $keyword = $this->request->get('keyword', '');
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 12);
        $service_scenario = $this->request->get('service_scenario', ''); // 服务场景ID
        $service_type = $this->request->get('service_type', ''); // 服务类型ID

        // 获取学术服务栏目ID
        $serviceColumn = Column::where('code', 'service')->find();
        if (!$serviceColumn) {
            $this->error('学术服务栏目不存在');
        }

        // 构建查询条件
        $where = [

            ['content_type', '=', 'SERVICE'],
            ['column_id', '=', $serviceColumn->id]
        ];

        // 关键词搜索
        $searchCondition = null;
        if (!empty($keyword)) {
            $searchCondition = function ($query) use ($keyword) {
                $query->whereOr([
                    ['title', 'like', '%' . $keyword . '%'],
                    ['subtitle', 'like', '%' . $keyword . '%'],
                    ['service_desc', 'like', '%' . $keyword . '%'],
                    ['tags', 'like', '%' . $keyword . '%'],
                ]);
            };
        }

        $query = Content::where($where);
        if ($searchCondition !== null) {
            $query = $query->where($searchCondition);
        }

        // 服务场景筛选
        if (!empty($service_scenario)) {
            $query = $query->whereRaw("FIND_IN_SET(?, service_scenarios)", [$service_scenario]);
        }

        // 服务类型筛选
        if (!empty($service_type)) {
            $query = $query->whereRaw("FIND_IN_SET(?, service_types_select)", [$service_type]);
        }

        // 执行查询
        $result = $query->field('id,title,subtitle,service_desc,cover_image,service_icon,service_scenarios,service_types_select,price,purchase_count,click_count,create_time,tags')
            ->order([
                'homepage_recommend' => 'desc',
                'sort' => 'desc',
                'create_time' => 'desc'
            ])
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 处理返回数据
        $list = $result->toArray();
        foreach ($list['data'] as &$item) {
            // 高亮搜索关键词
            if (!empty($keyword)) {
                $item['title'] = $this->highlightKeyword($item['title'], $keyword);
                $item['service_desc'] = $this->highlightKeyword($item['service_desc'], $keyword);
            }

            // 为图片添加完整URL
            if (!empty($item['cover_image'])) {
                $item['cover_image'] = full_url($item['cover_image']);
            }
            if (!empty($item['service_icon'])) {
                $item['service_icon'] = full_url($item['service_icon']);
            }

            // 格式化价格
            $item['price'] = floatval($item['price']);
            $item['purchase_count'] = intval($item['purchase_count']);

            // 处理标签
            $item['tags'] = !empty($item['tags']) ? explode(',', $item['tags']) : [];

            // 格式化时间
            $item['create_time'] = date('Y-m-d', strtotime($item['create_time']));

            // 处理服务标签
            $item['service_tags'] = !empty($item['service_tags']) ? explode(',', $item['service_tags']) : [];

            // 处理服务场景名称
            $item['service_scenario_names'] = [];
            if (!empty($item['service_scenarios'])) {
                $scenarioIds = array_filter(explode(',', $item['service_scenarios']));
                if (!empty($scenarioIds)) {
                    $scenarios = Changjing::whereIn('id', $scenarioIds)->column('cj_name', 'id');
                    $item['service_scenario_names'] = array_values($scenarios);
                }
            }

            // 处理服务类型名称
            $item['service_type_names'] = [];
            if (!empty($item['service_types_select'])) {
                $typeIds = array_filter(explode(',', $item['service_types_select']));
                if (!empty($typeIds)) {
                    $types = Futype::whereIn('id', $typeIds)->column('ft_name', 'id');
                    $item['service_type_names'] = array_values($types);
                }
            }
        }

        // 获取服务分类选项（从数据库动态获取）
        $serviceOptions = $this->getServiceOptions($serviceColumn->id);

        // 统计数据
        $totalCount = Content::where([

            ['content_type', '=', 'SERVICE'],
            ['column_id', '=', $serviceColumn->id]
        ])->count();

        $this->success('获取成功', [
            'pageInfo' => [
                'title' => '专注科研服务',
                'subtitle' => '累计服务科研相关人员',
                'totalCount' => $totalCount
            ],
            'options' => $serviceOptions,
            'filters' => [
                'keyword' => $keyword,
                'service_scenario' => $service_scenario,
                'service_type' => $service_type
            ],
            'list' => $list['data'],
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $list['total'],
                'last_page' => $list['last_page']
            ]
        ]);
    }

    /**
     * 获取服务选项
     * @param int $columnId 栏目ID
     * @return array
     */
    private function getServiceOptions(int $columnId): array
    {
        // 获取服务场景选项
        $scenarios = Changjing::order('weigh', 'desc')
            ->field('id as `key`, cj_name as label')
            ->select()
            ->toArray();
        array_unshift($scenarios, ['key' => '', 'label' => '全部场景']);

        // 获取服务类型选项
        $types = Futype::order('weigh', 'desc')
            ->field('id as `key`, ft_name as label')
            ->select()
            ->toArray();
        array_unshift($types, ['key' => '', 'label' => '全部类型']);

        return [
            'scenarios' => $scenarios,
            'types' => $types,
        ];
    }

    /**
     * 专家智库列表页面接口
     * @throws Throwable
     */
    public function expertList(): void
    {
        $keyword = $this->request->get('keyword', '');
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 12);
        $research_field = $this->request->get('research_field', ''); // 研究领域

        // 获取专家智库栏目ID
        $expertColumn = Column::where('code', 'expert')->find();
        if (!$expertColumn) {
            $this->error('专家智库栏目不存在');
        }

        // 构建查询条件
        $where = [
            ['status', '=', 1],
            ['content_type', '=', 'EXPERT'],
            ['column_id', '=', $expertColumn->id]
        ];

        // 关键词搜索
        $searchCondition = null;
        if (!empty($keyword)) {
            $searchCondition = function ($query) use ($keyword) {
                $query->whereOr([
                    ['title', 'like', '%' . $keyword . '%'],
                    ['expert_name', 'like', '%' . $keyword . '%'],
                    ['position', 'like', '%' . $keyword . '%'],
                    ['institution', 'like', '%' . $keyword . '%'],
                    ['research_area', 'like', '%' . $keyword . '%'],
                    ['academic_achievements', 'like', '%' . $keyword . '%'],
                    ['expertise_skills', 'like', '%' . $keyword . '%'],
                    ['summary', 'like', '%' . $keyword . '%'],
                    ['tags', 'like', '%' . $keyword . '%'],
                ]);
            };
        }

        // 研究领域筛选
        if (!empty($research_field)) {
            $where[] = ['research_area', 'like', '%' . $research_field . '%'];
        }

        // 构建查询
        $query = Content::where($where);
        if ($searchCondition !== null) {
            $query = $query->where($searchCondition);
        }

        // 执行查询
        $result = $query->field('id,title,expert_name,expert_number,position,institution,research_area,avatar_url,academic_achievements,tutoring_experience,expertise_skills,service_types,summary,click_count,create_time,tags')
            ->order([
                'homepage_recommend' => 'desc',
                'sort' => 'desc',
                'create_time' => 'desc'
            ])
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 处理返回数据
        $list = $result->toArray();
        foreach ($list['data'] as &$item) {
            // 高亮搜索关键词
            if (!empty($keyword)) {
                $item['expert_name'] = $this->highlightKeyword($item['expert_name'], $keyword);
                $item['position'] = $this->highlightKeyword($item['position'], $keyword);
                $item['institution'] = $this->highlightKeyword($item['institution'], $keyword);
                $item['research_area'] = $this->highlightKeyword($item['research_area'], $keyword);
                $item['summary'] = $this->highlightKeyword($item['summary'], $keyword);
            }

            // 为头像添加完整URL
            if (!empty($item['avatar_url'])) {
                $item['avatar_url'] = full_url($item['avatar_url']);
            }

            // 处理研究方向标签
            $item['research_tags'] = !empty($item['research_area']) ? explode(',', $item['research_area']) : [];

            // 处理其他标签
            $item['tags'] = !empty($item['tags']) ? explode(',', $item['tags']) : [];

            // 格式化时间
            $item['create_time'] = date('Y-m-d', strtotime($item['create_time']));
        }

        // 获取专家分类选项（从数据库动态获取）
        $expertOptions = $this->getExpertOptions($expertColumn->id);

        // 统计数据
        $totalCount = Content::where([
            ['status', '=', 1],
            ['content_type', '=', 'EXPERT'],
            ['column_id', '=', $expertColumn->id]
        ])->count();

        $this->success('获取成功', [
            'pageInfo' => [
                'title' => '甄选导师5800+名',
                'subtitle' => '涵盖全学科研究领域',
                'totalCount' => $totalCount
            ],
            'options' => $expertOptions,
            'filters' => [
                'keyword' => $keyword,
                'research_field' => $research_field
            ],
            'list' => $list['data'],
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $list['total'],
                'last_page' => $list['last_page']
            ]
        ]);
    }

    /**
     * 获取专家选项
     * @param int $columnId 栏目ID
     * @return array
     */
    private function getExpertOptions(int $columnId): array
    {
        // 获取热门研究领域（从现有专家的研究方向中提取）
        $researchFields = $this->getPopularResearchFields($columnId);

        return [
            'categories' => [],                 // 不再使用分类
            'researchFields' => $researchFields // 研究领域
        ];
    }

    /**
     * 获取热门研究领域
     * @param int $columnId 栏目ID
     * @return array
     */
    private function getPopularResearchFields(int $columnId): array
    {
        // 查询所有专家的研究方向
        $experts = Content::where([
            ['status', '=', 1],
            ['content_type', '=', 'EXPERT'],
            ['column_id', '=', $columnId]
        ])
            ->where('research_area', '<>', '')
            ->field('research_area')
            ->select()
            ->toArray();

        // 统计研究领域出现频次
        $fieldCounts = [];
        foreach ($experts as $expert) {
            if (!empty($expert['research_area'])) {
                $fields = array_filter(array_map('trim', explode(',', $expert['research_area'])));
                foreach ($fields as $field) {
                    if (!empty($field)) {
                        $fieldCounts[$field] = ($fieldCounts[$field] ?? 0) + 1;
                    }
                }
            }
        }

        // 按出现频次排序，取前10个
        arsort($fieldCounts);
        $popularFields = array_slice(array_keys($fieldCounts), 0, 10);

        // 转换为选项格式
        $options = [['key' => '', 'label' => '全部研究领域']];
        foreach ($popularFields as $field) {
            $options[] = ['key' => $field, 'label' => $field];
        }

        return $options;
    }

    /**
     * 学术期刊列表页面接口
     * @throws Throwable
     */
    public function journalList(): void
    {
        $keyword = $this->request->get('keyword', '');
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 12);
        $big_type = $this->request->get('big_type', ''); // 大类学科
        $small_type = $this->request->get('small_type', ''); // 小类学科
        $journal_type = $this->request->get('journal_type', ''); // 期刊类型
        $journal_datatype = $this->request->get('journal_datatype', ''); // 数据库
        $journal_zky = $this->request->get('journal_zky', ''); // 中科院
        $journal_jcr = $this->request->get('journal_jcr', ''); // JCR
        $impact_factor = $this->request->get('impact_factor', ''); // 影响因子范围

        // 获取学术期刊栏目ID
        $journalColumn = Column::where('code', 'journal')->find();
        if (!$journalColumn) {
            $this->error('学术期刊栏目不存在');
        }

        // 构建查询条件
        $where = [
            ['status', '=', 1],
            ['content_type', '=', 'JOURNAL'],
            ['column_id', '=', $journalColumn->id]
        ];

        // 关键词搜索（包含期刊名称、学科领域等）
        $searchCondition = null;
        if (!empty($keyword)) {
            $searchCondition = function ($query) use ($keyword) {
                $query->whereOr([
                    ['title', 'like', '%' . $keyword . '%'],
                    ['domain', 'like', '%' . $keyword . '%'],
                    ['summary', 'like', '%' . $keyword . '%'],
                    ['journal_data', 'like', '%' . $keyword . '%'],
                    ['paper_requirements', 'like', '%' . $keyword . '%'],
                    ['tags', 'like', '%' . $keyword . '%'],
                ]);
            };
        }

        // 大类学科筛选
        if (!empty($big_type)) {
            $where[] = ['big_type', '=', $big_type];
        }

        // 小类学科筛选
        if (!empty($small_type)) {
            $where[] = ['small_type', '=', $small_type];
        }

        // 期刊类型筛选
        if (!empty($journal_type)) {
            $where[] = ['journal_type', '=', $journal_type];
        }

        // 数据库筛选
        if (!empty($journal_datatype)) {
            $where[] = ['journal_datatype', '=', $journal_datatype];
        }

        // 中科院筛选
        if (!empty($journal_zky)) {
            $where[] = ['journal_zky', '=', $journal_zky];
        }

        // JCR筛选
        if (!empty($journal_jcr)) {
            $where[] = ['journal_jcr', '=', $journal_jcr];
        }

        // 影响因子范围筛选
        $impactFactorCondition = null;
        if (!empty($impact_factor)) {
            if (is_array($impact_factor) && count($impact_factor) >= 2) {
                // 处理数组格式的范围筛选 [min, max]
                $minFactor = floatval($impact_factor[0]);
                $maxFactor = floatval($impact_factor[1]);
                if ($minFactor >= 0 && $maxFactor > $minFactor) {
                    $impactFactorCondition = function ($query) use ($minFactor, $maxFactor) {
                        $query->where('impact_factor', '>=', $minFactor)
                            ->where('impact_factor', '<=', $maxFactor);
                    };
                }
            } elseif (is_string($impact_factor) && strpos($impact_factor, ',') !== false) {
                // 处理逗号分隔的字符串格式 "min,max"
                $factorRange = explode(',', $impact_factor);
                if (count($factorRange) >= 2) {
                    $minFactor = floatval(trim($factorRange[0]));
                    $maxFactor = floatval(trim($factorRange[1]));
                    if ($minFactor >= 0 && $maxFactor > $minFactor) {
                        $impactFactorCondition = function ($query) use ($minFactor, $maxFactor) {
                            $query->where('impact_factor', '>=', $minFactor)
                                ->where('impact_factor', '<=', $maxFactor);
                        };
                    }
                }
            }
        }

        // 构建查询
        $query = Content::where($where);
        if ($searchCondition !== null) {
            $query = $query->where($searchCondition);
        }
        if ($impactFactorCondition !== null) {
            $query = $query->where($impactFactorCondition);
        }

        // 执行查询
        $result = $query->field('id,title,domain,cover_image,big_type,small_type,journal_type,journal_datatype,journal_zky,journal_jcr,impact_factor,journal_data,paper_requirements,submission_guidelines,summary,click_count,create_time,tags')
            ->order([
                'homepage_recommend' => 'desc',
                'sort' => 'desc',
                'create_time' => 'desc'
            ])
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 处理返回数据
        $list = $result->toArray();
        foreach ($list['data'] as &$item) {
            // 高亮搜索关键词
            if (!empty($keyword)) {
                $item['title'] = $this->highlightKeyword($item['title'], $keyword);
                $item['domain'] = $this->highlightKeyword($item['domain'], $keyword);
                $item['summary'] = $this->highlightKeyword($item['summary'], $keyword);
            }

            // 为封面图片添加完整URL
            if (!empty($item['cover_image'])) {
                $item['cover_image'] = full_url($item['cover_image']);
            }

            // 格式化影响因子
            $item['impact_factor'] = !empty($item['impact_factor']) ? floatval($item['impact_factor']) : 0;

            // 处理标签
            $item['tags'] = !empty($item['tags']) ? explode(',', $item['tags']) : [];

            // 格式化时间
            $item['create_time'] = date('Y-m-d', strtotime($item['create_time']));
        }

        // 获取期刊筛选选项（从系统配置获取）
        $journalOptions = $this->getJournalOptions();

        // 统计数据
        $totalCount = Content::where([
            ['status', '=', 1],
            ['content_type', '=', 'JOURNAL'],
            ['column_id', '=', $journalColumn->id]
        ])->count();

        $this->success('获取成功', [
            'pageInfo' => [
                'title' => '学术期刊数据库',
                'subtitle' => '精选优质学术期刊',
                'totalCount' => $totalCount
            ],
            'options' => $journalOptions,
            'filters' => [
                'keyword' => $keyword,
                'big_type' => $big_type,
                'small_type' => $small_type,
                'journal_type' => $journal_type,
                'journal_datatype' => $journal_datatype,
                'journal_zky' => $journal_zky,
                'journal_jcr' => $journal_jcr,
                'impact_factor' => $impact_factor
            ],
            'list' => $list['data'],
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $list['total'],
                'last_page' => $list['last_page']
            ]
        ]);
    }

    /**
     * 获取期刊筛选选项（从系统配置获取）
     * @return array
     */
    private function getJournalOptions(): array
    {
        $configKeys = ['journal_type', 'journal_datatype', 'journal_zky', 'journal_jcr'];
        $options = [];

        foreach ($configKeys as $key) {
            $configValue = get_sys_config($key);
            $options[$key] = [['key' => '', 'label' => '请选择']];

            if ($configValue && is_array($configValue)) {
                // 检查是否是期望的格式 [{"key": "xxx", "value": "xxx"}, ...]
                if (isset($configValue[0]) && is_array($configValue[0]) && isset($configValue[0]['key']) && isset($configValue[0]['value'])) {
                    foreach ($configValue as $item) {
                        $options[$key][] = [
                            'key' => $item['key'],
                            'label' => $item['value']
                        ];
                    }
                }
            }
        }

        // 获取学科分类数据
        $bigTypes = Db::name('subject_category')
            ->where('parent_id', 0)
            ->where('status', 1)
            ->order('sort desc, id desc')
            ->field('id as `key`, name as label')
            ->select()
            ->toArray();

        $smallTypes = Db::name('subject_category')
            ->where('parent_id', '>', 0)
            ->where('status', 1)
            ->order('sort desc, id desc')
            ->field('id as `key`, name as label, parent_id')
            ->select()
            ->toArray();

        // 添加默认选项
        array_unshift($bigTypes, ['key' => '', 'label' => '请选择']);
        array_unshift($smallTypes, ['key' => '', 'label' => '请选择']);

        $options['big_type'] = $bigTypes;
        $options['small_type'] = $smallTypes;

        return $options;
    }

    /**
     * 学术头条列表页面接口
     * @throws Throwable
     */
    public function headlineList(): void
    {
        $keyword = $this->request->get('keyword', '');
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 12);
        $tag = $this->request->get('tag', ''); // 标签筛选
        $headlineType = $this->request->get('headline_type', ''); // 头条分类筛选

        // 获取学术头条栏目ID
        $headlineColumn = Column::where('code', 'headline')->find();
        if (!$headlineColumn) {
            $this->error('学术头条栏目不存在');
        }

        // 构建查询条件
        $where = [
            ['status', '=', 1],
            ['content_type', '=', 'HEADLINE'],
            ['column_id', '=', $headlineColumn->id]
        ];

        // 关键词搜索
        $searchCondition = null;
        if (!empty($keyword)) {
            $searchCondition = function ($query) use ($keyword) {
                $query->whereOr([
                    ['title', 'like', '%' . $keyword . '%'],
                    ['subtitle', 'like', '%' . $keyword . '%'],
                    ['summary', 'like', '%' . $keyword . '%'],
                    ['author', 'like', '%' . $keyword . '%'],
                    ['tags', 'like', '%' . $keyword . '%'],
                ]);
            };
        }

        // 标签筛选
        if (!empty($tag)) {
            $where[] = ['tags', 'like', '%' . $tag . '%'];
        }

        // 头条分类筛选 - 使用FIND_IN_SET
        if (!empty($headlineType)) {
            $headlineType = intval($headlineType); // 确保是整数，防止SQL注入
            $where[] = ['', 'exp', Db::raw("FIND_IN_SET({$headlineType}, headline_types)")];
        }

        // 构建查询
        $query = Content::where($where);
        if ($searchCondition !== null) {
            $query = $query->where($searchCondition);
        }

        // 执行查询
        $result = $query->field('id,title,subtitle,summary,cover_image,author,publish_date,like_count,collect_count,click_count,tags,headline_types,create_time')
            ->order([
                'homepage_recommend' => 'desc',
                'sort' => 'desc',
                'publish_date' => 'desc',
                'create_time' => 'desc'
            ])
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 获取所有头条分类信息
        $headlineTypesData = Toutiaotype::field('id,name')->select()->toArray();
        $headlineTypesMap = array_column($headlineTypesData, 'name', 'id');

        // 处理返回数据
        $list = $result->toArray();
        foreach ($list['data'] as &$item) {
            // 高亮搜索关键词
            if (!empty($keyword)) {
                $item['title'] = $this->highlightKeyword($item['title'], $keyword);
                $item['subtitle'] = $this->highlightKeyword($item['subtitle'], $keyword);
                $item['summary'] = $this->highlightKeyword($item['summary'], $keyword);
                $item['author'] = $this->highlightKeyword($item['author'], $keyword);
            }

            // 为封面图片添加完整URL
            if (!empty($item['cover_image'])) {
                $item['cover_image'] = full_url($item['cover_image']);
            }

            // 格式化数值字段
            $item['like_count'] = intval($item['like_count']);
            $item['collect_count'] = intval($item['collect_count']);
            $item['click_count'] = intval($item['click_count']);

            // 处理标签
            $item['tags'] = !empty($item['tags']) ? explode(',', $item['tags']) : [];

            // 处理头条分类
            $item['headline_types_info'] = [];
            if (!empty($item['headline_types'])) {
                $typeIds = explode(',', $item['headline_types']);
                foreach ($typeIds as $typeId) {
                    $typeId = trim($typeId);
                    if (!empty($typeId) && isset($headlineTypesMap[$typeId])) {
                        $item['headline_types_info'][] = [
                            'id' => $typeId,
                            'name' => $headlineTypesMap[$typeId]
                        ];
                    }
                }
            }

            // 格式化时间
            $item['create_time'] = date('Y-m-d', strtotime($item['create_time']));
            if (!empty($item['publish_date'])) {
                $item['publish_date'] = date('Y-m-d', strtotime($item['publish_date']));
            }
        }

        // 获取头条分类选项（从数据库动态获取）
        $headlineOptions = $this->getHeadlineOptions($headlineColumn->id);

        // 统计数据
        $totalCount = Content::where([
            ['status', '=', 1],
            ['content_type', '=', 'HEADLINE'],
            ['column_id', '=', $headlineColumn->id]
        ])->count();

        $this->success('获取成功', [
            'pageInfo' => [
                'title' => '学术头条',
                'subtitle' => '实时学术资讯与前沿动态',
                'totalCount' => $totalCount
            ],
            'options' => $headlineOptions,
            'headlineTypes' => $headlineTypesData, // 添加头条分类数据
            'filters' => [
                'keyword' => $keyword,
                'tag' => $tag,
                'headline_type' => $headlineType
            ],
            'list' => $list['data'],
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $list['total'],
                'last_page' => $list['last_page']
            ]
        ]);
    }

    /**
     * 获取头条选项
     * @param int $columnId 栏目ID
     * @return array
     */
    private function getHeadlineOptions(int $columnId): array
    {
        // 获取热门标签（从现有头条的标签中提取）
        $popularTags = $this->getPopularHeadlineTags($columnId);

        return [
            'categories' => [],              // 不再使用分类
            'popularTags' => $popularTags    // 热门标签
        ];
    }

    /**
     * 获取热门头条标签
     * @param int $columnId 栏目ID
     * @return array
     */
    private function getPopularHeadlineTags(int $columnId): array
    {
        // 查询所有头条的标签
        $headlines = Content::where([
            ['status', '=', 1],
            ['content_type', '=', 'HEADLINE'],
            ['column_id', '=', $columnId]
        ])
            ->where('tags', '<>', '')
            ->field('tags')
            ->select()
            ->toArray();

        // 统计标签出现频次
        $tagCounts = [];
        foreach ($headlines as $headline) {
            if (!empty($headline['tags'])) {
                $tags = array_filter(array_map('trim', explode(',', $headline['tags'])));
                foreach ($tags as $tag) {
                    if (!empty($tag)) {
                        $tagCounts[$tag] = ($tagCounts[$tag] ?? 0) + 1;
                    }
                }
            }
        }

        // 按出现频次排序，取前15个
        arsort($tagCounts);
        $popularTags = array_slice(array_keys($tagCounts), 0, 15);

        // 转换为选项格式
        $options = [['key' => '', 'label' => '全部标签']];
        foreach ($popularTags as $tag) {
            $options[] = ['key' => $tag, 'label' => $tag];
        }

        return $options;
    }

    /**
     * 获取学科分类级联数据
     * @throws Throwable
     */
    public function getSubjectCategories(): void
    {
        $parent_id = $this->request->get('parent_id', 0);

        // 获取指定父级下的子分类
        $categories = Db::name('subject_category')
            ->where('parent_id', $parent_id)
            ->where('status', 1)
            ->order('sort desc, id desc')
            ->field('id, name, code, parent_id')
            ->select()
            ->toArray();

        $this->success('获取成功', [
            'list' => $categories,
            'parent_id' => $parent_id
        ]);
    }

    /**
     * 获取学科分类选项数据（用于前端下拉选择）
     * @throws Throwable
     */
    public function getSubjectOptions(): void
    {
        $parent_id = $this->request->get('parent_id', '');

        if ($parent_id === '') {
            // 获取大类学科（顶级分类）
            $categories = Db::name('subject_category')
                ->where('parent_id', 0)
                ->where('status', 1)
                ->order('sort desc, id desc')
                ->field('id, name, code')
                ->select()
                ->toArray();
        } else {
            // 获取指定大类下的小类学科
            $categories = Db::name('subject_category')
                ->where('parent_id', $parent_id)
                ->where('status', 1)
                ->order('sort desc, id desc')
                ->field('id, name, code, parent_id')
                ->select()
                ->toArray();
        }

        $this->success('获取成功', [
            'list' => $categories,
            'parent_id' => $parent_id
        ]);
    }

    /**
     * 内容详情接口
     * @throws Throwable
     */
    public function contentDetail(): void
    {
        $id = $this->request->get('id/d', 0);

        if (empty($id)) {
            $this->error('内容ID不能为空');
        }

        // 查询内容详情
        $content = Content::where([
            ['id', '=', $id],
            ['status', '=', 1]
        ])
            ->with(['column'])
            ->find();

        if (!$content) {
            $this->error('内容不存在或已被删除');
        }

        // 增加点击量
        Content::where('id', $id)->inc('click_count');

        // 转换为数组并处理基础数据
        $detail = $content->toArray();

        // 处理图片URL
        if (!empty($detail['cover_image'])) {
            $detail['cover_image'] = full_url($detail['cover_image']);
        }
        if (!empty($detail['service_icon'])) {
            $detail['service_icon'] = full_url($detail['service_icon']);
        }
        if (!empty($detail['avatar_url'])) {
            $detail['avatar_url'] = full_url($detail['avatar_url']);
        }

        // 处理标签和时间格式化
        $detail['tags'] = !empty($detail['tags']) ? explode(',', $detail['tags']) : [];
        if (!empty($detail['research_area'])) {
            $detail['research_tags'] = explode(',', $detail['research_area']);
        }
        $detail['create_time'] = date('Y-m-d H:i:s', strtotime($detail['create_time']));
        if (!empty($detail['publish_date'])) {
            $detail['publish_date'] = date('Y-m-d', strtotime($detail['publish_date']));
        }

        // 处理数值字段
        if ($detail['content_type'] === 'SERVICE') {
            $detail['price'] = floatval($detail['price']);
            $detail['purchase_count'] = intval($detail['purchase_count']);
        } elseif ($detail['content_type'] === 'JOURNAL') {
            $detail['impact_factor'] = !empty($detail['impact_factor']) ? floatval($detail['impact_factor']) : 0;
        } elseif ($detail['content_type'] === 'HEADLINE') {
            $detail['like_count'] = intval($detail['like_count']);
            $detail['collect_count'] = intval($detail['collect_count']);
        }

        // 获取关联内容
        $relatedContent = [];

        // 关联服务
        if (!empty($detail['related_services'])) {
            $serviceIds = array_filter(explode(',', $detail['related_services']));
            if (!empty($serviceIds)) {
                $relatedContent['services'] = Content::where([
                    ['id', 'in', $serviceIds],
                    ['status', '=', 1]
                ])
                    ->field('id,title,subtitle,service_desc,cover_image,service_icon,price,purchase_count')
                    ->select()
                    ->each(function ($item) {
                        if (!empty($item['cover_image'])) $item['cover_image'] = full_url($item['cover_image']);
                        if (!empty($item['service_icon'])) $item['service_icon'] = full_url($item['service_icon']);
                        $item['price'] = floatval($item['price']);
                        return $item;
                    })
                    ->toArray();
            }
        }

        // 关联专家
        if (!empty($detail['related_experts'])) {
            $expertIds = array_filter(explode(',', $detail['related_experts']));
            if (!empty($expertIds)) {
                $relatedContent['experts'] = Content::where([
                    ['id', 'in', $expertIds],
                    ['status', '=', 1]
                ])
                    ->field('id,title,expert_name,position,institution,research_area,avatar_url,summary')
                    ->select()
                    ->each(function ($item) {
                        if (!empty($item['avatar_url'])) $item['avatar_url'] = full_url($item['avatar_url']);
                        $item['research_tags'] = !empty($item['research_area']) ? explode(',', $item['research_area']) : [];
                        return $item;
                    })
                    ->toArray();
            }
        }

        // 关联期刊
        if (!empty($detail['related_journals'])) {
            $journalIds = array_filter(explode(',', $detail['related_journals']));
            if (!empty($journalIds)) {
                $relatedContent['journals'] = Content::where([
                    ['id', 'in', $journalIds],
                    ['status', '=', 1]
                ])
                    ->field('id,title,domain,cover_image,big_type,small_type,journal_type,impact_factor,summary')
                    ->select()
                    ->each(function ($item) {
                        if (!empty($item['cover_image'])) $item['cover_image'] = full_url($item['cover_image']);
                        $item['impact_factor'] = !empty($item['impact_factor']) ? floatval($item['impact_factor']) : 0;
                        return $item;
                    })
                    ->toArray();
            }
        }

        // 获取同类推荐（同栏目同类型，排除当前内容，最多6条）
        $recommended = Content::where([
            ['content_type', '=', $detail['content_type']],
            ['column_id', '=', $detail['column_id']],
            ['status', '=', 1],
            ['id', '<>', $id]
        ])
            ->order('homepage_recommend desc, sort desc, create_time desc')
            ->limit(6)
            ->select()
            ->each(function ($item) {
                if (!empty($item['cover_image'])) $item['cover_image'] = full_url($item['cover_image']);
                if (!empty($item['service_icon'])) $item['service_icon'] = full_url($item['service_icon']);
                if (!empty($item['avatar_url'])) $item['avatar_url'] = full_url($item['avatar_url']);
                return $item;
            })
            ->toArray();

        // 获取上一条内容
        $prevContent = Content::where([
            ['content_type', '=', $detail['content_type']],
            ['column_id', '=', $detail['column_id']],
            ['status', '=', 1],
            ['id', '<', $id]
        ])
            ->field('id,title,cover_image,publish_date,create_time')
            ->order('id', 'desc')
            ->find();

        // 获取下一条内容
        $nextContent = Content::where([
            ['content_type', '=', $detail['content_type']],
            ['column_id', '=', $detail['column_id']],
            ['status', '=', 1],
            ['id', '>', $id]
        ])
            ->field('id,title,cover_image,publish_date,create_time')
            ->order('id', 'asc')
            ->find();

        // 处理上一条和下一条的数据
        $prevNext = [
            'prev' => null,
            'next' => null
        ];

        if ($prevContent) {
            $prevNext['prev'] = [
                'id' => $prevContent->id,
                'title' => $prevContent->title,
                'cover_image' => $prevContent->cover_image ? full_url($prevContent->cover_image) : '',
                'publish_date' => $prevContent->publish_date ? date('Y-m-d', strtotime($prevContent->publish_date)) : date('Y-m-d', strtotime($prevContent->create_time))
            ];
        }

        if ($nextContent) {
            $prevNext['next'] = [
                'id' => $nextContent->id,
                'title' => $nextContent->title,
                'cover_image' => $nextContent->cover_image ? full_url($nextContent->cover_image) : '',
                'publish_date' => $nextContent->publish_date ? date('Y-m-d', strtotime($nextContent->publish_date)) : date('Y-m-d', strtotime($nextContent->create_time))
            ];
        }

        // 构建基本返回数据
        $responseData = [
            'detail' => $detail,
            'related' => $relatedContent,
            'recommended' => $recommended,
            'prevNext' => $prevNext,
            'breadcrumb' => [
                ['name' => '首页', 'path' => '/'],
                ['name' => $detail['column']['name'] ?? '', 'path' => ''],
                ['name' => $detail['title'], 'path' => '']
            ]
        ];

        // 如果是学术头条详情，添加侧边栏数据
        if ($detail['content_type'] === 'HEADLINE') {
            $responseData['hotTags'] = $this->getHotTags($detail['column_id']); // 热门标签
            $responseData['latestArticles'] = $this->getLatestArticles($detail['column_id']); // 最新文章
            $responseData['hotArticles'] = $this->getHotArticles($detail['column_id']); // 热点咨询
        }

        // 如果是学术服务，添加评价列表
        if ($detail['content_type'] === 'SERVICE') {
            $responseData['reviews'] = $this->getServiceReviews($id);
        }

        // 添加用户状态信息（购买、收藏、点赞）
        $responseData['detail']['has_purchased'] = false; // 默认未购买
        $responseData['detail']['is_collected'] = false;  // 默认未收藏
        $responseData['detail']['is_liked'] = false;      // 默认未点赞

        // 检查用户是否登录
        if ($this->auth->isLogin()) {
            $userId = $this->auth->id;

            // 如果是学术服务，检查购买状态
            if ($detail['content_type'] === 'SERVICE') {
                $purchaseOrder = Db::name('order')
                    ->where([
                        ['user_id', '=', $userId],
                        ['service_id', '=', $id],
                        ['payment_status', '=', 'paid'] // 已支付状态
                    ])
                    ->find();

                $responseData['detail']['has_purchased'] = !empty($purchaseOrder);
            }

            // 检查收藏状态（所有内容类型都支持收藏）
            $responseData['detail']['is_collected'] = UserCollect::isCollected($userId, $id);

            // 检查点赞状态（所有内容类型都支持点赞）
            $responseData['detail']['is_liked'] = UserLike::isLiked($userId, $id);
        }

        $this->success('获取成功', $responseData);
    }

    /**
     * 切换收藏状态（收藏/取消收藏）
     * @throws Throwable
     */
    public function toggleCollect(): void
    {
        $contentId = $this->request->post('content_id/d', 0);

        if (empty($contentId)) {
            $this->error('内容ID不能为空');
        }

        // 检查内容是否存在
        $content = Content::where('id', $contentId)
            ->where('status', 1)
            ->find();

        if (!$content) {
            $this->error('内容不存在或已被删除');
        }

        $userId = $this->auth->id;

        // 添加调试信息
        if (empty($userId)) {
            $this->error('用户ID获取失败，请重新登录');
        }

        try {
            $isCollected = UserCollect::isCollected($userId, $contentId);

            Db::startTrans();

            if ($isCollected) {
                // 取消收藏
                UserCollect::removeCollect($userId, $contentId);
                // 减少收藏数
                Content::where('id', $contentId)->dec('collect_count');
                Db::commit();
                $this->success('取消收藏成功', [
                    'is_collected' => false,
                    'collect_count' => max(0, $content->collect_count - 1)
                ]);
            } else {
                // 添加收藏
                UserCollect::addCollect($userId, $contentId, $content->content_type);
                // 增加收藏数
                Content::where('id', $contentId)->inc('collect_count');
                Db::commit();
                $this->success('收藏成功', [
                    'is_collected' => true,
                    'collect_count' => $content->collect_count + 1
                ]);
            }
        } catch (\think\exception\HttpResponseException $e) {
            // 这是框架正常的响应异常，重新抛出
            throw $e;
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('操作失败：' . $e->getMessage() . ' [文件:' . $e->getFile() . ' 行号:' . $e->getLine() . ']');
        }
    }

    /**
     * 检查内容是否已收藏
     * @throws Throwable
     */
    public function checkCollect(): void
    {
        $contentId = $this->request->get('content_id/d', 0);

        if (empty($contentId)) {
            $this->error('内容ID不能为空');
        }

        $userId = $this->auth->id;
        $isCollected = UserCollect::isCollected($userId, $contentId);

        $this->success('检查成功', [
            'is_collected' => $isCollected
        ]);
    }

    /**
     * 获取用户收藏列表
     * @throws Throwable
     */
    public function collectList(): void
    {
        $contentType = $this->request->get('content_type', '');
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);

        // 验证内容类型
        $allowedTypes = ['SERVICE', 'EXPERT', 'JOURNAL', 'HEADLINE'];
        if (!empty($contentType) && !in_array($contentType, $allowedTypes)) {
            $this->error('无效的内容类型');
        }

        $userId = $this->auth->id;
        $result = UserCollect::getUserCollects($userId, $contentType, $page, $limit);

        // 处理返回数据
        $list = [];
        foreach ($result['list'] as $item) {
            if ($item['content']) {
                $content = $item['content']->toArray();

                // 处理图片URL
                if (!empty($content['cover_image'])) {
                    $content['cover_image'] = full_url($content['cover_image']);
                }
                if (!empty($content['service_icon'])) {
                    $content['service_icon'] = full_url($content['service_icon']);
                }
                if (!empty($content['avatar_url'])) {
                    $content['avatar_url'] = full_url($content['avatar_url']);
                }

                // 处理标签
                if (!empty($content['tags'])) {
                    $content['tags'] = explode(',', $content['tags']);
                }

                // 处理时间格式
                $content['create_time'] = date('Y-m-d H:i:s', strtotime($content['create_time']));
                if (!empty($content['publish_date'])) {
                    $content['publish_date'] = date('Y-m-d', strtotime($content['publish_date']));
                }

                // 处理数值字段
                if ($content['content_type'] === 'SERVICE') {
                    $content['price'] = floatval($content['price']);
                    $content['purchase_count'] = intval($content['purchase_count']);
                } elseif ($content['content_type'] === 'JOURNAL') {
                    $content['impact_factor'] = !empty($content['impact_factor']) ? floatval($content['impact_factor']) : 0;
                }

                $list[] = [
                    'id' => $item['id'],
                    'collect_time' => date('Y-m-d H:i:s', $item['create_time']),
                    'content' => $content
                ];
            }
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $result['total'],
            'page' => $result['page'],
            'limit' => $result['limit']
        ]);
    }

    /**
     * 获取收藏统计信息
     * @throws Throwable
     */
    public function collectStatistics(): void
    {
        $userId = $this->auth->id;

        $stats = [
            'total' => UserCollect::where('user_id', $userId)->count(),
            'SERVICE' => UserCollect::where('user_id', $userId)->where('content_type', 'SERVICE')->count(),
            'EXPERT' => UserCollect::where('user_id', $userId)->where('content_type', 'EXPERT')->count(),
            'JOURNAL' => UserCollect::where('user_id', $userId)->where('content_type', 'JOURNAL')->count(),
            'HEADLINE' => UserCollect::where('user_id', $userId)->where('content_type', 'HEADLINE')->count()
        ];

        $this->success('获取成功', $stats);
    }

    /**
     * 切换点赞状态（点赞/取消点赞）
     * @throws Throwable
     */
    public function toggleLike(): void
    {
        $contentId = $this->request->post('content_id/d', 0);

        if (empty($contentId)) {
            $this->error('内容ID不能为空');
        }

        // 检查内容是否存在
        $content = Content::where('id', $contentId)
            ->where('status', 1)
            ->find();

        if (!$content) {
            $this->error('内容不存在或已被删除');
        }

        $userId = $this->auth->id;

        if (empty($userId)) {
            $this->error('用户ID获取失败，请重新登录');
        }

        try {
            $isLiked = UserLike::isLiked($userId, $contentId);

            Db::startTrans();

            if ($isLiked) {
                // 取消点赞
                UserLike::removeLike($userId, $contentId);
                // 减少点赞数
                Content::where('id', $contentId)->dec('like_count');
                Db::commit();
                $this->success('取消点赞成功', [
                    'is_liked' => false,
                    'like_count' => max(0, $content->like_count - 1)
                ]);
            } else {
                // 添加点赞
                UserLike::addLike($userId, $contentId, $content->content_type);
                // 增加点赞数
                Content::where('id', $contentId)->inc('like_count');
                Db::commit();
                $this->success('点赞成功', [
                    'is_liked' => true,
                    'like_count' => $content->like_count + 1
                ]);
            }
        } catch (\think\exception\HttpResponseException $e) {
            // 这是框架正常的响应异常，重新抛出
            throw $e;
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('操作失败：' . $e->getMessage() . ' [文件:' . $e->getFile() . ' 行号:' . $e->getLine() . ']');
        }
    }

    /**
     * 检查内容是否已点赞
     * @throws Throwable
     */
    public function checkLike(): void
    {
        $contentId = $this->request->get('content_id/d', 0);

        if (empty($contentId)) {
            $this->error('内容ID不能为空');
        }

        $userId = $this->auth->id;
        $isLiked = UserLike::isLiked($userId, $contentId);

        $this->success('检查成功', [
            'is_liked' => $isLiked
        ]);
    }

    /**
     * 获取用户点赞列表
     * @throws Throwable
     */
    public function likeList(): void
    {
        $contentType = $this->request->get('content_type', '');
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);

        // 验证内容类型
        $allowedTypes = ['SERVICE', 'EXPERT', 'JOURNAL', 'HEADLINE'];
        if (!empty($contentType) && !in_array($contentType, $allowedTypes)) {
            $this->error('无效的内容类型');
        }

        $userId = $this->auth->id;
        $result = UserLike::getUserLikes($userId, $contentType, $page, $limit);

        // 处理返回数据
        $list = [];
        foreach ($result['list'] as $item) {
            if ($item['content']) {
                $content = $item['content']->toArray();

                // 处理图片URL
                if (!empty($content['cover_image'])) {
                    $content['cover_image'] = full_url($content['cover_image']);
                }
                if (!empty($content['service_icon'])) {
                    $content['service_icon'] = full_url($content['service_icon']);
                }
                if (!empty($content['avatar_url'])) {
                    $content['avatar_url'] = full_url($content['avatar_url']);
                }

                // 处理标签
                if (!empty($content['tags'])) {
                    $content['tags'] = explode(',', $content['tags']);
                }

                // 处理时间格式
                $content['create_time'] = date('Y-m-d H:i:s', strtotime($content['create_time']));
                if (!empty($content['publish_date'])) {
                    $content['publish_date'] = date('Y-m-d', strtotime($content['publish_date']));
                }

                // 处理数值字段
                if ($content['content_type'] === 'SERVICE') {
                    $content['price'] = floatval($content['price']);
                    $content['purchase_count'] = intval($content['purchase_count']);
                } elseif ($content['content_type'] === 'JOURNAL') {
                    $content['impact_factor'] = !empty($content['impact_factor']) ? floatval($content['impact_factor']) : 0;
                }

                $list[] = [
                    'id' => $item['id'],
                    'like_time' => date('Y-m-d H:i:s', $item['create_time']),
                    'content' => $content
                ];
            }
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $result['total'],
            'page' => $result['page'],
            'limit' => $result['limit']
        ]);
    }

    /**
     * 获取点赞统计信息
     * @throws Throwable
     */
    public function likeStatistics(): void
    {
        $userId = $this->auth->id;

        $stats = [
            'total' => UserLike::where('user_id', $userId)->count(),
            'SERVICE' => UserLike::where('user_id', $userId)->where('content_type', 'SERVICE')->count(),
            'EXPERT' => UserLike::where('user_id', $userId)->where('content_type', 'EXPERT')->count(),
            'JOURNAL' => UserLike::where('user_id', $userId)->where('content_type', 'JOURNAL')->count(),
            'HEADLINE' => UserLike::where('user_id', $userId)->where('content_type', 'HEADLINE')->count()
        ];

        $this->success('获取成功', $stats);
    }

    /**
     * 获取热门标签 - 基于标签出现频率排序
     * @param int $columnId 栏目ID
     * @return array
     */
    private function getHotTags(int $columnId): array
    {
        // 获取所有学术头条的标签
        $articles = Content::where([
            ['status', '=', 1],
            ['content_type', '=', 'HEADLINE'],
            ['column_id', '=', $columnId]
        ])->where('tags', '<>', '')->field('tags')->select();

        $tagCounts = [];
        foreach ($articles as $article) {
            if (!empty($article->tags)) {
                $tags = explode(',', $article->tags);
                foreach ($tags as $tag) {
                    $tag = trim($tag);
                    if (!empty($tag)) {
                        $tagCounts[$tag] = isset($tagCounts[$tag]) ? $tagCounts[$tag] + 1 : 1;
                    }
                }
            }
        }

        // 按频率排序并取前10个
        arsort($tagCounts);
        $hotTags = [];
        $count = 0;
        foreach ($tagCounts as $tag => $frequency) {
            if ($count >= 10) break;
            $hotTags[] = [
                'name' => $tag,
                'count' => $frequency
            ];
            $count++;
        }

        return $hotTags;
    }

    /**
     * 获取最新文章 - 按发布时间排序
     * @param int $columnId 栏目ID
     * @return array
     */
    private function getLatestArticles(int $columnId): array
    {
        $articles = Content::where([
            ['status', '=', 1],
            ['content_type', '=', 'HEADLINE'],
            ['column_id', '=', $columnId]
        ])->field('id,title,publish_date,create_time')
            ->order('publish_date', 'desc')
            ->order('create_time', 'desc')
            ->limit(10)
            ->select();

        $latestArticles = [];
        foreach ($articles as $article) {
            $latestArticles[] = [
                'id' => $article->id,
                'title' => $article->title,
                'publish_date' => $article->publish_date ? date('Y-m-d', strtotime($article->publish_date)) : date('Y-m-d', strtotime($article->create_time))
            ];
        }

        return $latestArticles;
    }

    /**
     * 获取热点咨询 - 按点击率排序
     * @param int $columnId 栏目ID
     * @return array
     */
    private function getHotArticles(int $columnId): array
    {
        $articles = Content::where([
            ['status', '=', 1],
            ['content_type', '=', 'HEADLINE'],
            ['column_id', '=', $columnId]
        ])->field('id,title,click_count,publish_date,create_time')
            ->order('click_count', 'desc')
            ->order('create_time', 'desc')
            ->limit(10)
            ->select();

        $hotArticles = [];
        foreach ($articles as $article) {
            $hotArticles[] = [
                'id' => $article->id,
                'title' => $article->title,
                'click_count' => intval($article->click_count),
                'publish_date' => $article->publish_date ? date('Y-m-d', strtotime($article->publish_date)) : date('Y-m-d', strtotime($article->create_time))
            ];
        }

        return $hotArticles;
    }

    /**
     * 获取服务评价列表
     * @param int $serviceId 服务ID
     * @return array
     */
    private function getServiceReviews(int $serviceId): array
    {
        $reviews = \app\admin\model\order\Review::where('service_id', $serviceId)
            ->with(['user']) // 只关联用户信息，不关联订单
            ->where('status', 'normal') // 只显示审核通过的评价
            ->order('create_time', 'desc')
            ->limit(5) // 限制显示最新20条评价
            ->select();

        $reviewList = [];
        foreach ($reviews as $review) {
            $reviewData = [
                'id' => $review->id,
                'rating' => intval($review->rating ?? 5), // 评分
                'content' => $review->content, // 评价内容
                'images' => $review->images, // 评价图片
                'create_time' => $review->create_time,
                'reply_content' => $review->reply_content, // 商家回复
                'reply_time' => $review->reply_time, // 回复时间
                'user' => [
                    'id' => $review->user ? $review->user->id : 0,
                    'nickname' => $review->user ? $this->hideUserInfo($review->user->nickname ?: '匿名用户') : '匿名用户',
                    'avatar' => ($review->user && $review->user->avatar) ? full_url($review->user->avatar) : ''
                ]
            ];

            // 处理评价图片URL
            if (!empty($reviewData['images'])) {
                $reviewData['images'] = array_map(function ($img) {
                    return full_url($img);
                }, $reviewData['images']);
            }

            $reviewList[] = $reviewData;
        }

        return $reviewList;
    }

    /**
     * 隐藏用户信息（保护隐私）
     * @param string $info
     * @return string
     */
    private function hideUserInfo(string $info): string
    {
        if (mb_strlen($info) <= 2) {
            return $info;
        }

        $firstChar = mb_substr($info, 0, 1);
        $lastChar = mb_substr($info, -1, 1);
        $middleLength = mb_strlen($info) - 2;

        return $firstChar . str_repeat('*', min($middleLength, 3)) . $lastChar;
    }
}
