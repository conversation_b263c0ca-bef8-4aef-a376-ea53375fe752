<?php

namespace app\api\controller;

use Throwable;
use ba\Captcha;
use ba\ClickCaptcha;
use think\facade\Config;
use app\common\facade\Token;
use app\common\controller\Frontend;
use app\api\validate\User as UserValidate;
use app\common\model\UserCollect;
use app\admin\model\user\Like as UserLike;
use app\admin\model\order\Orderlist;
use think\facade\Db;
use ba\PayLib;
use Yansongda\Pay\Pay;

class User extends Frontend
{
    protected array $noNeedLogin = ['checkIn', 'logout'];
    
    /**
     * 格式化时间字段 - 处理ThinkPHP自动格式化的时间字段
     * @param mixed $time 时间值，可能是时间戳或已格式化的字符串
     * @return string|null 格式化后的时间字符串
     */
    private function formatTime($time): ?string
    {
        if (empty($time)) {
            return null;
        }
        
        if (is_numeric($time)) {
            // 如果是时间戳，格式化它
            return date('Y-m-d H:i:s', intval($time));
        }
        
        // 如果已经是字符串格式，直接返回
        return (string)$time;
    }

    public function initialize(): void
    {
        parent::initialize();
    }

    /**
     * 会员签入(登录和注册)
     * @throws Throwable
     */
    public function checkIn(): void
    {
            
        $openMemberCenter = Config::get('buildadmin.open_member_center');
        if (!$openMemberCenter) {
            $this->error(__('Member center disabled'));
        }

        // 检查登录态
        if ($this->auth->isLogin()) {
            $this->success(__('You have already logged in. There is no need to log in again~'), [
                'type' => $this->auth::LOGGED_IN
            ], $this->auth::LOGIN_RESPONSE_CODE);
        }
        
        $userLoginCaptchaSwitch = Config::get('buildadmin.user_login_captcha');

        if ($this->request->isPost()) {
            $params = $this->request->post(['tab', 'email', 'mobile', 'username', 'password', 'keep', 'captcha', 'captchaId', 'captchaInfo', 'registerType']);

            // 提前检查 tab ，然后将以 tab 值作为数据验证场景
            if (!in_array($params['tab'] ?? '', ['login', 'register', 'mobile_login'])) {
                $this->error(__('Unknown operation'));
            }
            
            $validate = new UserValidate();
            try {
                $validate->scene($params['tab'])->check($params);
            } catch (Throwable $e) {
                $this->error($e->getMessage());
            }
           
            if ($params['tab'] == 'login') {
                if ($userLoginCaptchaSwitch) {
                    $captchaObj = new ClickCaptcha();
                    if (!$captchaObj->check($params['captchaId'], $params['captchaInfo'])) {
                        $this->error(__('Captcha error'));
                    }
                }
                $res = $this->auth->login($params['username'], $params['password'], !empty($params['keep']));
            } elseif ($params['tab'] == 'mobile_login') {
                // 手机号验证码登录
                $captchaObj = new Captcha();
                if (!$captchaObj->check($params['captcha'], $params['mobile'] . 'user_login') && $params['captcha'] != 123456) {
                    $this->error(__('Please enter the correct verification code'));
                }
                $res = $this->auth->loginByMobile($params['mobile'], !empty($params['keep']));
            } elseif ($params['tab'] == 'register') {
                $captchaObj = new Captcha();
                if (!$captchaObj->check($params['captcha'], $params[$params['registerType']] . 'user_register')) {
                    $this->error(__('Please enter the correct verification code'));
                }
                $res = $this->auth->register($params['username'], $params['password'], $params['mobile'], $params['email']);
            }

            if (isset($res) && $res === true) {
                $this->success(__('Login succeeded!'), [
                    'userInfo'  => $this->auth->getUserInfo(),
                    'routePath' => '/user'
                ]);
            } else {
                $msg = $this->auth->getError();
                $msg = $msg ?: __('Check in failed, please try again or contact the website administrator~');
                $this->error($msg);
            }
        }

        $this->success('', [
            'userLoginCaptchaSwitch'  => $userLoginCaptchaSwitch,
            'accountVerificationType' => get_account_verification_type()
        ]);
    }

    public function logout(): void
    {
        if ($this->request->isPost()) {
            $refreshToken = $this->request->post('refreshToken', '');
            if ($refreshToken) Token::delete((string)$refreshToken);
            $this->auth->logout();
            $this->success();
        }
    }

    /**
     * 获取我的收藏列表
     * @throws Throwable
     */
    public function myCollects(): void
    {
        $contentType = $this->request->get('content_type', '');
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);

        // 验证内容类型
        $allowedTypes = ['', 'SERVICE', 'EXPERT', 'JOURNAL', 'HEADLINE'];
        if (!in_array($contentType, $allowedTypes)) {
            $this->error('无效的内容类型');
        }

        $userId = $this->auth->id;
        $result = UserCollect::getUserCollects($userId, $contentType, $page, $limit);

        // 处理返回数据
        $list = [];
        foreach ($result['list'] as $item) {
            if ($item['content']) {
                $content = $item['content']->toArray();
                
                // 处理图片URL
                if (!empty($content['cover_image'])) {
                    $content['cover_image'] = full_url($content['cover_image']);
                }
                if (!empty($content['service_icon'])) {
                    $content['service_icon'] = full_url($content['service_icon']);
                }
                if (!empty($content['avatar_url'])) {
                    $content['avatar_url'] = full_url($content['avatar_url']);
                }

                // 处理标签
                if (!empty($content['tags'])) {
                    $content['tags'] = explode(',', $content['tags']);
                }

                // 处理时间格式
                $content['create_time'] = date('Y-m-d H:i:s', strtotime($content['create_time']));
                if (!empty($content['publish_date'])) {
                    $content['publish_date'] = date('Y-m-d', strtotime($content['publish_date']));
                }

                // 处理数值字段
                if ($content['content_type'] === 'SERVICE') {
                    $content['price'] = floatval($content['price']);
                    $content['purchase_count'] = intval($content['purchase_count']);
                } elseif ($content['content_type'] === 'JOURNAL') {
                    $content['impact_factor'] = !empty($content['impact_factor']) ? floatval($content['impact_factor']) : 0;
                }

                // 添加收藏时间
                $list[] = [
                    'collect_id' => $item['id'],
                    'collect_time' => date('Y-m-d H:i:s', $item['create_time']),
                    'content' => $content
                ];
            }
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $result['total'],
            'page' => $result['page'],
            'limit' => $result['limit']
        ]);
    }

    /**
     * 获取我的收藏统计信息
     * @throws Throwable
     */
    public function myCollectStats(): void
    {
        $userId = $this->auth->id;
        
        $stats = [
            'total' => UserCollect::where('user_id', $userId)->count(),
            'SERVICE' => UserCollect::where('user_id', $userId)->where('content_type', 'SERVICE')->count(),
            'EXPERT' => UserCollect::where('user_id', $userId)->where('content_type', 'EXPERT')->count(),
            'JOURNAL' => UserCollect::where('user_id', $userId)->where('content_type', 'JOURNAL')->count(),
            'HEADLINE' => UserCollect::where('user_id', $userId)->where('content_type', 'HEADLINE')->count()
        ];

        $this->success('获取成功', $stats);
    }

    /**
     * 获取我的点赞列表
     * @throws Throwable
     */
    public function myLikes(): void
    {
        $contentType = $this->request->get('content_type', '');
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);

        // 验证内容类型
        $allowedTypes = ['', 'SERVICE', 'EXPERT', 'JOURNAL', 'HEADLINE'];
        if (!in_array($contentType, $allowedTypes)) {
            $this->error('无效的内容类型');
        }

        $userId = $this->auth->id;
        $result = UserLike::getUserLikes($userId, $contentType, $page, $limit);

        // 处理返回数据
        $list = [];
        foreach ($result['list'] as $item) {
            if ($item['content']) {
                $content = $item['content']->toArray();
                
                // 处理图片URL
                if (!empty($content['cover_image'])) {
                    $content['cover_image'] = full_url($content['cover_image']);
                }
                if (!empty($content['service_icon'])) {
                    $content['service_icon'] = full_url($content['service_icon']);
                }
                if (!empty($content['avatar_url'])) {
                    $content['avatar_url'] = full_url($content['avatar_url']);
                }

                // 处理标签
                if (!empty($content['tags'])) {
                    $content['tags'] = explode(',', $content['tags']);
                }

                // 处理时间格式
                $content['create_time'] = date('Y-m-d H:i:s', strtotime($content['create_time']));
                if (!empty($content['publish_date'])) {
                    $content['publish_date'] = date('Y-m-d', strtotime($content['publish_date']));
                }

                // 处理数值字段
                if ($content['content_type'] === 'SERVICE') {
                    $content['price'] = floatval($content['price']);
                    $content['purchase_count'] = intval($content['purchase_count']);
                } elseif ($content['content_type'] === 'JOURNAL') {
                    $content['impact_factor'] = !empty($content['impact_factor']) ? floatval($content['impact_factor']) : 0;
                }

                // 添加点赞时间
                $list[] = [
                    'like_id' => $item['id'],
                    'like_time' => date('Y-m-d H:i:s', $item['create_time']),
                    'content' => $content
                ];
            }
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $result['total'],
            'page' => $result['page'],
            'limit' => $result['limit']
        ]);
    }

    /**
     * 获取我的点赞统计信息
     * @throws Throwable
     */
    public function myLikeStats(): void
    {
        $userId = $this->auth->id;
        
        $stats = [
            'total' => UserLike::where('user_id', $userId)->count(),
            'SERVICE' => UserLike::where('user_id', $userId)->where('content_type', 'SERVICE')->count(),
            'EXPERT' => UserLike::where('user_id', $userId)->where('content_type', 'EXPERT')->count(),
            'JOURNAL' => UserLike::where('user_id', $userId)->where('content_type', 'JOURNAL')->count(),
            'HEADLINE' => UserLike::where('user_id', $userId)->where('content_type', 'HEADLINE')->count()
        ];

        $this->success('获取成功', $stats);
    }

    /**
     * 批量取消收藏
     * @throws Throwable
     */
    public function batchRemoveCollects(): void
    {
        $collectIds = $this->request->post('collect_ids/a', []);
        
        if (empty($collectIds)) {
            $this->error('请选择要取消收藏的内容');
        }

        $userId = $this->auth->id;
        $successCount = 0;
        
        foreach ($collectIds as $collectId) {
            $collectId = intval($collectId);
            if ($collectId > 0) {
                // 通过收藏记录ID查找并删除
                $collect = UserCollect::where('id', $collectId)
                    ->where('user_id', $userId)
                    ->find();
                
                if ($collect) {
                    $collect->delete();
                    // 减少对应内容的收藏数
                    \app\admin\model\Content::where('id', $collect->content_id)->dec('collect_count');
                    $successCount++;
                }
            }
        }
        
        $this->success("成功取消收藏{$successCount}个内容", [
            'success_count' => $successCount
        ]);
    }

    /**
     * 获取我的订单列表
     * @throws Throwable
     */
    public function myOrders(): void
    {
        $status = $this->request->get('status', '');
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);

        // 验证订单状态
        $allowedStatus = ['', 'pending', 'paid', 'in_progress', 'completed', 'cancelled', 'refunded'];
        if (!in_array($status, $allowedStatus)) {
            $this->error('无效的订单状态');
        }

        $userId = $this->auth->id;
        
        $query = Orderlist::where('user_id', $userId);
        
        // 根据状态筛选
        if (!empty($status)) {
            $query->where('status', $status);
        }
        
        // 分页查询
        $result = $query->with('service')
            ->order('create_time', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 处理返回数据，只返回截图上需要的字段
        $list = [];
        foreach ($result->items() as $order) {
            // 处理封面图片URL
            $coverImage = '';
            if (!empty($order->cover_image)) {
                $coverImage = full_url($order->cover_image);
            }
            
            // 添加状态文本
            $statusText = [
                'pending' => '待付款',
                'paid' => '已付款',
                'in_progress' => '进行中',
                'completed' => '已完成',
                'cancelled' => '已取消',
                'refunded' => '已退款'
            ];
            
            // 判断是否可以评价（已完成且未评价的订单）
            $canReview = false;
            if ($order->status === 'completed') {
                $existingReview = \think\facade\Db::table('ba_order_review')
                    ->where('order_id', $order->id)
                    ->where('user_id', $userId)
                    ->find();
                $canReview = !$existingReview;
            }
            
            // 格式化时间
            $createTime = date('Y-m-d H:i:s', is_numeric($order->create_time) ? $order->create_time : strtotime($order->create_time));
            
            $list[] = [
                'id' => $order->id,
                'order_no' => $order->order_no,
                'service_id' => $order->service_id,
                'service_title' => $order->service_title,
                'cover_image' => $coverImage,
                'total_amount' => floatval($order->total_amount),
                'status' => $order->status,
                'status_text' => $statusText[$order->status] ?? '未知状态',
                'qr_code_url' => $order->qr_code_url,
                'payment_method' => $order->payment_method,
                'create_time' => $createTime,
                'can_review' => $canReview, // 是否可以评价
            ];
        }

        $this->success('获取成功', [
            'list' => $list,
            'total' => $result->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取我的订单统计
     * @throws Throwable
     */
    public function myOrderStats(): void
    {
        $userId = $this->auth->id;
        
        $stats = [
            'total' => Orderlist::where('user_id', $userId)->count(),
            'pending' => Orderlist::where('user_id', $userId)->where('status', 'pending')->count(),
            'paid' => Orderlist::where('user_id', $userId)->where('status', 'paid')->count(),
            'in_progress' => Orderlist::where('user_id', $userId)->where('status', 'in_progress')->count(),
            'completed' => Orderlist::where('user_id', $userId)->where('status', 'completed')->count(),
            'cancelled' => Orderlist::where('user_id', $userId)->where('status', 'cancelled')->count(),
            'refunded' => Orderlist::where('user_id', $userId)->where('status', 'refunded')->count()
        ];

        $this->success('获取成功', $stats);
    }

    /**
     * 获取订单详情
     * @throws Throwable
     */
    public function orderDetail(): void
    {
        $orderId = $this->request->get('order_id/d', 0);
        
        if (empty($orderId)) {
            $this->error('订单ID不能为空');
        }

        $userId = $this->auth->id;
        
        // 查找订单
        $order = Orderlist::where('id', $orderId)
            ->where('user_id', $userId)
            ->with('service')
            ->find();
        
        if (!$order) {
            $this->error('订单不存在');
        }

        $orderData = $order->toArray();
        
        // 处理服务信息
        if ($order->service) {
            $serviceData = $order->service->toArray();
            
            // 处理图片URL
            if (!empty($serviceData['cover_image'])) {
                $serviceData['cover_image'] = full_url($serviceData['cover_image']);
            }
            if (!empty($serviceData['service_icon'])) {
                $serviceData['service_icon'] = full_url($serviceData['service_icon']);
            }
            
            $orderData['service'] = $serviceData;
        }
        
        // 处理封面图片URL
        if (!empty($orderData['cover_image'])) {
            $orderData['cover_image'] = full_url($orderData['cover_image']);
        }
        
        // 处理时间格式
        $orderData['create_time'] = date('Y-m-d H:i:s', strtotime($orderData['create_time']));
        $orderData['update_time'] = date('Y-m-d H:i:s', strtotime($orderData['update_time']));
        
        if (!empty($orderData['payment_time'])) {
            $orderData['payment_time'] = date('Y-m-d H:i:s', strtotime($orderData['payment_time']));
        }
        if (!empty($orderData['expire_time'])) {
            $orderData['expire_time'] = date('Y-m-d H:i:s', strtotime($orderData['expire_time']));
        }
        
        // 处理数值字段
        $orderData['service_price'] = floatval($orderData['service_price']);
        $orderData['discount_amount'] = floatval($orderData['discount_amount']);
        $orderData['total_amount'] = floatval($orderData['total_amount']);
        
        // 添加状态文本
        $statusText = [
            'pending' => '待付款',
            'paid' => '已付款',
            'in_progress' => '进行中',
            'completed' => '已完成',
            'cancelled' => '已取消',
            'refunded' => '已退款'
        ];
        $orderData['status_text'] = $statusText[$orderData['status']] ?? '未知状态';
        
        // 判断是否可以取消
        $orderData['can_cancel'] = $orderData['status'] === 'pending';
        
        // 判断是否可以申请售后
        $orderData['can_refund'] = in_array($orderData['status'], ['paid', 'in_progress', 'completed']);

        $this->success('获取成功', $orderData);
    }

    /**
     * 取消订单
     * @throws Throwable
     */
    public function cancelOrder(): void
    {
        $orderId = $this->request->post('order_id/d', 0);
        $reason = $this->request->post('reason', '');
        
        if (empty($orderId)) {
            $this->error('订单ID不能为空');
        }

        $userId = $this->auth->id;
        
        // 查找订单
        $order = Orderlist::where('id', $orderId)
            ->where('user_id', $userId)
            ->find();
        
        if (!$order) {
            $this->error('订单不存在');
        }

        // 只有待付款状态的订单可以取消
        if ($order->status !== 'pending') {
            $this->error('当前订单状态不允许取消');
        }

        try {
            Db::startTrans();
            
            // 更新订单状态
            $order->status = 'cancelled';
            $order->admin_remark = '用户取消：' . $reason;
            $order->save();
            
            // 记录状态变更日志
            $statusLog = new \app\admin\model\order\Statuslog();
            $statusLog->order_id = $orderId;
            $statusLog->order_no = $order->order_no;
            $statusLog->from_status = 'pending';
            $statusLog->to_status = 'cancelled';
            $statusLog->operate_type = 'user';
            $statusLog->operator_id = $userId;
            $statusLog->operator_name = $this->auth->nickname ?: $this->auth->username;
            $statusLog->remark = '用户取消订单：' . $reason;
            $statusLog->save();
            
            Db::commit();
            
            $this->success('订单已取消');
            
        } catch (\think\exception\HttpResponseException $e) {
            // 这是框架正常的响应异常，重新抛出
            throw $e;
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('取消订单失败：' . $e->getMessage());
        }
    }






    /**
     * 创建订单并发起微信支付（正式版）
     * @throws Throwable
     */
    public function createOrderAndPay(): void
    {
        $serviceId = $this->request->post('service_id/d', 0);
        $contactMobile = $this->request->post('contact_mobile', '');
        $contactEmail = $this->request->post('contact_email', '');
        $contactQq = $this->request->post('contact_qq', '');
        $contactWechat = $this->request->post('contact_wechat', '');
        $userRemark = $this->request->post('user_remark', '');
        $quantity = $this->request->post('quantity/d', 1);
        $payType = $this->request->post('pay_type', 'wechat'); // 支付类型：wechat/alipay
        $contact_name=$this->request->post('contact_name', '');
        
        // 验证必填参数
        if (empty($serviceId)) {
            $this->error('服务ID不能为空');
        }
        if (empty($contactMobile)) {
            $this->error('联系手机号不能为空');
        }
        if (!preg_match('/^1[3-9]\d{9}$/', $contactMobile)) {
            $this->error('手机号格式不正确');
        }
        if (empty($contact_name)) {
            $this->error('联系人不能为空');
        }
        if ($quantity <= 0) {
            $this->error('购买数量必须大于0');
        }
        
        // 验证支付类型
        $allowedPayTypes = ['wechat', 'alipay'];
        if (!in_array($payType, $allowedPayTypes)) {
            $this->error('不支持的支付类型');
        }

        $userId = $this->auth->id;
        
        // 查找服务
        $service = \app\admin\model\Content::where('id', $serviceId)
            ->where('content_type', 'SERVICE')
            ->where('status', 1)
            ->find();
        
        if (!$service) {
            $this->error('服务不存在或已下架');
        }
        
        // 验证服务是否有价格
        if (empty($service->price) || $service->price <= 0) {
            $this->error('该服务暂不支持在线购买');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 生成订单号
            $orderNo = 'ORD' . date('YmdHis') . mt_rand(1000, 9999);
            
            // 计算金额
            $servicePrice = floatval($service->price);
            $discountAmount = 0.00; // 暂时不支持优惠
            $totalAmount = $servicePrice * $quantity - $discountAmount;
            
            // 创建订单
            $order = new Orderlist();
            $orderData = [
                'order_no' => $orderNo,
                'user_id' => $userId,
                'service_id' => $serviceId,
                'service_title' => $service->title,
                'service_desc' => $service->service_desc ?: $service->excerpt,
                'cover_image' => $service->cover_image,
                'contact_mobile' => $contactMobile,
                'contact_email' => $contactEmail,
                'contact_qq' => $contactQq,
                'contact_wechat' => $contactWechat,
                'contact_name' => $contact_name,
                'service_price' => $servicePrice,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'payment_status' => 'unpaid',
                'user_remark' => $userRemark,
                'out_trade_no' => $orderNo,
                'expire_time' => time() + 1800, // 30分钟过期
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $order->save($orderData);
            $orderId = $order->id;
            
            // 获取支付配置
            $config = PayLib::getConfig();
            
            // 根据支付类型发起不同的支付
            $amount = intval(bcmul($totalAmount, 100)); // 转换为分
           
            try {
                if ($payType === 'wechat') {
                    // 微信支付参数
                    $payOrder = [
                        'out_trade_no' => $orderNo,
                        'description' => $service->title, // 微信支付使用description
                        'amount' => [
                            'total' => $amount,
                            'currency' => 'CNY'
                        ]
                    ];
                    
                    // 发起微信支付
                    $result = Pay::wechat($config)->scan($payOrder);
                    $qrCodeUrl = $result['code_url'];
                    $paymentMethod = 'wechat';
                    $paymentMethodText = '微信支付';
                } elseif ($payType === 'alipay') {
                    // 支付宝支付参数 - 电脑扫码支付
                    $payOrder = [
                        'out_trade_no' => $orderNo,
                        'subject' => $service->title, // 支付宝使用subject
                        'total_amount' => sprintf('%.2f', $totalAmount), // 支付宝使用total_amount，单位为元，保留两位小数
                    ];
                    
                    // 发起支付宝支付
                    $result = Pay::alipay($config)->scan($payOrder);
                    
                    // 直接获取二维码URL
                    $qrCodeUrl = $result->qr_code ?? $result['qr_code'] ?? '';
                    
                    // 调试：记录支付宝返回的完整数据
                    \think\facade\Log::info('支付宝电脑支付返回数据：' . json_encode($result->toArray(), JSON_UNESCAPED_UNICODE));
                    
                    if (empty($qrCodeUrl)) {
                        throw new \Exception('支付宝电脑支付返回数据为空');
                    }
                    
                    $paymentMethod = 'alipay';
                    $paymentMethodText = '支付宝支付';
                } else {
                    throw new \Exception('不支持的支付类型');
                }
            } catch (\Yansongda\Artful\Exception\InvalidConfigException $e) {
                throw new \Exception('支付配置错误：' . $e->getMessage() . ' [缺少配置参数]');
            } catch (\Yansongda\Pay\Exception\InvalidSignException $e) {
                throw new \Exception('支付签名验证失败：' . $e->getMessage() . ' [请检查证书配置]');
            } catch (\Yansongda\Artful\Exception\InvalidResponseException $e) {
                // 这很可能就是"解包错误"的原因
                $responseData = isset($e->response) ? json_encode($e->response) : '无响应数据';
                throw new \Exception('支付响应解析错误（解包错误）：' . $e->getMessage() . ' [响应数据：' . $responseData . ']');
            } catch (\Yansongda\Pay\Exception\DecryptException $e) {
                throw new \Exception('支付数据解密错误：' . $e->getMessage() . ' [请检查证书配置]');
            } catch (\Yansongda\Artful\Exception\InvalidParamsException $e) {
                throw new \Exception('支付参数错误：' . $e->getMessage() . ' [请检查支付参数]');
            }
            
            // 更新订单支付状态并存储二维码链接
            $order->payment_status = 'paying';
            $order->payment_method = $paymentMethod;
            $order->qr_code_url = $qrCodeUrl; // 存储二维码链接，用于二次支付
            $order->save();
            
            // 记录订单状态变更
            $statusLog = new \app\admin\model\order\Statuslog();
            $statusLog->order_id = $orderId;
            $statusLog->order_no = $orderNo;
            $statusLog->from_status = null;
            $statusLog->to_status = 'pending';
            $statusLog->operate_type = 'user';
            $statusLog->operator_id = $userId;
            $statusLog->operator_name = $this->auth->nickname ?: $this->auth->username;
            $statusLog->remark = '用户创建订单并发起' . $paymentMethodText;
            $statusLog->save();
            
            // 提交事务
            Db::commit();
           
            // 处理过期时间
           // 检查是否为时间戳
            if (is_numeric($order->expire_time)) {
                $expireTime = date('Y-m-d H:i:s', $order->expire_time);
            } else {
                $expireTime = $order->expire_time; // 已经是日期格式
            }
            
            // 获取支付模式信息
            $config = PayLib::getConfig();
            $payMode = $config['mode'] ?? 0;
            $modeText = $payMode == 1 ? '沙箱模式' : ($payMode == 2 ? '服务商模式' : '正常模式');
            
            $this->success('订单创建成功，' . $paymentMethodText . '调用成功', [
                'order_id' => $orderId,
                'order_no' => $orderNo,
                'total_amount' => $totalAmount,
                'qr_code' => $qrCodeUrl,
                'payment_type' => $payType,
                'payment_method' => $paymentMethodText,
                'expire_time' => $expireTime,
                'payment_mode' => $modeText,
                'note' => $payType === 'alipay' ? '请访问支付页面完成支付' : '请使用' . $paymentMethodText . '扫描二维码完成支付'
            ]);
            
        } catch (\think\exception\HttpResponseException $e) {
            // 框架响应异常，重新抛出
            throw $e;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error('创建订单失败：' . $e->getMessage());
        }
    }

    /**
     * 创建订单并发起WAP支付（移动端版）
     * @throws Throwable
     */
    public function createOrderAndPayWap(): void
    {
        $serviceId = $this->request->post('service_id/d', 0);
        $contactMobile = $this->request->post('contact_mobile', '');
        $contactEmail = $this->request->post('contact_email', '');
        $contactQq = $this->request->post('contact_qq', '');
        $contactWechat = $this->request->post('contact_wechat', '');
        $userRemark = $this->request->post('user_remark', '');
        $quantity = $this->request->post('quantity/d', 1);
        $payType = $this->request->post('pay_type', 'wechat'); // 支付类型：wechat/alipay
        $contact_name = $this->request->post('contact_name', '');
        $returnUrl = $this->request->post('return_url', ''); // WAP支付成功后的返回地址
        
        // 验证必填参数
        if (empty($serviceId)) {
            $this->error('服务ID不能为空');
        }
        if (empty($contactMobile)) {
            $this->error('联系手机号不能为空');
        }
        if (!preg_match('/^1[3-9]\d{9}$/', $contactMobile)) {
            $this->error('手机号格式不正确');
        }
        if (empty($contact_name)) {
            $this->error('联系人不能为空');
        }
        if ($quantity <= 0) {
            $this->error('购买数量必须大于0');
        }
        
        // 验证支付类型
        $allowedPayTypes = ['wechat', 'alipay'];
        if (!in_array($payType, $allowedPayTypes)) {
            $this->error('不支持的支付类型');
        }

        $userId = $this->auth->id;
        
        // 查找服务
        $service = \app\admin\model\Content::where('id', $serviceId)
            ->where('content_type', 'SERVICE')
            ->where('status', 1)
            ->find();
        
        if (!$service) {
            $this->error('服务不存在或已下架');
        }
        
        // 验证服务是否有价格
        if (empty($service->price) || $service->price <= 0) {
            $this->error('该服务暂不支持在线购买');
        }
        
        // 开启事务
        Db::startTrans();
        try {
            // 生成订单号
            $orderNo = 'ORD' . date('YmdHis') . mt_rand(1000, 9999);
            
            // 计算金额
            $servicePrice = floatval($service->price);
            $discountAmount = 0.00; // 暂时不支持优惠
            $totalAmount = $servicePrice * $quantity - $discountAmount;
            
            // 创建订单
            $order = new Orderlist();
            $orderData = [
                'order_no' => $orderNo,
                'user_id' => $userId,
                'service_id' => $serviceId,
                'service_title' => $service->title,
                'service_desc' => $service->service_desc ?: $service->excerpt,
                'cover_image' => $service->cover_image,
                'contact_mobile' => $contactMobile,
                'contact_email' => $contactEmail,
                'contact_qq' => $contactQq,
                'contact_wechat' => $contactWechat,
                'contact_name' => $contact_name,
                'service_price' => $servicePrice,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'payment_status' => 'unpaid',
                'user_remark' => $userRemark,
                'out_trade_no' => $orderNo,
                'expire_time' => time() + 1800, // 30分钟过期
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $order->save($orderData);
            $orderId = $order->id;
            
            // 获取支付配置
            $config = PayLib::getConfig();
            
            // 根据支付类型发起不同的支付
            $amount = intval(bcmul($totalAmount, 100)); // 转换为分
           
            try {
                if ($payType === 'wechat') {
                    // 微信H5支付参数 - 使用v3版本格式（与PC版本保持一致）
                    $payOrder = [
                        'out_trade_no' => $orderNo,
                        'description' => $service->title, // v3版本使用description
                        'amount' => [
                            'total' => $amount,
                        ],
                        'scene_info' => [
                            'payer_client_ip' => $this->request->ip(),
                            'h5_info' => [
                                'type' => 'Wap'
                            ]
                        ]
                    ];
                    
                    // 尝试使用h5方法而不是wap方法
                    try {
                        $result = Pay::wechat($config)->h5($payOrder);
                        $payUrl = $result['h5_url'] ?? $result->h5_url ?? '';
                    } catch (\Exception $h5Exception) {
                        // 如果h5方法失败，记录错误并尝试其他方法
                        \think\facade\Log::error('微信H5支付失败，尝试其他方法：' . $h5Exception->getMessage());
                        throw $h5Exception;
                    }
                    
                    $paymentMethod = 'wechat';
                    $paymentMethodText = '微信支付';
                } elseif ($payType === 'alipay') {
                    // 支付宝WAP支付参数
                    $payOrder = [
                        'out_trade_no' => $orderNo,
                        'subject' => $service->title, // 支付宝使用subject
                        'total_amount' => sprintf('%.2f', $totalAmount), // 支付宝使用total_amount，单位为元，保留两位小数
                        'quit_url' => $returnUrl ?: $this->request->domain(), // 取消支付返回的链接
                    ];
                    
                    // 如果有返回地址，添加return_url
                    if (!empty($returnUrl)) {
                        $payOrder['return_url'] = $returnUrl;
                    }
                    
                    // 发起支付宝H5支付 - 按照v3文档使用h5方法
                    $result = Pay::alipay($config)->h5($payOrder);
                    
                    // 调试：记录支付宝H5支付返回的完整数据
                    \think\facade\Log::info('支付宝H5支付返回数据类型：' . gettype($result));
                    \think\facade\Log::info('支付宝H5支付返回数据内容：' . json_encode($result, JSON_UNESCAPED_UNICODE));
                    
                    // 支付宝H5支付返回Response对象处理
                    $payUrl = '';
                    
                    if (is_string($result)) {
                        // 直接是HTML字符串
                        $payUrl = $result;
                    } elseif (is_object($result)) {
                        // 尝试不同的方法获取内容
                        $className = get_class($result);
                        \think\facade\Log::info('支付宝H5支付返回对象类名：' . $className);
                        
                        // 尝试获取内容的不同方法
                        if (method_exists($result, 'getContent')) {
                            /** @var \Symfony\Component\HttpFoundation\Response $result */
                            $payUrl = $result->getContent();
                        } elseif (method_exists($result, 'getBody')) {
                            $body = $result->getBody();
                            $payUrl = (string)$body;
                        } elseif (method_exists($result, '__toString')) {
                            $payUrl = (string)$result;
                        } else {
                            // 最后尝试序列化看看内容
                            $payUrl = serialize($result);
                        }
                    } else {
                        // 其他类型直接转字符串
                        $payUrl = (string)$result;
                    }
                    
                    // 调试：记录处理后的支付URL
                    \think\facade\Log::info('处理后的支付URL：' . substr($payUrl, 0, 200) . '...');
                    
                    if (empty($payUrl)) {
                        throw new \Exception('支付宝WAP支付返回数据为空');
                    }
                    
                    $paymentMethod = 'alipay';
                    $paymentMethodText = '支付宝支付';
                } else {
                    throw new \Exception('不支持的支付类型');
                }
            } catch (\Yansongda\Artful\Exception\InvalidConfigException $e) {
                throw new \Exception('支付配置错误：' . $e->getMessage() . ' [缺少配置参数]');
            } catch (\Yansongda\Pay\Exception\InvalidSignException $e) {
                throw new \Exception('支付签名验证失败：' . $e->getMessage() . ' [请检查证书配置]');
            } catch (\Yansongda\Artful\Exception\InvalidResponseException $e) {
                // 这很可能就是"解包错误"的原因
                $responseData = isset($e->response) ? json_encode($e->response) : '无响应数据';
                throw new \Exception('支付响应解析错误（解包错误）：' . $e->getMessage() . ' [响应数据：' . $responseData . ']');
            } catch (\Yansongda\Pay\Exception\DecryptException $e) {
                throw new \Exception('支付数据解密错误：' . $e->getMessage() . ' [请检查证书配置]');
            } catch (\Yansongda\Artful\Exception\InvalidParamsException $e) {
                throw new \Exception('支付参数错误：' . $e->getMessage() . ' [请检查支付参数]');
            }
            
            // 更新订单支付状态并存储支付链接
            $order->payment_status = 'paying';
            $order->payment_method = $paymentMethod;
            $order->qr_code_url = $payUrl; // 存储支付链接，复用这个字段
            $order->save();
            
            // 记录订单状态变更
            $statusLog = new \app\admin\model\order\Statuslog();
            $statusLog->order_id = $orderId;
            $statusLog->order_no = $orderNo;
            $statusLog->from_status = null;
            $statusLog->to_status = 'pending';
            $statusLog->operate_type = 'user';
            $statusLog->operator_id = $userId;
            $statusLog->operator_name = $this->auth->nickname ?: $this->auth->username;
            $statusLog->remark = '用户创建订单并发起' . $paymentMethodText . 'H5支付';
            $statusLog->save();
            
            // 提交事务
            Db::commit();
           
            // 处理过期时间
            if (is_numeric($order->expire_time)) {
                $expireTime = date('Y-m-d H:i:s', $order->expire_time);
            } else {
                $expireTime = $order->expire_time; // 已经是日期格式
            }
            
            // 获取支付模式信息
            $config = PayLib::getConfig();
            $payMode = $config['mode'] ?? 0;
            $modeText = $payMode == 1 ? '沙箱模式' : ($payMode == 2 ? '服务商模式' : '正常模式');
            
            $this->success('订单创建成功，' . $paymentMethodText . 'H5支付调用成功', [
                'order_id' => $orderId,
                'order_no' => $orderNo,
                'total_amount' => $totalAmount,
                'pay_url' => $payUrl, // H5支付返回支付链接
                'payment_type' => $payType,
                'payment_method' => $paymentMethodText,
                'expire_time' => $expireTime,
                'payment_mode' => $modeText,
                'is_h5' => true, // 标识这是H5支付
                'note' => $payType === 'wechat' ? '请在微信浏览器中打开支付链接完成支付' : '请点击支付链接跳转到支付宝完成支付'
            ]);
            
        } catch (\think\exception\HttpResponseException $e) {
            // 框架响应异常，重新抛出
            throw $e;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error('创建订单失败：' . $e->getMessage());
        }
    }

    /**
     * 提交订单评价
     * @throws Throwable
     */
    public function submitReview(): void
    {
        $orderId = $this->request->post('order_id/d', 0);
        $rating = $this->request->post('rating/d', 5);
        $content = $this->request->post('content', '');
        $images = $this->request->post('images', []);
        
        if (empty($orderId)) {
            $this->error('订单ID不能为空');
        }
        
        if ($rating < 1 || $rating > 5) {
            $this->error('评分必须在1-5星之间');
        }
        
        if (empty($content)) {
            $this->error('评价内容不能为空');
        }

        $userId = $this->auth->id;
        
        // 查找订单
        $order = Orderlist::where('id', $orderId)
            ->where('user_id', $userId)
            ->find();
        
        if (!$order) {
            $this->error('订单不存在');
        }

        // 验证订单状态（只有已完成的订单才能评价）
        if ($order->status !== 'completed') {
            $this->error('只有已完成的订单才能进行评价');
        }
        
        // 检查是否已经评价过
        $existingReview = \think\facade\Db::table('ba_order_review')
            ->where('order_id', $orderId)
            ->where('user_id', $userId)
            ->find();
            
        if ($existingReview) {
            $this->error('该订单已经评价过了');
        }

        try {
            \think\facade\Db::startTrans();
            
            // 创建评价记录
            $reviewData = [
                'order_id' => $orderId,
                'user_id' => $userId,
                'service_id' => $order->service_id,
                'rating' => $rating,
                'content' => $content,
                'images' => is_array($images) ? json_encode($images, JSON_UNESCAPED_UNICODE) : $images,
                'status' => 'normal',
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $reviewId = \think\facade\Db::table('ba_order_review')->insertGetId($reviewData);
            
            if (!$reviewId) {
                throw new \Exception('评价创建失败');
            }
            
            // 记录订单状态变更日志
            $statusLog = new \app\admin\model\order\Statuslog();
            $statusLog->order_id = $orderId;
            $statusLog->order_no = $order->order_no;
            $statusLog->from_status = 'completed';
            $statusLog->to_status = 'completed';
            $statusLog->operate_type = 'user';
            $statusLog->operator_id = $userId;
            $statusLog->operator_name = $this->auth->nickname ?: $this->auth->username;
            $statusLog->remark = '用户提交了订单评价，评分：' . $rating . '星';
            $statusLog->save();
            
            \think\facade\Db::commit();
            
            $this->success('评价提交成功', [
                'review_id' => $reviewId,
                'order_id' => $orderId,
                'rating' => $rating,
                'content' => $content,
                'images' => $images,
                'create_time' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\think\exception\HttpResponseException $e) {
            // 这是框架正常的响应异常，重新抛出
            throw $e;
        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            
            // 记录详细的异常信息到日志
            \think\facade\Log::error('评价提交异常：' . $e->getMessage() . ' 文件：' . $e->getFile() . ' 行：' . $e->getLine());
            \think\facade\Log::error('异常堆栈：' . $e->getTraceAsString());
            
            // 返回详细的错误信息
            $errorMsg = '评价提交失败';
            if (!empty($e->getMessage())) {
                $errorMsg .= '：' . $e->getMessage();
            } else {
                $errorMsg .= '：未知错误，请查看系统日志';
            }
            
            $this->error($errorMsg);
        }
    }

    /**
     * 上传评价图片
     * @throws Throwable
     */
    public function uploadReviewImage(): void
    {
        $file = $this->request->file('image');
        
        if (!$file) {
            $this->error('请选择要上传的图片');
        }
        
        try {
            // 验证文件
            if (!$file->isValid()) {
                $this->error('上传文件无效');
            }
            
            // 检查文件大小（5MB）
            if ($file->getSize() > 5242880) {
                $this->error('图片文件过大，最大支持5MB');
            }
            
            // 检查文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            $extension = strtolower($file->extension());
            
            if (!in_array($extension, $allowedTypes)) {
                $this->error('不支持的图片格式，请上传：' . implode('、', $allowedTypes));
            }
            
            // 生成文件名
            $filename = date('Ymd') . '/' . uniqid() . '.' . $extension;
            $uploadPath = public_path() . 'uploads/review/' . $filename;
            
            // 确保目录存在
            $dir = dirname($uploadPath);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            
            // 移动文件
            if (!$file->move($uploadPath)) {
                $this->error('图片保存失败');
            }
            
            $imageUrl = '/uploads/review/' . $filename;
            
            $this->success('图片上传成功', [
                'url' => $imageUrl,
                'full_url' => full_url($imageUrl)
            ]);
            
        } catch (\Exception $e) {
            $this->error('图片上传失败：' . $e->getMessage());
        }
    }

    /**
     * 获取我的评价列表
     * @throws Throwable
     */
    public function myReviews(): void
    {
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);
        
        $userId = $this->auth->id;
        
        // 查询用户的评价列表
        $reviews = \think\facade\Db::table('ba_order_review')
            ->alias('r')
            ->leftJoin('ba_order o', 'r.order_id = o.id')
            ->where('r.user_id', $userId)
            ->field('r.*, o.service_title, o.cover_image, o.total_amount')
            ->order('r.create_time', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);
        
        // 处理返回数据
        $list = [];
        foreach ($reviews->items() as $review) {
            // 处理封面图片URL
            $coverImage = '';
            if (!empty($review['cover_image'])) {
                $coverImage = full_url($review['cover_image']);
            }
            
            // 处理评价图片
            $images = [];
            if (!empty($review['images'])) {
                $imagesData = json_decode($review['images'], true);
                if (is_array($imagesData)) {
                    foreach ($imagesData as $image) {
                        $images[] = full_url($image);
                    }
                }
            }
            
            $list[] = [
                'id' => $review['id'],
                'order_id' => $review['order_id'],
                'service_id' => $review['service_id'],
                'service_title' => $review['service_title'],
                'cover_image' => $coverImage,
                'total_amount' => floatval($review['total_amount']),
                'rating' => intval($review['rating']),
                'content' => $review['content'],
                'images' => $images,
                'reply_content' => $review['reply_content'],
                'reply_time' => $review['reply_time'] ? date('Y-m-d H:i:s', $review['reply_time']) : '',
                'create_time' => date('Y-m-d H:i:s', $review['create_time']),
                'status' => $review['status']
            ];
        }
        
        $this->success('获取成功', [
            'list' => $list,
            'total' => $reviews->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 查询订单支付状态
     * @throws Throwable
     */
    public function checkPayStatus(): void
    {
        $orderId = $this->request->get('order_id/d', 0);
        
        if (empty($orderId)) {
            $this->error('订单ID不能为空');
        }

        $userId = $this->auth->id;
        
        // 查找订单
        $order = Orderlist::where('id', $orderId)
            ->where('user_id', $userId)
            ->find();
        
        if (!$order) {
            $this->error('订单不存在');
        }

        // 检查订单是否已过期
        $currentTime = time();
        $expireTime = is_numeric($order->expire_time) ? intval($order->expire_time) : strtotime($order->expire_time);
        
        if ($expireTime && $currentTime > $expireTime) {
            // 订单已过期，自动取消
            if ($order->status === 'pending') {
                Db::startTrans();
                try {
                    $order->status = 'cancelled';
                    $order->payment_status = 'cancelled';
                    $order->admin_remark = '订单已过期自动取消';
                    $order->save();
                    
                    // 记录状态变更
                    $statusLog = new \app\admin\model\order\Statuslog();
                    $statusLog->order_id = $orderId;
                    $statusLog->order_no = $order->order_no;
                    $statusLog->from_status = 'pending';
                    $statusLog->to_status = 'cancelled';
                    $statusLog->operate_type = 'system';
                    $statusLog->operator_id = 0;
                    $statusLog->operator_name = '系统自动';
                    $statusLog->remark = '订单已过期自动取消';
                    $statusLog->save();
                    
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                }
            }
            
            $this->success('订单已过期', [
                'order_id' => $orderId,
                'order_no' => $order->order_no,
                'payment_status' => 'cancelled',
                'status' => 'cancelled',
                'payment_time' => null,
                'expired' => true,
                'expire_time' => date('Y-m-d H:i:s', $expireTime)
            ]);
            return;
        }

        // 如果订单已支付，直接返回
        if ($order->payment_status === 'paid') {
            $this->success('支付成功', [
                'order_id' => $orderId,
                'order_no' => $order->order_no,
                'payment_status' => 'paid',
                'status' => $order->status,
                'payment_time' => $order->payment_time ? date('Y-m-d H:i:s', intval($order->payment_time)) : null,
                'expired' => false
            ]);
            return;
        }

        // 主动查询第三方支付平台状态
        try {
            $config = PayLib::getConfig();
            $paymentMethod = $order->payment_method;
            $isPaid = false;
            $tradeNo = '';
            $paymentMethodText = '';
            $queryResult = null;
            
            if ($paymentMethod === 'wechat') {
                // 微信支付查询 - 使用v3版本的query方法
                $queryResult = Pay::wechat($config)->query(['out_trade_no' => $order->order_no]);
                $isPaid = isset($queryResult['trade_state']) && $queryResult['trade_state'] === 'SUCCESS';
                $tradeNo = $queryResult['transaction_id'] ?? '';
                $paymentMethodText = '微信支付';
            } elseif ($paymentMethod === 'alipay') {
                // 支付宝支付查询 - 使用v3版本的query方法
                $queryResult = Pay::alipay($config)->query(['out_trade_no' => $order->order_no]);
                $isPaid = isset($queryResult['trade_status']) && $queryResult['trade_status'] === 'TRADE_SUCCESS';
                $tradeNo = $queryResult['trade_no'] ?? '';
                $paymentMethodText = '支付宝支付';
            } else {
                throw new \Exception('不支持的支付方式：' . $paymentMethod);
            }
            
            // 记录查询日志
            \think\facade\Log::channel('pay_notify')->info('支付状态查询', [
                'order_no' => $order->order_no,
                'payment_method' => $paymentMethod,
                'query_result' => $queryResult,
                'is_paid' => $isPaid
            ]);
            
            // 根据查询结果更新订单状态
            if ($isPaid) {
                Db::startTrans();
                
                // 更新订单状态
                $order->status = 'paid';
                $order->payment_status = 'paid';
                $order->payment_time = time();
                $order->trade_no = $tradeNo;
                $order->payment_data = json_encode($queryResult);
                $order->save();
                
                // 记录状态变更
                $statusLog = new \app\admin\model\order\Statuslog();
                $statusLog->order_id = $orderId;
                $statusLog->order_no = $order->order_no;
                $statusLog->from_status = 'pending';
                $statusLog->to_status = 'paid';
                $statusLog->operate_type = 'payment';
                $statusLog->operator_id = $userId;
                $statusLog->operator_name = $paymentMethodText . '回调';
                $statusLog->remark = '支付成功（主动查询）';
                $statusLog->save();
                
                Db::commit();
                
                $this->success('支付成功', [
                    'order_id' => $orderId,
                    'order_no' => $order->order_no,
                    'payment_status' => 'paid',
                    'status' => 'paid',
                    'payment_method' => $paymentMethodText,
                    'payment_time' => date('Y-m-d H:i:s', intval($order->payment_time)),
                    'expired' => false
                ]);
            } else {
                // 未支付，返回当前状态和剩余时间
                $remainingTime = $expireTime - $currentTime;
                $this->success('支付中，请继续完成支付', [
                    'order_id' => $orderId,
                    'order_no' => $order->order_no,
                    'payment_status' => $order->payment_status,
                    'status' => $order->status,
                    'payment_method' => $paymentMethodText,
                    'payment_time' => null,
                    'expired' => false,
                    'remaining_seconds' => max(0, $remainingTime),
                    'expire_time' => date('Y-m-d H:i:s', $expireTime)
                ]);
            }
            
        } catch (\think\exception\HttpResponseException $e) {
            // 这是框架正常的响应异常，重新抛出
            throw $e;
        } catch (\Exception $e) {
            // 记录查询失败日志
            \think\facade\Log::channel('pay_notify')->error('支付状态查询失败', [
                'order_no' => $order->order_no,
                'payment_method' => $order->payment_method,
                'error' => $e->getMessage()
            ]);
            
            $paymentMethodText = $order->payment_method === 'wechat' ? '微信支付' : ($order->payment_method === 'alipay' ? '支付宝支付' : '未知支付方式');
            $remainingTime = $expireTime - $currentTime;
            
            $this->success('支付查询失败，请手动刷新', [
                'order_id' => $orderId,
                'order_no' => $order->order_no,
                'payment_status' => $order->payment_status,
                'status' => $order->status,
                'payment_method' => $paymentMethodText,
                'payment_time' => null,
                'expired' => false,
                'remaining_seconds' => max(0, $remainingTime),
                'expire_time' => date('Y-m-d H:i:s', $expireTime),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 提交退款申请
     * @throws Throwable
     */
    public function submitRefund(): void
    {
        $orderId = $this->request->post('order_id/d', 0);
        $refundType = $this->request->post('refund_type', '');
        $refundAmount = $this->request->post('refund_amount', '');
        $reason = $this->request->post('reason', '');
        $description = $this->request->post('description', '');
        $evidenceImages = $this->request->post('evidence_images', []);
        
        if (empty($orderId)) {
            $this->error('订单ID不能为空');
        }
        
        // 验证退款类型
        $allowedRefundTypes = ['refund_only', 'return_refund', 'exchange'];
        if (!in_array($refundType, $allowedRefundTypes)) {
            $this->error('请选择正确的退款类型');
        }
        
        if (empty($reason)) {
            $this->error('申请原因不能为空');
        }
        
        if (empty($refundAmount) || $refundAmount <= 0) {
            $this->error('退款金额不能为空且必须大于0');
        }

        $userId = $this->auth->id;
        
        // 查找订单
        $order = Orderlist::where('id', $orderId)
            ->where('user_id', $userId)
            ->find();
        
        if (!$order) {
            $this->error('订单不存在');
        }

        // 验证订单状态（只有已付款、进行中、已完成的订单可以申请退款）
        $allowedStatus = ['paid', 'in_progress', 'completed'];
        if (!in_array($order->status, $allowedStatus)) {
            $this->error('当前订单状态不允许申请退款');
        }
        
        // 验证退款金额不能超过订单金额
        if (floatval($refundAmount) > floatval($order->total_amount)) {
            $this->error('退款金额不能超过订单金额');
        }
        
        // 检查是否已经提交过待处理的退款申请
        $existingRefund = \think\facade\Db::table('ba_order_refund')
            ->where('order_id', $orderId)
            ->whereIn('status', ['pending', 'approved', 'processing'])
            ->find();
            
        if ($existingRefund) {
            $this->error('该订单已有待处理的退款申请');
        }

        try {
            \think\facade\Db::startTrans();
            
            // 生成退款申请编号
            $refundNo = 'RF' . date('YmdHis') . mt_rand(1000, 9999);
            
            // 创建退款申请记录
            $refundData = [
                'order_id' => $orderId,
                'order_no' => $order->order_no,
                'user_id' => $userId,
                'refund_no' => $refundNo,
                'refund_type' => $refundType,
                'refund_amount' => $refundAmount,
                'reason' => $reason,
                'description' => $description,
                'evidence_images' => is_array($evidenceImages) ? json_encode($evidenceImages, JSON_UNESCAPED_UNICODE) : $evidenceImages,
                'status' => 'pending', // 待处理
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $refundId = \think\facade\Db::table('ba_order_refund')->insertGetId($refundData);
            
            if (!$refundId) {
                throw new \Exception('退款申请创建失败');
            }
            
            // 记录订单状态变更日志
            $statusLog = new \app\admin\model\order\Statuslog();
            $statusLog->order_id = $orderId;
            $statusLog->order_no = $order->order_no;
            $statusLog->from_status = $order->status;
            $statusLog->to_status = $order->status; // 订单状态暂不改变
            $statusLog->operate_type = 'user';
            $statusLog->operator_id = $userId;
            $statusLog->operator_name = $this->auth->nickname ?: $this->auth->username;
            
            $refundTypeText = [
                'refund_only' => '仅退款',
                'return_refund' => '退货退款', 
                'exchange' => '换货'
            ];
            $statusLog->remark = '用户提交' . $refundTypeText[$refundType] . '申请 - 申请金额：' . $refundAmount . '元，原因：' . $reason;
            $statusLog->save();
            
            \think\facade\Db::commit();
            
            $this->success('退款申请提交成功，请等待审核', [
                'refund_id' => $refundId,
                'refund_no' => $refundNo,
                'order_id' => $orderId,
                'refund_type' => $refundType,
                'refund_amount' => floatval($refundAmount),
                'status' => 'pending',
                'create_time' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\think\exception\HttpResponseException $e) {
            // 这是框架正常的响应异常，重新抛出
            throw $e;
        } catch (\Exception $e) {
            \think\facade\Db::rollback();
            
            // 记录详细的异常信息到日志
            \think\facade\Log::error('退款申请提交异常：' . $e->getMessage() . ' 文件：' . $e->getFile() . ' 行：' . $e->getLine());
            \think\facade\Log::error('异常堆栈：' . $e->getTraceAsString());
            
            $this->error('退款申请提交失败：' . $e->getMessage());
        }
    }

    /**
     * 获取我的退款申请列表
     * @throws Throwable
     */
    public function myRefunds(): void
    {
        $status = $this->request->get('status', '');
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);

        // 验证状态
        $allowedStatus = ['', 'pending', 'approved', 'rejected', 'processing', 'completed', 'cancelled'];
        if (!in_array($status, $allowedStatus)) {
            $this->error('无效的退款状态');
        }

        $userId = $this->auth->id;
        
        // 构建查询条件
        $where = [['r.user_id', '=', $userId]];
        if (!empty($status)) {
            $where[] = ['r.status', '=', $status];
        }
        
        // 查询退款申请列表
        $refunds = \think\facade\Db::table('ba_order_refund')
            ->alias('r')
            ->leftJoin('ba_order o', 'r.order_id = o.id')
            ->where($where)
            ->field('r.*, o.service_title, o.total_amount as order_amount, o.cover_image')
            ->order('r.create_time', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);
        
        // 处理返回数据
        $list = [];
        foreach ($refunds->items() as $refund) {
            // 处理封面图片URL
            $coverImage = '';
            if (!empty($refund['cover_image'])) {
                $coverImage = full_url($refund['cover_image']);
            }
            
            // 处理退款申请凭证图片
            $evidenceImages = [];
            if (!empty($refund['evidence_images'])) {
                $imagesData = json_decode($refund['evidence_images'], true);
                if (is_array($imagesData)) {
                    foreach ($imagesData as $image) {
                        $evidenceImages[] = full_url($image);
                    }
                }
            }
            
            // 状态文本
            $statusText = [
                'pending' => '待处理',
                'approved' => '已同意',
                'rejected' => '已拒绝',
                'processing' => '处理中',
                'completed' => '已完成',
                'cancelled' => '已取消'
            ];
            
            // 退款类型文本
            $refundTypeText = [
                'refund_only' => '仅退款',
                'return_refund' => '退货退款',
                'exchange' => '换货'
            ];
            
            $list[] = [
                'id' => $refund['id'],
                'refund_no' => $refund['refund_no'],
                'order_id' => $refund['order_id'],
                'order_no' => $refund['order_no'],
                'service_title' => $refund['service_title'],
                'cover_image' => $coverImage,
                'order_amount' => floatval($refund['order_amount']),
                'refund_type' => $refund['refund_type'],
                'refund_type_text' => $refundTypeText[$refund['refund_type']] ?? '未知类型',
                'refund_amount' => floatval($refund['refund_amount']),
                'actual_refund_amount' => floatval($refund['actual_refund_amount']),
                'reason' => $refund['reason'],
                'description' => $refund['description'],
                'evidence_images' => $evidenceImages,
                'status' => $refund['status'],
                'status_text' => $statusText[$refund['status']] ?? '未知状态',
                'admin_remark' => $refund['admin_remark'],
                'process_time' => $refund['process_time'] ? date('Y-m-d H:i:s', $refund['process_time']) : '',
                'completion_time' => $refund['completion_time'] ? date('Y-m-d H:i:s', $refund['completion_time']) : '',
                'refund_time' => $refund['refund_time'] ? date('Y-m-d H:i:s', $refund['refund_time']) : '',
                'create_time' => date('Y-m-d H:i:s', $refund['create_time'])
            ];
        }
        
        $this->success('获取成功', [
            'list' => $list,
            'total' => $refunds->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 获取退款申请详情
     * @throws Throwable
     */
    public function refundDetail(): void
    {
        $refundId = $this->request->get('refund_id/d', 0);
        
        if (empty($refundId)) {
            $this->error('退款申请ID不能为空');
        }

        $userId = $this->auth->id;
        
        // 查询退款申请详情
        $refund = \think\facade\Db::table('ba_order_refund')
            ->alias('r')
            ->leftJoin('ba_order o', 'r.order_id = o.id')
            ->where('r.id', $refundId)
            ->where('r.user_id', $userId)
            ->field('r.*, o.service_title, o.total_amount as order_amount, o.cover_image, o.payment_method')
            ->find();
        
        if (!$refund) {
            $this->error('退款申请不存在');
        }
        
        // 处理封面图片URL
        if (!empty($refund['cover_image'])) {
            $refund['cover_image'] = full_url($refund['cover_image']);
        }
        
        // 处理退款申请凭证图片
        $evidenceImages = [];
        if (!empty($refund['evidence_images'])) {
            $imagesData = json_decode($refund['evidence_images'], true);
            if (is_array($imagesData)) {
                foreach ($imagesData as $image) {
                    $evidenceImages[] = full_url($image);
                }
            }
        }
        $refund['evidence_images'] = $evidenceImages;
        
        // 处理数值字段
        $refund['order_amount'] = floatval($refund['order_amount']);
        $refund['refund_amount'] = floatval($refund['refund_amount']);
        $refund['actual_refund_amount'] = floatval($refund['actual_refund_amount']);
        
        // 处理时间字段
        $refund['process_time'] = $refund['process_time'] ? date('Y-m-d H:i:s', $refund['process_time']) : '';
        $refund['completion_time'] = $refund['completion_time'] ? date('Y-m-d H:i:s', $refund['completion_time']) : '';
        $refund['refund_time'] = $refund['refund_time'] ? date('Y-m-d H:i:s', $refund['refund_time']) : '';
        $refund['create_time'] = date('Y-m-d H:i:s', $refund['create_time']);
        $refund['update_time'] = date('Y-m-d H:i:s', $refund['update_time']);
        
        // 状态文本
        $statusText = [
            'pending' => '待处理',
            'approved' => '已同意',
            'rejected' => '已拒绝',
            'processing' => '处理中',
            'completed' => '已完成',
            'cancelled' => '已取消'
        ];
        $refund['status_text'] = $statusText[$refund['status']] ?? '未知状态';
        
        // 退款类型文本
        $refundTypeText = [
            'refund_only' => '仅退款',
            'return_refund' => '退货退款',
            'exchange' => '换货'
        ];
        $refund['refund_type_text'] = $refundTypeText[$refund['refund_type']] ?? '未知类型';
        
        $this->success('获取成功', $refund);
    }

    /**
     * 上传退款凭证图片
     * @throws Throwable
     */
    public function uploadRefundImage(): void
    {
        $file = $this->request->file('image');
        
        if (!$file) {
            $this->error('请选择要上传的图片');
        }
        
        try {
            // 验证文件
            if (!$file->isValid()) {
                $this->error('上传文件无效');
            }
            
            // 检查文件大小（5MB）
            if ($file->getSize() > 5242880) {
                $this->error('图片文件过大，最大支持5MB');
            }
            
            // 检查文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            $extension = strtolower($file->extension());
            
            if (!in_array($extension, $allowedTypes)) {
                $this->error('不支持的图片格式，请上传：' . implode('、', $allowedTypes));
            }
            
            // 生成文件名
            $filename = date('Ymd') . '/' . uniqid() . '.' . $extension;
            $uploadPath = public_path() . 'uploads/refund/' . $filename;
            
            // 确保目录存在
            $dir = dirname($uploadPath);
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            
            // 移动文件
            if (!$file->move($uploadPath)) {
                $this->error('图片保存失败');
            }
            
            $imageUrl = '/uploads/refund/' . $filename;
            
            $this->success('图片上传成功', [
                'url' => $imageUrl,
                'full_url' => full_url($imageUrl)
            ]);
            
        } catch (\Exception $e) {
            $this->error('图片上传失败：' . $e->getMessage());
        }
    }


}