<?php

namespace app\common\model;

use think\Model;

/**
 * 用户收藏模型
 */
class UserCollect extends Model
{
    // 表名
    protected $name = 'user_collect';

    // 数据库连接
    protected $connection = 'mysql';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 时间字段
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo(\app\common\model\User::class, 'user_id', 'id');
    }

    /**
     * 关联内容模型
     */
    public function content()
    {
        return $this->belongsTo(\app\admin\model\Content::class, 'content_id', 'id');
    }

    /**
     * 检查用户是否收藏了指定内容
     * @param int $userId 用户ID
     * @param int $contentId 内容ID
     * @return bool
     */
    public static function isCollected(int $userId, int $contentId): bool
    {
        try {
            $count = self::where([
                ['user_id', '=', $userId],
                ['content_id', '=', $contentId]
            ])->count();
            
            return $count > 0;
        } catch (\Exception $e) {
            throw new \Exception('检查收藏状态失败：' . $e->getMessage());
        }
    }

    /**
     * 添加收藏
     * @param int $userId 用户ID
     * @param int $contentId 内容ID
     * @param string $contentType 内容类型
     * @return bool
     */
    public static function addCollect(int $userId, int $contentId, string $contentType): bool
    {
        try {
            // 检查是否已经收藏
            if (self::isCollected($userId, $contentId)) {
                // 如果已经收藏，直接返回true，不重复添加
                return true;
            }

            // 添加收藏记录
            $data = [
                'user_id' => $userId,
                'content_id' => $contentId,
                'content_type' => $contentType,
                'create_time' => time(),
                'update_time' => time()
            ];
            
            $result = self::create($data);

            if (!$result) {
                throw new \Exception('收藏记录保存失败');
            }

            return true;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 取消收藏
     * @param int $userId 用户ID
     * @param int $contentId 内容ID
     * @return bool
     */
    public static function removeCollect(int $userId, int $contentId): bool
    {
        try {
            // 直接删除，使用更简单的方式
            $affectedRows = self::where([
                ['user_id', '=', $userId],
                ['content_id', '=', $contentId]
            ])->delete();
            
            // 不管删除了多少行，都返回true（目标是未收藏状态）
            return true;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取用户收藏列表
     * @param int $userId 用户ID
     * @param string $contentType 内容类型，为空则获取所有类型
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getUserCollects(int $userId, string $contentType = '', int $page = 1, int $limit = 10): array
    {
        $where = [['user_id', '=', $userId]];
        if (!empty($contentType)) {
            $where[] = ['content_type', '=', $contentType];
        }

        $query = self::where($where)
            ->with(['content'])
            ->order('create_time', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        return [
            'list' => $query->items(),
            'total' => $query->total(),
            'page' => $page,
            'limit' => $limit
        ];
    }
} 