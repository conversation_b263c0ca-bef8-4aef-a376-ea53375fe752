{"name": "wonderful-code/buildadmin", "description": "Build your admin framework", "type": "project", "keywords": ["<PERSON><PERSON>min", "thinkphp"], "homepage": "https://uni.buildadmin.com", "license": "Apache-2.0", "authors": [{"name": "妙码生花", "email": "<EMAIL>"}], "require": {"php": ">=8.0.2", "topthink/framework": "8.1.1", "topthink/think-orm": "3.0.33", "topthink/think-multi-app": "1.1.1", "topthink/think-throttle": "2.0.2", "topthink/think-migration": "3.1.1", "symfony/http-foundation": "^5.4|^6.4|^7.1", "phpmailer/phpmailer": "^6.8", "guzzlehttp/guzzle": "^7.8.1", "voku/anti-xss": "^4.1", "nelexa/zip": "^4.0.0", "ext-bcmath": "*", "ext-iconv": "*", "ext-json": "*", "ext-gd": "*", "yurunsoft/yurun-oauth-login": "~3.0", "overtrue/easy-sms": "^2.4.2", "yansongda/pay": "~3.7.0", "hyperf/pimple": "~2.2.0"}, "require-dev": {"symfony/var-dumper": "^5.4", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app", "modules\\": "modules"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}