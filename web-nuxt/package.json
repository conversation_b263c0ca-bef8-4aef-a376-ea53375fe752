{"name": "build-admin-nuxt", "version": "2.3.2", "private": true, "license": "Apache-2.0", "type": "module", "scripts": {"build": "nuxt build --dotenv .env.production --log-level silent", "dev": "nuxt dev --dotenv .env.development --log-level silent", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint-fix": "eslint --fix .", "format": "npx prettier --write ."}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@element-plus/nuxt": "1.1.1", "@pinia/nuxt": "0.9.0", "@unocss/nuxt": "0.65.3", "@vueuse/nuxt": "12.2.0", "@wangeditor-next/editor": "5.6.4", "@wangeditor-next/editor-for-vue": "5.1.14", "echarts": "5.6.0", "element-plus": "2.9.1", "font-awesome": "4.7.0", "glob": "^11.0.3", "lodash-es": "4.17.21", "nprogress": "0.2.0", "nuxt": "3.15.0", "nuxt-icons": "3.2.1", "pinia": "2.3.0", "pinia-plugin-persistedstate": "4.2.0", "sortablejs": "1.15.6", "vue": "3.5.13", "vue-i18n": "11.0.1"}, "devDependencies": {"@eslint/js": "9.17.0", "@nuxt/devtools": "1.7.0", "@types/node": "22.10.2", "@types/nprogress": "0.2.3", "@types/sortablejs": "1.15.8", "eslint": "9.17.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-vue": "9.32.0", "prettier": "3.4.2", "rollup": "4.29.1", "sass": "1.83.0", "typescript": "5.7.2", "typescript-eslint": "8.18.2", "vite": "6.0.6"}, "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "esbuild", "vue-demi"]}}