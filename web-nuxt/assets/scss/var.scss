@use 'sass:map';
@use 'mixins' as *;

// --ba-background
$bg-color: () !default;
$bg-color: map.merge(
    (
        '': #f5f5f5,
        'overlay': #ffffff,
    ),
    $bg-color
);

// --ba-border-color
$border-color: () !default;
$border-color: map.merge(
    (
        '': #f6f6f6,
    ),
    $border-color
);

:root {
    @include set-component-css-var('bg-color', $bg-color);
    @include set-component-css-var('border-color', $border-color);
}
