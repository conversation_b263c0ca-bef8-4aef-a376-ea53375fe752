<template>
    <div class="login-footer-buried-point">
        <div v-if="!isEmpty(state.oauthNames)" class="third-party">
            <div class="third-party-title">第三方账户登录:</div>
            <el-tooltip v-if="state.oauthNames.includes('qq')" effect="dark" content="腾讯QQ登录" placement="top">
                <img @click="onLogin('qq')" class="third-party-logo" src="~assets/images/qq.png" alt="" />
            </el-tooltip>
            <el-tooltip v-if="state.oauthNames.includes('wechat_scan') && !isMobile()" effect="dark" content="微信扫码登录" placement="top">
                <img @click="onLogin('wechat_scan')" class="third-party-logo" src="~assets/images/wechat_scan.png" alt="" />
            </el-tooltip>
            <el-tooltip v-if="state.oauthNames.includes('wechat_mp') && isWeixinBrowser()" effect="dark" content="微信登录" placement="top">
                <img @click="onLogin('wechat_mp')" class="third-party-logo" src="~assets/images/wechat_mp.png" alt="" />
            </el-tooltip>
        </div>
    </div>
</template>

<script setup lang="ts">
import { isEmpty } from 'lodash-es'
import { oauthList } from '~/api/user/oauth'
import { isWeixinBrowser, onLogin } from '~/utils/modules/oauth'

const state: {
    oauthNames: string[]
} = reactive({
    oauthNames: [],
})

const { data } = await oauthList()
if (data.value?.code == 1) {
    state.oauthNames = data.value?.data.oauthNames
}
</script>

<style scoped lang="scss">
.third-party {
    display: flex;
    align-items: center;
    .third-party-title {
        text-align: center;
        color: var(--el-text-color-secondary);
        font-size: var(--el-font-size-small);
        padding-left: 2px;
    }
    .third-party-logo {
        height: 42px;
        width: 42px;
        padding: 5px;
        opacity: 0.8;
        cursor: pointer;
        &:hover {
            opacity: 1;
        }
    }
}
</style>
