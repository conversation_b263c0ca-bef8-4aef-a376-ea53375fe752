<template>
    <div>
        <el-form-item label="绑定信息">
            <div class="third-party">
                <el-tooltip v-if="state.sources['qq']" effect="dark" content="腾讯QQ账户绑定" placement="top">
                    <img
                        @click="onBind('qq')"
                        :class="state.sources['qq'] === 'Unbound' ? 'grey' : ''"
                        class="third-party-logo"
                        src="~assets/images/qq.png"
                        alt=""
                    />
                </el-tooltip>
                <el-tooltip v-if="state.sources['wechat_scan']" effect="dark" content="微信扫码登录绑定" placement="top">
                    <img
                        @click="onBind('wechat_scan')"
                        :class="state.sources['wechat_scan'] === 'Unbound' ? 'grey' : ''"
                        class="third-party-logo"
                        src="~assets/images/wechat_scan.png"
                        alt=""
                    />
                </el-tooltip>
                <el-tooltip v-if="state.sources['wechat_mp']" effect="dark" content="微信内登录绑定" placement="top">
                    <img
                        @click="onBind('wechat_mp')"
                        :class="state.sources['wechat_mp'] === 'Unbound' ? 'grey' : ''"
                        class="third-party-logo"
                        src="~assets/images/wechat_mp.png"
                        alt=""
                    />
                </el-tooltip>
            </div>
            <client-only>
                <el-dialog
                    title="输入账户密码以解除绑定"
                    v-model="state.showUnbind"
                    class="ba-unbind-dialog"
                    :destroy-on-close="true"
                    :close-on-click-modal="false"
                    width="30%"
                >
                    <el-form
                        :model="state.unbindForm"
                        :rules="state.unbindRules"
                        :label-position="'top'"
                        ref="unbindFormRef"
                        @keyup.enter.stop="onSubmitUnbind(unbindFormRef)"
                        @submit.prevent=""
                    >
                        <el-form-item prop="password">
                            <el-input
                                ref="passwordRef"
                                type="password"
                                clearable
                                v-model="state.unbindForm.password"
                                :show-password="true"
                                placeholder="请输入账户密码"
                            >
                            </el-input>
                        </el-form-item>
                    </el-form>
                    <template #footer>
                        <div :style="'width: calc(100% - 20px)'">
                            <el-button @click="state.showUnbind = false">{{ $t('Cancel') }}</el-button>
                            <el-button v-blur :loading="state.unbindLoading" @click="onSubmitUnbind(unbindFormRef)" type="primary"> 解绑 </el-button>
                        </div>
                    </template>
                </el-dialog>
            </client-only>
        </el-form-item>
    </div>
</template>

<script setup lang="ts">
import { bindList, unbind } from '~/api/user/oauth'
import { isWeixinBrowser, onLogin } from '~/utils/modules/oauth'
import { ElNotification, FormInstance, ElInput } from 'element-plus'

const passwordRef = ref<InstanceType<typeof ElInput>>()
const unbindFormRef = ref<FormInstance>()
const state: {
    sources: anyObj
    showUnbind: boolean
    unbindForm: anyObj
    unbindRules: anyObj
    unbindLoading: boolean
    sourceTempName: string
} = reactive({
    sources: {},
    showUnbind: false,
    unbindForm: {
        password: '',
    },
    unbindRules: {
        password: [buildValidatorData({ name: 'required', title: '账户密码' }), buildValidatorData({ name: 'password' })],
    },
    unbindLoading: false,
    sourceTempName: '',
})

const { data } = await bindList()
if (data.value?.code == 1) {
    state.sources = data.value.data.sources
}

const onBind = (source: string) => {
    if (state.sources[source] == 'Unbound') {
        if (source == 'wechat_mp' && !isWeixinBrowser()) {
            ElNotification({
                type: 'error',
                message: '请在微信内置浏览器打开！',
            })
            return false
        }
        onLogin(source, 'bind')
    } else {
        // 解绑
        state.showUnbind = true
        state.sourceTempName = source
        setTimeout(() => {
            passwordRef.value!.focus()
        }, 200)
    }
}

const onSubmitUnbind = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate((res) => {
        if (res) {
            unbind({ ...state.unbindForm, name: state.sourceTempName })
                .then((res) => {
                    state.showUnbind = false
                    state.sources[res.data.name] = 'Unbound'
                })
                .catch(() => {})
        }
    })
}
</script>

<style scoped lang="scss">
.third-party {
    display: flex;
    align-items: center;
    .grey {
        filter: grayscale(100%);
    }
    .third-party-logo {
        height: 36px;
        width: 36px;
        padding: 5px;
        opacity: 0.8;
        margin-right: 8px;
        border: 1px solid var(--el-border-color);
        border-radius: var(--el-border-radius-base);
        cursor: pointer;
        &:hover {
            opacity: 1;
        }
    }
}
:deep(.ba-unbind-dialog) .el-dialog__body {
    padding-bottom: 10px;
}
@media screen and (max-width: 1024px) {
    :deep(.ba-unbind-dialog) {
        --el-dialog-width: 50% !important;
    }
}
@media screen and (max-width: 768px) {
    :deep(.ba-unbind-dialog) {
        --el-dialog-width: 70% !important;
    }
}
@media screen and (max-width: 600px) {
    :deep(.ba-unbind-dialog) {
        --el-dialog-width: 92% !important;
    }
}
</style>
