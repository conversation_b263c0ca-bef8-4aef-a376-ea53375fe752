export const isWeixinBrowser = () => {
    const event = useRequestEvent()
    const userAgent = import.meta.client ? navigator.userAgent : event?.node.req.headers['user-agent']
    if (!userAgent) return false
    const ua = userAgent.toLowerCase()
    return /micromessenger/.test(ua) ? true : false
}

export const onLogin = (name: string, type = 'login') => {
    const userInfo = useUserInfo()
    const runtimeConfig = useRuntimeConfig()
    const url = runtimeConfig.public.apiBaseUrl + `/api/OAuthLogin/index?server=1&name=${name}&type=${type}&token=${userInfo.getToken()}`

    if (type == 'bind' && !isMobile()) {
        window.open(url, '_blank', `height=600,width=760,top=20,left=20,toolbar=no,menubar=no,scrollbars=no,resizable=no,location=no,status=no`)

        useEventListener(window, 'message', (event) => {
            if (event.data == 'bind-done') {
                window.location.reload()
            }
        })
    } else {
        window.location.href = url
    }
}
