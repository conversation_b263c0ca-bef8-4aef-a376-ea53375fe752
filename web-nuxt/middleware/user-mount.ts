import { loginAgent } from '~/api/user/oauth'

export default defineNuxtRouteMiddleware(async (to) => {
    const userInfo = useUserInfo()
    const requestAgentType = useState('requestAgentType', () => '')
    if (!requestAgentType.value && to.query.code && to.query.state) {
        // node 服务端，发起请求完成绑定
        const res = await loginAgent(to.query.code as string, to.query.state as string)
        if (res.data.value?.code == 1) {
            requestAgentType.value = res.data.value.data.type
            userInfo.dataFill(res.data.value.data.userInfo, false)
        }
    } else if (requestAgentType.value == 'bind' && import.meta.client) {
        // 进入到客户端，若存在父级窗口，直接关闭当前窗口
        if (window.opener) {
            window.opener.postMessage('bind-done', '*')
            window.close()
        }
    }
})
