<template>
    <div class="user-views">
        <el-card
            class="user-views-card"
            shadow="hover"
            :header="state.allowSetPassword ? t('user.account.changePassword.Set Password') : t('user.account.changePassword.Change Password')"
        >
            <el-alert
                v-if="state.allowSetPassword"
                class="set-password-alert"
                :title="t('user.account.changePassword.You do not have a password, please set it first')"
                type="info"
            />
            <div class="change-password">
                <el-form :model="state.form" :rules="state.rules" label-position="top" ref="formRef" @keyup.enter="onSubmit(formRef)">
                    <FormItem
                        v-if="!state.allowSetPassword"
                        :label="t('user.account.changePassword.Old password')"
                        type="password"
                        v-model="state.form.oldPassword"
                        prop="oldPassword"
                        :input-attr="{ showPassword: true }"
                        :placeholder="t('user.account.changePassword.Please enter your current password')"
                    />
                    <FormItem
                        :label="t('user.account.changePassword.New password')"
                        type="password"
                        v-model="state.form.newPassword"
                        prop="newPassword"
                        :input-attr="{ showPassword: true }"
                        :placeholder="t('Please input field', { field: t('user.account.changePassword.New password') })"
                    />
                    <FormItem
                        :label="t('user.account.changePassword.Confirm new password')"
                        type="password"
                        v-model="state.form.confirmPassword"
                        prop="confirmPassword"
                        :input-attr="{ showPassword: true }"
                        :placeholder="t('Please input field', { field: t('user.account.changePassword.Confirm new password') })"
                    />
                    <el-form-item class="submit-buttons">
                        <el-button @click="onResetForm(formRef)">{{ $t('Reset') }}</el-button>
                        <el-button type="primary" :loading="state.formSubmitLoading" @click="onSubmit(formRef)">{{ $t('Save') }}</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { FormInstance } from 'element-plus'
import { getChangePassword, postChangePassword } from '~/api/user/oauth'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

definePageMeta({
    name: 'account/changePassword',
})
useSeoMeta({
    title: '修改密码',
})

const router = useRouter()
const userInfo = useUserInfo()
const formRef = ref<FormInstance>()
const state = reactive({
    allowSetPassword: false,
    formSubmitLoading: false,
    form: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
    },
    rules: {
        oldPassword: [buildValidatorData({ name: 'required', title: t('user.account.changePassword.Old password') })],
        newPassword: [
            buildValidatorData({ name: 'required', title: t('user.account.changePassword.New password') }),
            buildValidatorData({ name: 'password' }),
        ],
        confirmPassword: [
            buildValidatorData({ name: 'required', title: t('user.account.changePassword.Confirm new password') }),
            buildValidatorData({ name: 'password' }),
            {
                validator: (rule: any, val: string, callback: Function) => {
                    if (state.form.newPassword || state.form.confirmPassword) {
                        if (state.form.newPassword == state.form.confirmPassword) {
                            callback()
                        } else {
                            callback(new Error(t('user.account.changePassword.The duplicate password does not match the new password')))
                        }
                    }
                    callback()
                },
                trigger: 'blur',
            },
        ],
    },
})

const { data } = await getChangePassword()
if (data.value?.code == 1) {
    state.allowSetPassword = data.value.data.allowSetPassword
}

const onSubmit = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.validate((valid) => {
        if (valid) {
            state.formSubmitLoading = true
            postChangePassword({ ...state.form })
                .then((res) => {
                    state.formSubmitLoading = false
                    if (res.code == 1) {
                        if (res.data.allowSetPassword) {
                            setTimeout(() => {
                                router.go(0)
                            }, 3000)
                        } else {
                            userInfo.logout()
                        }
                    }
                })
                .finally(() => {
                    state.formSubmitLoading = false
                })
        }
    })
}
</script>

<style scoped lang="scss">
.change-password {
    width: 360px;
    max-width: 100%;
}
.submit-buttons :deep(.el-form-item__content) {
    justify-content: flex-end;
}
.set-password-alert {
    margin-bottom: 10px;
}
</style>
