const controllerUrl = '/api/OAuthLogin/'

export function loginAgent(code: string, state: string) {
    return Http.fetch(
        {
            url: controllerUrl + 'loginAgent',
            method: 'get',
            params: {
                code: code,
                state: state,
            },
        },
        {
            loading: true,
        }
    )
}

export function oauthList() {
    return Http.fetch({
        url: controllerUrl + 'oauthList',
        method: 'get',
    })
}

export function bindList() {
    return Http.fetch({
        url: controllerUrl + 'bindList',
        method: 'get',
    })
}

export function getChangePassword() {
    return Http.fetch({
        url: controllerUrl + 'changePassword',
        method: 'GET',
    })
}

export function unbind(data: anyObj) {
    return Http.$fetch(
        {
            url: controllerUrl + 'unbind',
            method: 'post',
            body: data,
        },
        {
            showSuccessMessage: true,
        }
    )
}

export function postChangePassword(params: anyObj) {
    return Http.$fetch(
        {
            url: controllerUrl + 'changePassword',
            method: 'POST',
            body: params,
        },
        {
            showSuccessMessage: true,
        }
    )
}
