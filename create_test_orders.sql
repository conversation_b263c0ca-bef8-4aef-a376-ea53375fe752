-- 为用户ID为1创建测试订单数据
-- 当前时间戳
SET @current_time = UNIX_TIMESTAMP();

-- 1. 待付款订单 (pending)
INSERT INTO ba_order (
    order_no, user_id, service_id, service_title, service_desc, cover_image,
    contact_mobile, contact_email, contact_qq, contact_wechat,
    service_price, discount_amount, total_amount, status, payment_status,
    user_remark, expire_time, create_time, update_time
) VALUES (
    'ORD202412160001', 1, 1, '测试学术服务', '这是一个测试学术服务的详细描述', '/static/uploads/cover1.jpg',
    '13800138000', '<EMAIL>', '123456789', 'test_wechat',
    299.00, 0.00, 299.00, 'pending', 'unpaid',
    '希望尽快完成这个学术服务', @current_time + 1800, @current_time - 86400, @current_time
);

-- 2. 另一个待付款订单 (pending)
INSERT INTO ba_order (
    order_no, user_id, service_id, service_title, service_desc, cover_image,
    contact_mobile, contact_email, contact_qq, contact_wechat,
    service_price, discount_amount, total_amount, status, payment_status,
    user_remark, expire_time, create_time, update_time
) VALUES (
    'ORD202412160002', 1, 2, '测试成果转换11', '成果转换服务描述', '/static/uploads/cover2.jpg',
    '13800138000', '<EMAIL>', '123456789', 'test_wechat',
    208.00, 8.00, 200.00, 'pending', 'unpaid',
    '需要专业的成果转换服务', @current_time + 1800, @current_time - 7200, @current_time
);

-- 3. 已付款订单 (paid)
INSERT INTO ba_order (
    order_no, user_id, service_id, service_title, service_desc, cover_image,
    contact_mobile, contact_email, contact_qq, contact_wechat,
    service_price, discount_amount, total_amount, status, payment_status,
    payment_method, trade_no, out_trade_no, payment_time, payment_data,
    user_remark, create_time, update_time
) VALUES (
    'ORD202412160003', 1, 3, '测试成果转换', '高质量的成果转换服务', '/static/uploads/cover3.jpg',
    '13800138000', '<EMAIL>', '123456789', 'test_wechat',
    1888.00, 88.00, 1800.00, 'paid', 'paid',
    'wechat', 'wx_20241216001', 'ORD202412160003', @current_time - 172800, '{"transaction_id":"wx_20241216001","trade_type":"NATIVE"}',
    '需要高质量的成果转换', @current_time - 259200, @current_time - 172800
);

-- 4. 进行中订单 (in_progress)
INSERT INTO ba_order (
    order_no, user_id, service_id, service_title, service_desc, cover_image,
    contact_mobile, contact_email, contact_qq, contact_wechat,
    service_price, discount_amount, total_amount, status, payment_status,
    payment_method, trade_no, out_trade_no, payment_time, payment_data,
    user_remark, admin_remark, create_time, update_time
) VALUES (
    'ORD202412160004', 1, 4, '论文写作的测试', '专业论文写作服务', '/static/uploads/cover4.jpg',
    '13800138000', '<EMAIL>', '123456789', 'test_wechat',
    1899.00, 99.00, 1800.00, 'in_progress', 'paid',
    'alipay', '2024121600001', 'ORD202412160004', @current_time - 345600, '{"trade_no":"2024121600001","buyer_id":"2088123456789"}',
    '需要高质量的论文写作服务', '服务已开始，预计15天完成', @current_time - 432000, @current_time - 86400
);

-- 5. 已完成订单 (completed)
INSERT INTO ba_order (
    order_no, user_id, service_id, service_title, service_desc, cover_image,
    contact_mobile, contact_email, contact_qq, contact_wechat,
    service_price, discount_amount, total_amount, status, payment_status,
    payment_method, trade_no, out_trade_no, payment_time, payment_data,
    user_remark, admin_remark, create_time, update_time
) VALUES (
    'ORD202412160005', 1, 5, '论文写作', '优质论文写作服务', '/static/uploads/cover5.jpg',
    '13800138000', '<EMAIL>', '123456789', 'test_wechat',
    1888.00, 0.00, 1888.00, 'completed', 'paid',
    'wechat', 'wx_20241210001', 'ORD202412160005', @current_time - 604800, '{"transaction_id":"wx_20241210001","trade_type":"NATIVE"}',
    '论文写作服务需求', '服务已完成，客户满意', @current_time - 1209600, @current_time - 172800
);

-- 6. 已取消订单 (cancelled)
INSERT INTO ba_order (
    order_no, user_id, service_id, service_title, service_desc, cover_image,
    contact_mobile, contact_email, contact_qq, contact_wechat,
    service_price, discount_amount, total_amount, status, payment_status,
    user_remark, admin_remark, create_time, update_time
) VALUES (
    'ORD202412160006', 1, 1, '测试学术服务', '学术服务测试', '/static/uploads/cover1.jpg',
    '13800138000', '<EMAIL>', '123456789', 'test_wechat',
    299.00, 0.00, 299.00, 'cancelled', 'unpaid',
    '暂时不需要这个服务了', '用户取消：暂时不需要这个服务了', @current_time - 518400, @current_time - 432000
);

-- 7. 已退款订单 (refunded)
INSERT INTO ba_order (
    order_no, user_id, service_id, service_title, service_desc, cover_image,
    contact_mobile, contact_email, contact_qq, contact_wechat,
    service_price, discount_amount, total_amount, status, payment_status,
    payment_method, trade_no, out_trade_no, payment_time, payment_data,
    user_remark, admin_remark, create_time, update_time
) VALUES (
    'ORD202412160007', 1, 3, '测试成果转换', '成果转换服务', '/static/uploads/cover3.jpg',
    '13800138000', '<EMAIL>', '123456789', 'test_wechat',
    1888.00, 88.00, 1800.00, 'refunded', 'refunded',
    'wechat', 'wx_20241205001', 'ORD202412160007', @current_time - 950400, '{"transaction_id":"wx_20241205001","trade_type":"NATIVE"}',
    '对服务质量不满意', '已同意退款申请，退款已处理', @current_time - 1036800, @current_time - 345600
);

-- 8. 另一个已完成订单 (completed)
INSERT INTO ba_order (
    order_no, user_id, service_id, service_title, service_desc, cover_image,
    contact_mobile, contact_email, contact_qq, contact_wechat,
    service_price, discount_amount, total_amount, status, payment_status,
    payment_method, trade_no, out_trade_no, payment_time, payment_data,
    user_remark, admin_remark, create_time, update_time
) VALUES (
    'ORD202412160008', 1, 2, '测试成果转换11', '成果转换专业服务', '/static/uploads/cover2.jpg',
    '13800138000', '<EMAIL>', '123456789', 'test_wechat',
    208.00, 8.00, 200.00, 'completed', 'paid',
    'alipay', '2024120500001', 'ORD202412160008', @current_time - 1209600, '{"trade_no":"2024120500001","buyer_id":"2088123456789"}',
    '成果转换服务需求', '服务已完成，质量优秀', @current_time - 1296000, @current_time - 432000
);

-- 输出插入结果
SELECT '测试订单数据插入完成' as result;
SELECT COUNT(*) as total_orders FROM ba_order WHERE user_id = 1;
SELECT status, COUNT(*) as count FROM ba_order WHERE user_id = 1 GROUP BY status; 