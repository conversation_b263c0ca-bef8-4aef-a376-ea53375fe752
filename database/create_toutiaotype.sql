-- 创建头条分类表（如果不存在）
CREATE TABLE IF NOT EXISTS `toutiaotype` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '头条分类名称',
  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序权重,数字越大越靠前',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态:0=禁用,1=启用',
  `create_time` int(10) unsigned DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='头条分类表';

-- 插入一些默认数据（如果不存在）
INSERT IGNORE INTO `toutiaotype` (`id`, `name`, `sort`, `status`, `create_time`, `update_time`) VALUES
(1, '会议快讯', 100, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, '科研工具', 90, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, '学术干货', 80, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, '前沿快讯', 70, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()); 