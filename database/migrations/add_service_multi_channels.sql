-- 为学术服务添加多选频道功能
-- 添加专门用于学术服务多选频道的字段，不影响其他模型的单选频道

-- 添加学术服务多选频道字段
ALTER TABLE `ba_content` ADD COLUMN `service_channel_ids` TEXT NULL COMMENT '学术服务所属频道ID，多选，以逗号分隔' AFTER `channel_id`;

-- 数据迁移：将现有学术服务的单选频道迁移到多选字段
UPDATE `ba_content` 
SET `service_channel_ids` = CAST(`channel_id` AS CHAR) 
WHERE `content_type` = 'SERVICE' AND `channel_id` IS NOT NULL;

-- 添加索引提高查询效率
ALTER TABLE `ba_content` ADD INDEX `idx_service_channel_ids` (`service_channel_ids`(255)); 