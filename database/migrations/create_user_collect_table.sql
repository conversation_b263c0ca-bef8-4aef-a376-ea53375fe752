-- 创建用户收藏表
-- 执行时间：2025-01-06

-- 创建用户收藏表
CREATE TABLE IF NOT EXISTS `ba_user_collect` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `content_id` int(10) unsigned NOT NULL COMMENT '内容ID',
  `content_type` enum('SERVICE','EXPERT','JOURNAL','HEADLINE') NOT NULL COMMENT '内容类型：SERVICE=学术服务,EXPERT=专家智库,JOURNAL=学术期刊,HEADLINE=学术头条',
  `create_time` int(10) unsigned NOT NULL COMMENT '收藏时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_content_unique` (`user_id`, `content_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_content_id` (`content_id`),
  KEY `idx_content_type` (`content_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表'; 