-- 删除已存在的订单相关表（如果存在）
DROP TABLE IF EXISTS `ba_order_refund`;
DROP TABLE IF EXISTS `ba_order_review`;
DROP TABLE IF EXISTS `ba_order_status_log`;
DROP TABLE IF EXISTS `ba_order_item`;
DROP TABLE IF EXISTS `ba_order`;

-- 订单主表
CREATE TABLE `ba_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单编号',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `service_id` int(10) unsigned NOT NULL COMMENT '购买的服务ID（关联ba_content表）',
  `service_title` varchar(255) NOT NULL COMMENT '服务标题',
  `service_desc` text COMMENT '服务描述',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '服务封面图片',
  
  -- 用户联系信息
  `contact_mobile` varchar(20) DEFAULT NULL COMMENT '联系手机号',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `contact_qq` varchar(20) DEFAULT NULL COMMENT '联系QQ',
  `contact_wechat` varchar(50) DEFAULT NULL COMMENT '联系微信',
  
  -- 价格相关
  `service_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '服务单价',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  
  -- 订单状态
  `status` enum('pending','paid','in_progress','completed','cancelled','refunded') NOT NULL DEFAULT 'pending' COMMENT '订单状态：pending=待付款,paid=已付款,in_progress=进行中,completed=已完成,cancelled=已取消,refunded=已退款',
  `payment_status` enum('unpaid','paying','paid','failed','refunded') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态：unpaid=未支付,paying=支付中,paid=已支付,failed=支付失败,refunded=已退款',
  
  -- 支付相关（配合BuildAdmin支付模块）
  `payment_method` enum('wechat','alipay','balance','other') DEFAULT NULL COMMENT '支付方式：wechat=微信支付,alipay=支付宝,balance=余额支付,other=其他方式',
  `trade_no` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `out_trade_no` varchar(64) DEFAULT NULL COMMENT '商户订单号（支付模块使用）',
  `payment_time` int(10) unsigned DEFAULT NULL COMMENT '支付时间',
  `payment_data` text COMMENT '支付回调数据（JSON格式）',
  
  -- 订单备注
  `user_remark` text COMMENT '用户备注（购买时填写的需求说明）',
  `admin_remark` text COMMENT '管理员备注',
  
  -- 时间记录
  `expire_time` int(10) unsigned DEFAULT NULL COMMENT '订单过期时间（未支付订单30分钟过期）',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  `delete_time` int(10) unsigned DEFAULT NULL COMMENT '删除时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  UNIQUE KEY `out_trade_no` (`out_trade_no`),
  KEY `user_id` (`user_id`),
  KEY `service_id` (`service_id`),
  KEY `status` (`status`),
  KEY `payment_status` (`payment_status`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单主表';

-- 订单状态变更记录表
CREATE TABLE `ba_order_status_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_id` int(10) unsigned NOT NULL COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单编号',
  `from_status` varchar(20) DEFAULT NULL COMMENT '原状态',
  `to_status` varchar(20) NOT NULL COMMENT '新状态',
  `operate_type` enum('user','admin','system','payment') NOT NULL DEFAULT 'system' COMMENT '操作类型：user=用户操作,admin=管理员操作,system=系统操作,payment=支付回调',
  `operator_id` int(10) unsigned DEFAULT NULL COMMENT '操作者ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作者名称',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `order_no` (`order_no`),
  KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单状态变更记录表';



-- 订单评价表
CREATE TABLE `ba_order_review` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_id` int(10) unsigned NOT NULL COMMENT '订单ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `service_id` int(10) unsigned NOT NULL COMMENT '服务ID',
  `rating` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '评分：1-5星',
  `content` text COMMENT '评价内容',
  `images` text COMMENT '评价图片（JSON数组）',
  `reply_content` text COMMENT '商家回复',
  `reply_time` int(10) unsigned DEFAULT NULL COMMENT '回复时间',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态：normal=正常显示,hidden=隐藏不显示',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `service_id` (`service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单评价表';

-- 售后/退款申请表
CREATE TABLE `ba_order_refund` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_id` int(10) unsigned NOT NULL COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单编号',
  `user_id` int(10) unsigned NOT NULL COMMENT '申请用户ID',
  `refund_no` varchar(32) NOT NULL COMMENT '退款单号',
  `refund_type` enum('refund_only','return_refund','exchange') NOT NULL COMMENT '申请类型：refund_only=仅退款,return_refund=退货退款,exchange=换货',
  `refund_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '申请退款金额',
  `reason` varchar(500) NOT NULL COMMENT '申请原因',
  `description` text COMMENT '详细说明',
  `evidence_images` text COMMENT '凭证图片（JSON数组）',
  
  `status` enum('pending','approved','rejected','processing','completed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '处理状态：pending=待处理,approved=已同意,rejected=已拒绝,processing=处理中,completed=已完成,cancelled=已取消',
  `admin_remark` text COMMENT '管理员备注',
  `process_time` int(10) unsigned DEFAULT NULL COMMENT '处理时间',
  `completion_time` int(10) unsigned DEFAULT NULL COMMENT '完成时间',
  
  -- 退款相关
  `actual_refund_amount` decimal(10,2) DEFAULT NULL COMMENT '实际退款金额',
  `refund_method` varchar(50) DEFAULT NULL COMMENT '退款方式',
  `refund_transaction_id` varchar(100) DEFAULT NULL COMMENT '第三方支付平台退款交易号（微信refund_id/支付宝流水号）',
  `refund_time` int(10) unsigned DEFAULT NULL COMMENT '退款时间',
  
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `refund_no` (`refund_no`),
  KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='售后退款申请表'; 