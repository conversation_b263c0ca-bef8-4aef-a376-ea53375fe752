# 🚀 快速测试脚本

## 📋 立即验证修改效果

### 1. 检查支付配置状态

```bash
# 替换为您的实际Token
TOKEN="your_token_here"

# 检查当前支付配置
curl -X POST "https://yanzhi.dlxingyun.com/api/User/checkPayConfig" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}"
```

**预期结果：**
- 📊 显示详细的配置状态
- 📈 显示配置完成度百分比  
- ⚠️ 明确列出缺少的配置项
- 🎯 提供具体的配置建议

### 2. 测试创建订单（验证错误提示）

```bash
# 测试创建订单 - 应该会收到明确的配置错误提示
curl -X POST "https://yanzhi.dlxingyun.com/api/User/createOrderTest" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "service_id": 1,
    "quantity": 1,
    "contact_mobile": "13800138000",
    "contact_email": "<EMAIL>",
    "user_remark": "配置测试订单"
  }'
```

**预期结果：**
```json
{
  "code": 0,
  "msg": "创建订单失败：微信支付配置不完整，缺少以下配置：\nApp的AppID(app_id) - 扫码支付必需",
  "data": null
}
```

### 3. 完整测试流程（一键版）

```bash
#!/bin/bash

# 配置
TOKEN="your_token_here"
BASE_URL="https://yanzhi.dlxingyun.com"

echo "🚀 开始快速验证支付配置..."

# 1. 检查配置
echo -e "\n📊 1. 检查支付配置状态..."
CONFIG_RESULT=$(curl -s -X POST "${BASE_URL}/api/User/checkPayConfig" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}")

echo "配置检查结果:"
echo $CONFIG_RESULT | jq '.'

# 提取关键信息
COMPLETION_RATE=$(echo $CONFIG_RESULT | jq -r '.data.completion_rate // "未知"')
IS_READY=$(echo $CONFIG_RESULT | jq -r '.data.is_ready // false')
PAYMENT_MODE=$(echo $CONFIG_RESULT | jq -r '.data.payment_mode // "未知"')

echo -e "\n📈 配置完成度: $COMPLETION_RATE"
echo "🔧 支付模式: $PAYMENT_MODE"
echo "✅ 是否就绪: $IS_READY"

if [ "$IS_READY" = "false" ]; then
    echo -e "\n⚠️ 缺少以下配置:"
    echo $CONFIG_RESULT | jq -r '.data.missing_configs[]' | sed 's/^/  - /'
fi

# 2. 测试创建订单
echo -e "\n🛒 2. 测试创建订单..."
ORDER_RESULT=$(curl -s -X POST "${BASE_URL}/api/User/createOrderTest" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "service_id": 1,
    "quantity": 1,
    "contact_mobile": "13800138000",
    "contact_email": "<EMAIL>",
    "user_remark": "快速测试订单"
  }')

echo "订单创建结果:"
echo $ORDER_RESULT | jq '.'

# 判断结果
ORDER_CODE=$(echo $ORDER_RESULT | jq -r '.code // 0')
if [ "$ORDER_CODE" = "1" ]; then
    echo -e "\n✅ 订单创建成功！支付配置正常"
    QR_CODE=$(echo $ORDER_RESULT | jq -r '.data.qr_code // ""')
    PAYMENT_MODE_RESULT=$(echo $ORDER_RESULT | jq -r '.data.payment_mode // ""')
    echo "🔗 二维码: $QR_CODE"
    echo "🎯 支付模式: $PAYMENT_MODE_RESULT"
else
    echo -e "\n❌ 订单创建失败，原因如预期："
    ERROR_MSG=$(echo $ORDER_RESULT | jq -r '.msg // "未知错误"')
    echo "📝 错误信息: $ERROR_MSG"
fi

echo -e "\n🎉 快速验证完成！"
```

## 🔍 验证要点

### ✅ 成功修改的特征

1. **智能配置检查**
   - 🔍 详细列出所有配置项状态
   - 📊 显示配置完成度百分比
   - 🎯 明确指出缺少的配置

2. **精确错误提示**
   - ❌ 不再返回硬编码的模拟数据
   - 📝 明确说明缺少哪些配置
   - 🧭 提供具体的配置指导

3. **模式识别**
   - 🔧 正确识别沙箱模式、正常模式等
   - 📋 在返回数据中包含支付模式信息
   - ⚡ 根据配置完整性决定下一步操作

### ❌ 修改前的问题

1. **硬返回模拟数据**
   - 😤 即使在沙箱模式也返回假的二维码
   - 🚫 不尝试调用真实的微信支付接口

2. **模糊错误信息**
   - 😕 只说"需要配置app_id"
   - 🤷 不知道具体缺少哪些配置

3. **测试困难**
   - 🔍 难以判断是配置问题还是代码问题
   - 🧪 无法有效测试沙箱环境

## 📝 使用说明

1. **替换Token**：将 `your_token_here` 替换为您的实际登录Token
2. **运行检查**：先运行配置检查，了解当前状态
3. **测试订单**：尝试创建订单，验证错误提示
4. **对比结果**：与修改前的表现进行对比

## 🎯 下一步操作

根据测试结果：

1. **如果收到配置错误** ✅
   - 说明修改成功
   - 按提示配置缺少的AppID

2. **如果仍然返回模拟数据** ❌
   - 检查代码是否正确部署
   - 验证配置文件是否生效

3. **配置AppID后** 🚀
   - 将调用真实的微信支付接口
   - 在沙箱模式下进行安全测试 